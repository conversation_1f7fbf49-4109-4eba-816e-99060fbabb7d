within StateMachine;
record CrkABCDVICDEcoCD_10
  import BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod;
  extends Mode30XBVBase(
    final modeID=ModeID30XBV.CrkABCDVICDEcoCD_10,
    final CrkA=true,
    final CrkB=true,
    final CrkC=true,
    final CrkD=true,
    final EcoA=false,
    final EcoB=false,
    final EcoC=true,
    final EcoD=true,
    final ViA=false,
    final ViB=false,
    final ViC=true,
    final ViD=true,
    initMethod=ModeInitMethod.External);
end CrkABCDVICDEcoCD_10;
