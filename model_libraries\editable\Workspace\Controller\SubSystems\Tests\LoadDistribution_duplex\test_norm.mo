within Workspace.Controller.SubSystems.Tests.LoadDistribution_duplex;
model test_norm
  .Workspace.Controller.SubSystems.BaseClasses.LoadDistribution_DesignCapa_duplex loadDistribution_DesignCapa_duplex(
    capacityDesign_moduleB_max=900000)
    annotation (Placement(transformation(extent={{-29.89206769841219,-115.89206769841219},{69.89206769841219,-16.10793230158781}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator actuator(
    maxBound=1000000,
    minBound=0,
    setPoint=800000)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={-84.0,-66.0},rotation=-180.0)));
equation
  connect(actuator.value,loadDistribution_DesignCapa_duplex.ControllerSignal)
    annotation (Line(points={{-79,-66},{-29.79228356301536,-65.80043172920635}},color={0,0,127}));
  annotation (
    Icon(
      graphics={
        Rectangle(
          origin={-1,41},
          extent={{-79,57},{79,-57}},
          fillPattern=FillPattern.Solid,
          fillColor={98,27,27})}));
end test_norm;
