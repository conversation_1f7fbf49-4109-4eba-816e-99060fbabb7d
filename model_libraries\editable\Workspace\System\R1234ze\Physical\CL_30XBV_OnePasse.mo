within Workspace.System.R1234ze.Physical;
model CL_30XBV_OnePasse
  extends.Workspace.Controller.CL_XBV_System(
    controller(
      crkA_isOff=ModuleA.isOffA,
      crkB_isOff=ModuleA.isOffB,
      ecoA_isOff=ModuleA.isOffECOA,
      ecoB_isOff=ModuleA.isOffECOB,
      crkA_min_speed=ModuleA.NcompAMin,
      crkB_min_speed=ModuleA.NcompBMin,
      crkA_max_speed=ModuleA.NcompAMax,
      crkB_max_speed=ModuleA.NcompBMax,
      isOffSDTmin_fan=true,
      isOffDGTmax_fan=true,
      isOffSSTmin_EXV=true,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=true,
      isOffDGTmax_EXV=true,
      isOffSSTmin_comp=true,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=true,
      MaxFrequency=
        if(choiceBlock.is60HZ and ModuleA.is_fixedSpeed) then
          60
        else
          50,
      isOffSDTmax_fan=false),
    UseStateMachine=true,
    controllerSettings_crkA(
      capacity_setpoint=
        if use_ECATBlock then
          ECAT.TargetCoolingCapacity_W.setPoint
        else
          TargetCapacity,
      compressorFrequency_min=ModuleA.NcompAMin,
      compressorFrequency_max=ModuleA.NcompAMax,
      T_sst_min_limit_comp=273.15,
      nbrCoils=ModuleA.n_coilsA,
      FanFrequency_max=
        if(choiceBlock.is60HZ and ModuleA.is_fixedSpeed) then
          60
        else
          50,
      compressor=ModuleA.CompType_CKA,
      isHighAmbientOption=choiceBlock.isHighAmbiant,
      dT_sbc_max=8,
      refrigerant=Workspace.Controller.Components.Types.RefrigerantSelector.R1234ze),
    controllerSettings_crkB(
      capacity_setpoint=
        if use_ECATBlock then
          ECAT.TargetCoolingCapacity_W.setPoint
        else
          TargetCapacity,
      compressorFrequency_min=ModuleA.NcompBMin,
      compressorFrequency_max=ModuleA.NcompBMax,
      machineError_Temperature=0.1,
      nbrCoils=ModuleA.n_coilsB,
      FanFrequency_max=
        if(choiceBlock.is60HZ and ModuleA.is_fixedSpeed) then
          60
        else
          50,
      compressor=ModuleA.CompType_CKB,
      isHighAmbientOption=choiceBlock.isHighAmbiant,
      dT_sbc_max=8,
      refrigerant=Workspace.Controller.Components.Types.RefrigerantSelector.R1234ze));
  .Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.ChoiceBlock choiceBlock(
    Selector_ModuleA=Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.Selector.Unit_30XBV_1450_R1234ze)
    annotation (Placement(transformation(extent={{-83.57546614020706,106.42453385979289},{-52.424533859792945,137.5754661402071}},origin={0.0,0.0},rotation=0.0)));
  parameter.Modelica.SIunits.Temperature OAT=35+273.15
    annotation (Dialog(group="Boundary Conditions"));
  parameter.Modelica.SIunits.Temperature EWT=12+273.15
    annotation (Dialog(group="Boundary Conditions"));
  parameter.Modelica.SIunits.Temperature LWT=7+273.15
    annotation (Dialog(group="Boundary Conditions"));
  parameter Real TargetCapacity=2000000
    annotation (Dialog(group="Boundary Conditions"));
  parameter Real Vd_coolant=0.015
    annotation (Dialog(group="Boundary Conditions"));
  parameter Boolean isEWT_Fixed=true
    annotation (Dialog(group="Boundary Conditions"));
  parameter Boolean isLWT_Fixed=true
    annotation (Dialog(group="Boundary Conditions"));
  parameter Boolean isVd_Fixed=false
    annotation (Dialog(group="Boundary Conditions"));
  parameter Boolean isOffA=not StateMachine.currentMode.CrkA
    "set true to turn off circuit A"
    annotation (Dialog(tab="isOff"));
  parameter Boolean isOffB=not StateMachine.currentMode.CrkB
    "set true to turn off circuit B"
    annotation (Dialog(tab="isOff"));
  parameter Boolean isEcoAOff=not StateMachine.currentMode.EcoA
    "set true to turn off eco line in circuit A"
    annotation (Dialog(tab="isOff"));
  parameter Boolean isEcoBOff=not StateMachine.currentMode.EcoB
    "set true to turn off eco line in circuit B"
    annotation (Dialog(tab="isOff"));
  parameter Boolean Vi_A=StateMachine.currentMode.ViA
    "set true to turn on circuit A VI"
    annotation (Dialog(tab="isOff"));
  parameter Boolean Vi_B=StateMachine.currentMode.ViB
    "set true to turn off circuit B VI"
    annotation (Dialog(tab="isOff"));
  parameter Real ECO_ON_inter=139.5
    "Value of the OAT max to turn on the ECO at 273.15 K for LWT"
    annotation (Dialog(tab="isOff"));
  parameter Real ECO_ON_slope=0.523
    "Slope for the boundary to turn off the ECO according to the OAT and the LWT"
    annotation (Dialog(tab="isOff"));
  parameter Boolean use_bf=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean Use_EN14511=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean use_Calib=true
    annotation (Dialog(group="Use Parameters"));
  parameter Boolean use_ECATBlock=true
    annotation (Dialog(group="Use Parameters"));
  output Real VI_A;
  output Real VI_B;
  import SI=Modelica.SIunits;
  import Modelica.SIunits.Conversions .*;
  .Workspace.Auxiliary.ECAT_Block.ECAT30XBVBase ECAT(
    RefrigerantCharge_kg(
      fixed={false,false},
      value=ModuleA.systemVariables.mRef),
    CondCoilAirPressDrop_Pa(
      value={ModuleA.condAirA.summary.dp_air,ModuleA.condAirB.summary.dp_air}),
    RefrigerantSST_K(
      value={ModuleA.nodeCpinA.Tsat,ModuleA.nodeCpinB.Tsat}),
    RefrigerantSDT_K(
      value={ModuleA.nodeCpoutA.Tsat,ModuleA.nodeCpoutB.Tsat}),
    RefrigerantSET_K(
      value={ModuleA.nodeevapoutA.Tsat,ModuleA.nodeevapoutB.Tsat}),
    RefrigerantSCT_K(
      value={ModuleA.nodecondAirinA.Tsat,ModuleA.nodecondAirinB.Tsat}),
    RefrigerantDGT_K(
      value={ModuleA.nodeCpoutA.T,ModuleA.nodeCpoutB.T}),
    SuctionSuperheat_K(
      value={ModuleA.nodeCpinA.dTsh,ModuleA.nodeCpinB.dTsh}),
    CondSubcooling_K(
      value={ModuleA.nodecondAiroutA.dTsh,ModuleA.nodecondAiroutB.dTsh}),
    DischargeSuperheat_K(
      value={ModuleA.nodeCpoutA.dTsh,ModuleA.nodeCpoutB.dTsh}),
    CompressorFrequency_Hz(
      setPoint={1.,1},
      value={ModuleA.CompressorA.summary.Ncomp,ModuleA.CompressorB.summary.Ncomp}),
    CondFanAirflowRate_m3s(
      value={ModuleA.sourceAirA.Vd_flow*(ModuleA.n_coilsA),ModuleA.sourceAirB.Vd_flow*(ModuleA.n_coilsB)}),
    CompressorPower_W(
      value={ModuleA.CompressorA.summary.P_compression,ModuleA.CompressorB.summary.P_compression}),
    FanPower_W(
      value={ModuleA.motorA.computeStreamsAggregates.pow,ModuleA.motorB.computeStreamsAggregates.pow}),
    Altitude_m(
      setPoint=0),
    AmbientAirDBTemp_K(
      setPoint=35+273.15,
      value=ModuleA.sourceAirA.Tdb),
    AmbientAirRH_nd(
      setPoint=0.6,
      value=ModuleA.sourceAirA.RH),
    CondBrineConcentration_nd(
      setPoint=1),
    CondBrineLWT_K(
      value=1),
    CondBrineEWT_K(
      value=1.),
    CondBrineFlowRate_m3s(
      value=0),
    CondFoulingFactor_m2KW(
      setPoint=0),
    EvapBrineConcentration_nd(
      setPoint=0),
    EvapBrineLWT_K(
      value=sink.summary.T,
      setPoint=7+273.15),
    EvapBrineEWT_K(
      value=
        if ECAT.EvapBrineEWT_K.fixed then
          source.summary.T
        else
          ModuleA.businessFactors.bf_cap_interp*source.summary.T+(1-ModuleA.businessFactors.bf_cap_interp)*sink.summary.T,
      setPoint=12+273.15),
    EvapBrineFlowRate_m3s(
      value=
        if ECAT.EvapBrineFlowRate_m3s.fixed then
          source.summary.Vd
        else
          source.summary.Vd*ModuleA.businessFactors.bf_cap_interp,
      setPoint=0.015),
    TargetCoolingCapacity_W(
      setPoint=2000000),
    TargetHeatingCapacity_W(
      setPoint=1),
    TotalRefrigerantCharge_kg(
      value=ModuleA.systemVariables.summary.mRef[1]+ModuleA.systemVariables.summary.mRef[2]),
    TotalOilCharge_kg(
      value=-1),
    EvapPumpSpeed_rpm(
      value=-1),
    PubUnitPower_W(
      value=ModuleA.controlledPower),
    PubCoolingCapacity_W(
      value=ModuleA.controlledCapacity),
    PubHeatingCapacity_W(
      value=1),
    TotalCompressorPower_W(
      value=ModuleA.CompressorA.summary.P_compression+ModuleA.CompressorB.summary.P_compression),
    TotalFanPower_W(
      value=sum(
        ECAT.FanPower_W.value)),
    EvapPumpPower_W(
      value=-1),
    CondPumpPower_W(
      value=-1),
    CondPumpSpeed_rpm(
      value=-1,
      fixed=false),
    EvapBrineIntPressDrop_Pa(
      value=ModuleA.evaporator.summary.dp_coolant),
    EvapPumpTotalHead_m(
      value=-1),
    EvapBrineDensity_kgm3(
      value=1/sink.summary.v),
    CondBrineIntPressDrop_Pa(
      value=-1),
    CondPumpTotalHead_m(
      value=-1),
    CondBrineDensity_kgm3(
      value=-1),
    EvapBrineVelocity_mps(
      value=-1),
    CondBrineVelocity_mps(
      value=-1),
    StageNum_nd(
      value=1.1,
      fixed=false),
    redeclare replaceable.BOLT.InternalLibrary.ECATBlock.TableGenOutputs extraSettings,
    HeatingAmbientAirDBTemp_K(
      setPoint=1.05),
    EN14511PumpPowerCorrectionWithPump_W(
      value=ModuleA.en14511.M_pump),
    CondEN14511PumpPowerCorrectionWithPump_W(
      value=1),
    EN14511PumpPowerCorrectionWithoutPump_W(
      value=ModuleA.en14511.M),
    CondEN14511PumpPowerCorrectionWithoutPump_W(
      value=1),
    ExternalSystemKa_kPas2L2(
      value=-1,
      fixed=false),
    CondExternalSystemKa_kPas2L2(
      value=1,
      fixed=false),
    CondExternalSystemPressureDrop_Pa(
      value=1,
      fixed=false),
    IsMinimalCapacity(
      value=
        if controller.completeCompressorControl_base.capacity_controller.summary.ID ==-3 then
          1
        else
          0),
    IsCoolingMode(
      value=1),
    PubHeatingCapacityInstantaneous_W(
      value=1),
    HeatingAmbientAirWBTemp_K(
      value=-1),
    CondHighStaticFanExtPressDrop_Pa(
      value=1),
    EvapHighStaticFanExtPressDrop_Pa(
      value=1.1),
    HighStaticFanExternalKa_kPas2L2(
      value={1.1,1},
      fixed={false,false}),
    CondCoilHeatingCapacity_W(
      value={ModuleA.condAirA.summary.Q_flow_air,ModuleA.condAirB.summary.Q_flow_air}),
    CoolantFreezingTemp_K(
      value=ModuleA.FreezTemp),
    RefrigerantType_nd=BOLT.InternalLibrary.ECATBlock.Types.RefrigerantTypes.R1234ze,
    EvapFoulingFactor_m2KW(
      setPoint=0,
      value=ModuleA.evaporator.R_foul),
    nbrCircuit=2,
    ExternalSystemPressureDrop_Pa(
      fixed=false))
    annotation (Placement(transformation(extent={{-42.864038283143586,107.1359617168564},{-13.135961716856414,136.8640382831436}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.Records.summary_system summary_system(
    EER=ModuleA.systemVariables.summary.COPCool,
    Cooling_capacity=ModuleA.systemVariables.summary.CoolCap_total,
    OAT=ModuleA.sourceAirA.summary.Tdb-273.15,
    EWT=source.summary.T-273.15,
    LWT=ECAT.EvapBrineLWT_K.setPoint-273.15,
    SST_A=ModuleA.nodeCpinA.summary.Tsat-273.15,
    SST_B=ModuleA.nodeCpinB.summary.Tsat-273.15,
    SST_C=0,
    SST_D=0,
    SDT_A=ModuleA.nodeCpoutA.summary.Tsat-273.15,
    SDT_B=ModuleA.nodeCpoutB.summary.Tsat-273.15,
    SDT_C=0,
    SDT_D=0,
    SET_A=ModuleA.nodeevapoutA.summary.Tsat-273.15,
    SET_B=ModuleA.nodeevapoutB.summary.Tsat-273.15,
    SET_C=0,
    SET_D=0,
    SCT_A=ModuleA.nodecondAirinA.summary.Tsat-273.15,
    SCT_B=ModuleA.nodecondAirinB.summary.Tsat-273.15,
    SCT_C=0,
    SCT_D=0,
    DGT_A=ModuleA.nodeCpoutA.summary.T-273.15,
    DGT_B=ModuleA.nodeCpoutB.summary.T-273.15,
    DGT_C=0,
    DGT_D=0,
    Z_U_cond_A=ModuleA.condAirA.summary.Z_U,
    Z_U_cond_B=ModuleA.condAirB.summary.Z_U,
    Z_U_cond_C=0,
    Z_U_cond_D=0,
    Z_cond_dpr_A=ModuleA.condAirA.summary.Z_dpr,
    Z_cond_dpr_B=ModuleA.condAirB.summary.Z_dpr,
    Z_cond_dpr_C=0,
    Z_cond_dpr_D=0,
    Z_U_eco_A=ModuleA.BPHEECOA.summary.ZUA,
    Z_U_eco_B=ModuleA.BPHEECOB.summary.ZUA,
    Z_U_eco_C=0,
    Z_U_eco_D=0,
    Z_U_evap_A=ModuleA.evaporator.summary.Z_Uev1,
    Z_U_evap_B=ModuleA.evaporator.summary.Z_Uev2,
    Z_U_evap_C=0,
    Z_U_evap_D=0,
    Z_dpc_evap=ModuleA.evaporator.summary.Z_dpc,
    Z_flow_comp_A=ModuleA.CompressorA.summary.Z_flow_suc,
    Z_flow_comp_B=ModuleA.CompressorB.summary.Z_flow_suc,
    Z_flow_comp_C=0,
    Z_flow_comp_D=0,
    Z_power_comp_A=ModuleA.CompressorA.summary.Z_power,
    Z_power_comp_B=ModuleA.CompressorB.summary.Z_power,
    Z_power_comp_C=0,
    Z_power_comp_D=0,
    Z_fan_A=ModuleA.motorA.summary.Z_power,
    Z_fan_B=ModuleA.motorB.summary.Z_power,
    Z_fan_C=0,
    Z_fan_D=0,
    Compressor_power_A=ModuleA.CompressorA.summary.P_motor,
    Compressor_power_B=ModuleA.CompressorB.summary.P_motor,
    Compressor_power_C=0,
    Compressor_power_D=0,
    Fan_power_A=ModuleA.motorA.summary.power_motor,
    Fan_power_B=ModuleA.motorB.summary.power_motor,
    Fan_power_C=0,
    Fan_power_D=0,
    Fan_frequency_A=ModuleA.motorA.summary.Motor_freq,
    Fan_frequency_B=ModuleA.motorB.summary.Motor_freq,
    Fan_frequency_C=0,
    Fan_frequency_D=0,
    Compressor_frequency_A=ModuleA.CompressorA.summary.Ncomp,
    Compressor_frequency_B=ModuleA.CompressorB.summary.Ncomp,
    Compressor_frequency_C=0,
    Compressor_frequency_D=0,
    T_in_EXV_A=ModuleA.nodeEXVmaininA.summary.T-273.15,
    T_in_EXV_B=ModuleA.nodeEXVmaininB.summary.T-273.15,
    T_in_EXV_C=0,
    T_in_EXV_D=0,
    Subcooling_A=ModuleA.nodeBPHEECOinA.summary.dTsh,
    Subcooling_B=ModuleA.nodeBPHEECOinB.summary.dTsh,
    Subcooling_C=0,
    Subcooling_D=0,
    Pinch_A=ModuleA.evaporator.summary.LTD1,
    Pinch_B=ModuleA.evaporator.summary.LTD2,
    Pinch_C=0,
    Pinch_D=0,
    Vd_flow_rate=sink.summary.Vd,
    dP_evap=ModuleA.evaporator.summary.dp_coolant)
    annotation (Placement(transformation(extent={{-2.0494470235370983,109.95055297646289},{26.0494470235371,138.0494470235371}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.System.BaseCycle.Equipement_physical ModuleA(
    isOffA=isOffA,
    isOffB=isOffB,
    isOffECOA=isEcoAOff,
    isOffECOB=isEcoBOff,
    n_coilsB=choiceBlock.Unit.nCoil_CKB,
    n_coilsA=choiceBlock.Unit.nCoil_CKA,
    NcompAMax=
      if(choiceBlock.isCoatingOption) then
        choiceBlock.Unit.NcompMax_coating_CKA
      else
        choiceBlock.Unit.NcompMax_CKA,
    NcompAMin=choiceBlock.Unit.NcompMin_CKA,
    NcompBMax=
      if(choiceBlock.isCoatingOption) then
        choiceBlock.Unit.NcompMax_coating_CKB
      else
        choiceBlock.Unit.NcompMax_CKB,
    NcompBMin=choiceBlock.Unit.NcompMin_CKB,
    diameter_in_shell_evap=choiceBlock.Unit.evap_diameter_in_shell,
    n_tubes_passe1_evap=choiceBlock.Unit.evap_n_tubes_pass1,
    n_tubes_passe2_evap=choiceBlock.Unit.evap_n_tubes_pass2,
    n_plate_crkA=choiceBlock.Unit.eco_nPlate_CKA,
    n_plate_crkB=choiceBlock.Unit.eco_nPlate_CKB,
    voltage_crkA=choiceBlock.Unit.CompVoltage_CKA,
    voltage_crkB=choiceBlock.Unit.CompVoltage_CKB,
    Di_oil_crkA=choiceBlock.Unit.OilSepDiameter_CKA,
    Di_oil_crkB=choiceBlock.Unit.OilSepDiameter_CKB,
    length_oil_crkA=choiceBlock.Unit.OilSepLength_CKA,
    length_oil_crkB=choiceBlock.Unit.OilSepLength_CKB,
    dp_ref_oil_crkA=choiceBlock.Unit.Oil_sepA_DP,
    dp_ref_oil_crkB=choiceBlock.Unit.Oil_sepB_DP,
    m_flow_ref_oil_crkA=choiceBlock.Unit.Oil_sepA_MF,
    m_flow_ref_oil_crkB=choiceBlock.Unit.Oil_sepB_MF,
    dp_ref_SL_crkA=choiceBlock.Unit.Suc_lineA_DP,
    dp_ref_SL_crkB=choiceBlock.Unit.Suc_lineB_DP,
    m_flow_ref_SL_crkA=choiceBlock.Unit.Suc_lineA_MF,
    m_flow_ref_SL_crkB=choiceBlock.Unit.Suc_lineB_MF,
    dp_ref_DL_crkA=choiceBlock.Unit.Dis_lineA_DP,
    dp_ref_DL_crkB=choiceBlock.Unit.Dis_lineB_DP,
    m_flow_ref_DL_crkA=choiceBlock.Unit.Dis_lineA_MF,
    m_flow_ref_DL_crkB=choiceBlock.Unit.Dis_lineB_MF,
    Selector_comp_crkA=choiceBlock.Unit.selector_Comp_CKA,
    Selector_comp_crkB=choiceBlock.Unit.selector_Comp_CKB,
    evap_selector_tube=choiceBlock.Unit.evap_selector_tube,
    Eco_Geo_CKA=choiceBlock.Unit.Eco_Geo_CKA,
    Eco_Geo_CKB=choiceBlock.Unit.Eco_Geo_CKB,
    capacity_design=choiceBlock.Unit.Capacity_design,
    is_fixedSpeed=choiceBlock.FAN_SPEED,
    motorA(
      use_shaftSpeed_in=false),
    length_tube_evap_crkA=choiceBlock.Unit.evap_length_tube_CKA,
    length_tube_evap_crkB=choiceBlock.Unit.evap_length_tube_CKB,
    FanA(
      nStage_fixed=true),
    OAT=
      if use_ECATBlock then
        ECAT.AmbientAirDBTemp_K.setPoint
      else
        OAT,
    max_fan_rpm=
      if(choiceBlock.is60HZ) then
        1140
      else
        950,
    max_motor_frequency=
      if(choiceBlock.is60HZ) then
        60
      else
        50,
    limit_max_fan_rpm=
      if(choiceBlock.is60HZ) then
        1140
      else
        950,
    EXV_main_A=choiceBlock.Unit.EXV_main_A,
    EXV_main_B=choiceBlock.Unit.EXV_main_B,
    EXV_eco_A=choiceBlock.Unit.EXV_eco_A,
    EXV_eco_B=choiceBlock.Unit.EXV_eco_B,
    CompType_CKA=choiceBlock.Unit.CompType_CKA,
    CompType_CKB=choiceBlock.Unit.CompType_CKB,
    use_Calib=use_Calib,
    use_bf=use_bf,
    use_en=Use_EN14511,
    eco_DportH_a_A=choiceBlock.Unit.eco_DportH_a_A,
    eco_DportH_b_A=choiceBlock.Unit.eco_DportH_b_A,
    eco_DportL_a_A=choiceBlock.Unit.eco_DportL_a_A,
    eco_DportL_b_A=choiceBlock.Unit.eco_DportL_b_A,
    eco_DportH_a_B=choiceBlock.Unit.eco_DportH_a_B,
    eco_DportH_b_B=choiceBlock.Unit.eco_DportH_b_B,
    eco_DportL_a_B=choiceBlock.Unit.eco_DportL_a_B,
    eco_DportL_b_B=choiceBlock.Unit.eco_DportL_b_B,
    sinkBrine_init=sink.T_set,
    Vi_A=Vi_A,
    EvapFoulingFactor=ECAT.EvapFoulingFactor_m2KW.setPoint,
    Vi_B=Vi_B,
    isCoating=choiceBlock.isCoatingOption,
    selector_VFDA_CKA=choiceBlock.VFD_Type_moduleA[1],
    selector_VFDA_CKB=choiceBlock.VFD_Type_moduleA[2],
    BrineConcentration=ECAT.EvapBrineConcentration_nd.setPoint,
    relative_humidity=ECAT.AmbientAirRH_nd.setPoint,
    CoolantMedium=ECAT.EvapBrineType_nd,
    diameter_nozzle=choiceBlock.Unit.evap_diameter_nozzle,
    n_passes_evap=1,
    longEvap=choiceBlock.Unit.longEvap,
    LWT=
      if use_ECATBlock then
        ECAT.EvapBrineLWT_K.setPoint
      else
        LWT,
    sourceBrine_init=source.T_set,
    fractionB_start=
      if(StateMachine.currentModeID == Workspace.Controller.StateMachine.ModeID30KAV.CrkABEcoABVIAB_2) then
        0.025
      else
        0.04,
    altitude=ECAT.Altitude_m.setPoint,
    globalParameters(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    condAirA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodecondAirinA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodecondAiroutA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    dischargelineA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    liquidlineA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeOilsepoutA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeBPHEECOinA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    oilseparatorA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    split(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeCpoutA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    BPHEECOA(
      redeclare replaceable package RefMedium_H=.BOLT.InternalLibrary.Media.Refrigerant.R1234ze,
      redeclare replaceable package RefMedium_L=.BOLT.InternalLibrary.Media.Refrigerant.R1234ze),
    EXVECOA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    CompressorA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeEXVmaininA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeEXVECOoutA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeCpinA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeCpECOA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    EXVMainA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    suctionlineA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    ECOlineA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeevapinA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeBPHEECOoutA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    valve_92A(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    evaporator(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeevapoutA(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeevapoutB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeevapinB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    valve_92B(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    EXVMainB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    suctionlineB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeEXVmaininB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeCpinB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    BPHEECOB(
      redeclare replaceable package RefMedium_H=.BOLT.InternalLibrary.Media.Refrigerant.R1234ze,
      redeclare replaceable package RefMedium_L=.BOLT.InternalLibrary.Media.Refrigerant.R1234ze),
    CompressorB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    split2(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeCpoutB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeCpECOB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    EXVECOB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeBPHEECOinB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    oilseparatorB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    ECOlineB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeEXVECOoutB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    liquidlineB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeOilsepoutB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodeBPHEECOoutB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodecondAiroutB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    dischargelineB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    condAirB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    nodecondAirinB(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze))
    annotation (Placement(transformation(extent={{22.42424774323196,-14.0},{62.42424774323196,26.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Coolant.Source source(
    p_fixed=true,
    p_set=2.7e5,
    Vd_set=
      if use_ECATBlock then
        ECAT.EvapBrineFlowRate_m3s.setPoint
      else
        Vd_coolant,
    T_set=
      if use_ECATBlock then
        ECAT.EvapBrineEWT_K.setPoint
      else
        EWT,
    T_fixed=
      if use_ECATBlock then
        ECAT.EvapBrineEWT_K.fixed
      else
        isEWT_Fixed,
    Vd_fixed=
      if use_ECATBlock then
        ECAT.EvapBrineFlowRate_m3s.fixed
      else
        isVd_Fixed,
    X=ECAT.EvapBrineConcentration_nd.setPoint,
    CoolantMedium=ECAT.EvapBrineType_nd)
    annotation (Placement(transformation(extent={{-15.245765591247348,-15.245765591247363},{15.245765591247348,15.245765591247363}},origin={42.0,82.0},rotation=-90.0)));
  .BOLT.BoundaryNode.Coolant.Sink sink(
    Vd_set=0.0437950465545824,
    T_set=
      if use_ECATBlock then
        ECAT.EvapBrineLWT_K.setPoint
      else
        LWT,
    T_fixed=
      if use_ECATBlock then
        ECAT.EvapBrineLWT_K.fixed
      else
        isLWT_Fixed,
    p_set=271325,
    p_fixed=false,
    X=ECAT.EvapBrineConcentration_nd.setPoint,
    CoolantMedium=ECAT.EvapBrineType_nd)
    annotation (Placement(transformation(extent={{-16.992756225245216,-16.992756225245223},{16.992756225245216,16.992756225245223}},origin={106.0,8.0},rotation=-180.0)));
equation
  ECO_ON_A=
    if to_degC(
      ModuleA.sourceAirA.summary.Tdb) < ECO_ON_slope*to_degC(
      ECAT.EvapBrineLWT_K.setPoint)+ECO_ON_inter then
      ModuleA.CompressorA.summary.PR > 2.8 and ModuleA.CompressorA.summary.Ncomp/ModuleA.NcompAMax > 0.3
    else
      ModuleA.CompressorA.summary.PR > 2.3 and ModuleA.CompressorA.summary.Ncomp/ModuleA.NcompAMax > 0.3;
  ECO_ON_B=
    if to_degC(
      ModuleA.sourceAirB.summary.Tdb) < ECO_ON_slope*to_degC(
      ECAT.EvapBrineLWT_K.setPoint)+ECO_ON_inter then
      ModuleA.CompressorB.summary.PR > 2.8 and ModuleA.CompressorB.summary.Ncomp/ModuleA.NcompBMax > 0.3
    else
      ModuleA.CompressorB.summary.PR > 2.3 and ModuleA.CompressorB.summary.Ncomp/ModuleA.NcompBMax > 0.3;
  CAP_LOWsat=ModuleA.systemVariables.CoolCap_total < 0.23*ModuleA.globalParameters.capacity_design or controller.completeCompressorControl_base.capacity_controller.summary.ID ==-3;
  VI_A=.Workspace.Controller.StateMachine.isVIOn(
    ModuleA.Speed_crkA,
    95,
    20,
    ModuleA.nodeCpoutA.summary.Tsat,
    ModuleA.nodeCpinA.summary.Tsat);
  VI_ON_A=VI_A > 0.5;
  VI_B=.Workspace.Controller.StateMachine.isVIOn(
    ModuleA.Speed_crkB,
    95,
    20,
    ModuleA.nodeCpoutB.summary.Tsat,
    ModuleA.nodeCpinB.summary.Tsat);
  VI_ON_B=VI_B > 0.5;
  connect(ModuleA.measurementBusA,controllerSettings_crkA.measurementBus)
    annotation (Line(points={{35.24164774323196,28.156599999999997},{35.24164774323196,62},{3.4000000000000004,62}},color={255,204,51}));
  connect(ModuleA.measurementBusA,controller.measurementBus_crkA)
    annotation (Line(points={{35.24164774323196,28.156599999999997},{35.24164774323196,38},{-100,38},{-100,21.750529153390108},{-75.64361307627159,21.750529153390108}},color={255,204,51}));
  connect(ModuleA.measurementBusB,controllerSettings_crkB.measurementBus)
    annotation (Line(points={{50.04164774323196,28.156599999999997},{50.04164774323196,62},{70,62},{70,-50},{5.4,-50}},color={255,204,51}));
  connect(ModuleA.measurementBusB,controller.measurementBus_crkB)
    annotation (Line(points={{50.04164774323196,28.156599999999997},{50.04164774323196,-26},{-100,-26},{-100,2.106916077118526},{-75.64361307627159,2.106916077118526}},color={255,204,51}));
  connect(controller.fan_crkA,ModuleA.Fan_crkA)
    annotation (Line(points={{-34.784897877626676,25.286379507118994},{19.624247743231955,25.286379507118994},{19.624247743231955,23.68}},color={0,0,127}));
  connect(controller.exv_crkA,ModuleA.EXV_Main_crkA)
    annotation (Line(points={{-34.784897877626676,22.14340141491554},{19.624247743231955,22.14340141491554},{19.624247743231955,19.68}},color={0,0,127}));
  connect(controller.sstmax_crkA,ModuleA.actuatorSSTmaxA)
    annotation (Line(points={{-34.784897877626676,18.214678799661222},{19.624247743231955,18.214678799661222},{19.624247743231955,15.68}},color={0,0,127}));
  connect(controller.ecoExv_crkA,ModuleA.EXV_Eco_crkA)
    annotation (Line(points={{-34.784897877626676,14.67882844593234},{19.624247743231955,14.67882844593234},{19.624247743231955,11.68}},color={0,0,127}));
  connect(controller.compressor_crkA,ModuleA.Speed_crkA)
    annotation (Line(points={{-34.784897877626676,9.917216636244108},{19.624247743231955,9.917216636244108},{19.624247743231955,7.68}},color={0,0,127}));
  connect(controller.compressor_crkB,ModuleA.Speed_crkB)
    annotation (Line(points={{-34.784897877626676,5.595621759464359},{19.624247743231955,5.595621759464359},{19.624247743231955,3.6799999999999997}},color={0,0,127}));
  connect(controller.fan_crkB,ModuleA.Fan_crkB)
    annotation (Line(points={{-34.784897877626676,1.3211715540676616},{19.624247743231955,1.3211715540676616},{19.624247743231955,-0.3200000000000003}},color={0,0,127}));
  connect(controller.sstmax_crkB,ModuleA.actuatorSSTmaxB)
    annotation (Line(points={{-34.784897877626676,-6.143401414915541},{19.624247743231955,-6.143401414915541},{19.624247743231955,-8.32}},color={0,0,127}));
  connect(controller.ecoExv_crkB,ModuleA.EXV_Eco_crkB)
    annotation (Line(points={{-34.784897877626676,-9.679251768644423},{19.624247743231955,-9.679251768644423},{19.624247743231955,-12.32}},color={0,0,127}));
  connect(controller.exv_crkB,ModuleA.EXV_Main_crkB)
    annotation (Line(points={{-34.784897877626676,-2.607551061186655},{19.624247743231955,-2.607551061186655},{19.624247743231955,-4.32}},color={0,0,127}));
  connect(ModuleA.port_b,sink.port)
    annotation (Line(points={{65.46424774323197,5.68},{89.00724377475478,5.68},{89.00724377475478,7.999999999999998}},color={0,127,0}));
  connect(source.port,ModuleA.port_a)
    annotation (Line(points={{42,66.75423440875265},{42,38},{6,38},{6,5.68},{19.464247743231958,5.68}},color={0,127,0}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={245,166,35},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end CL_30XBV_OnePasse;
