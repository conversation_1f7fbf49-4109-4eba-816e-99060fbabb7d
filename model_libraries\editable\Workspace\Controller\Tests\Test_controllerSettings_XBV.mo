within Workspace.Controller.Tests;
model Test_controllerSettings_XBV
  .Workspace.Controller.ControllerSettings controllerSettings
    annotation (Placement(transformation(extent={{42.0,14.0},{62.0,34.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression comp_freq(
    y=23)
    annotation (Placement(transformation(extent={{-83.03965515075723,50.0},{-52.96034484924277,70.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sst(
    y=5+273.15)
    annotation (Placement(transformation(extent={{-83.03965515075723,31.599999999999994},{-52.96034484924277,51.599999999999994}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sdt(
    y=55+273.15)
    annotation (Placement(transformation(extent={{-83.03965515075723,13.599999999999998},{-52.96034484924277,33.599999999999994}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression oat(
    y=30+273.15)
    annotation (Placement(transformation(extent={{-83.03965515075723,-6.400000000000002},{-52.96034484924277,13.599999999999998}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression lwt(
    y=7+273.15)
    annotation (Placement(transformation(extent={{-83.03965515075723,-28.400000000000006},{-52.96034484924277,-8.400000000000006}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-22.0,4.0},{18.0,44.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_level(
    y=0.5)
    annotation (Placement(transformation(extent={{-83.03965515075723,68.0},{-52.96034484924277,88.0}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(comp_freq.y,measurementBus.compressorFrequency)
    annotation (Line(points={{-51.45637933416705,60},{-34.72818966708353,60},{-34.72818966708353,24},{-2,24}},color={0,0,127}));
  connect(sst.y,measurementBus.T_sst)
    annotation (Line(points={{-51.45637933416705,41.599999999999994},{-34.72818966708353,41.599999999999994},{-34.72818966708353,24},{-2,24}},color={0,0,127}));
  connect(sdt.y,measurementBus.T_sdt)
    annotation (Line(points={{-51.45637933416705,23.599999999999994},{-34.72818966708353,23.599999999999994},{-34.72818966708353,24},{-2,24}},color={0,0,127}));
  connect(oat.y,measurementBus.T_oat)
    annotation (Line(points={{-51.45637933416705,3.599999999999998},{-34.72818966708353,3.599999999999998},{-34.72818966708353,24},{-2,24}},color={0,0,127}));
  connect(lwt.y,measurementBus.T_lwt)
    annotation (Line(points={{-51.45637933416705,-18.40000000000001},{-34.72818966708353,-18.40000000000001},{-34.72818966708353,24},{-2,24}},color={0,0,127}));
  connect(controllerSettings.measurementBus,measurementBus)
    annotation (Line(points={{40.6,24},{-2,24}},color={255,204,51}));
  connect(rel_cooler_level.y,measurementBus.rel_cooler_level)
    annotation (Line(points={{-51.45637933416705,78},{-34,78},{-34,24},{-2,24}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Test_controllerSettings_XBV;
