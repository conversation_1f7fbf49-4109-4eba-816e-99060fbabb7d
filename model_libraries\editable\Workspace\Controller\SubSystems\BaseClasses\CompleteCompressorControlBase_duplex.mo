within Workspace.Controller.SubSystems.BaseClasses;
partial model CompleteCompressorControlBase_duplex
  extends BOLT.InternalLibrary.BuildingBlocks.Icons.Compressor;
  parameter Boolean isOff_moduleA_crkA=false;
  parameter Boolean isOff_moduleA_crkB=false;
  parameter Boolean isOff_moduleB_crkA=false;
  parameter Boolean isOff_moduleB_crkB=false;
  parameter Real capacityDesign_moduleA_max
    "Minimum capacity design of module A (w)"
    annotation (Dialog(group="capacity design"));
  parameter Real capacityDesign_moduleB_max
    "Minimum capacity design of module B (w)"
    annotation (Dialog(group="capacity design"));
  parameter Real max_compressor_speed_moduleA_crkA
    "Maximum speed of compressor module A (Hz)"
    annotation (Dialog(group="compressor speed module A"));
  parameter Real min_compressor_speed_moduleA_crkA
    "Minimum speed of compressor module A (Hz)"
    annotation (Dialog(group="compressor speed module A"));
  parameter Real max_compressor_speed_moduleA_crkB
    "Minimum speed of compressor module B (Hz)"
    annotation (Dialog(group="compressor speed module A"));
  parameter Real min_compressor_speed_moduleA_crkB
    "Maximum speed of compressor module B (Hz)"
    annotation (Dialog(group="compressor speed module A"));
  parameter Real max_compressor_speed_moduleB_crkA
    "Maximum speed of compressor module A (Hz)"
    annotation (Dialog(group="compressor speed module B"));
  parameter Real min_compressor_speed_moduleB_crkA
    "Minimum speed of compressor module A (Hz)"
    annotation (Dialog(group="compressor speed module B"));
  parameter Real max_compressor_speed_moduleB_crkB
    "Minimum speed of compressor module B (Hz)"
    annotation (Dialog(group="compressor speed module B"));
  parameter Real min_compressor_speed_moduleB_crkB
    "Maximum speed of compressor module B (Hz)"
    annotation (Dialog(group="compressor speed module B"));
  parameter Boolean isOffSSTmin=false;
  parameter Boolean isOffSDTmax=false;
  parameter Boolean isOffDGTmax=false;
  //extends .Workspace.Controller.SubSystems.BaseClasses.CompleteCompressorControlBase(
  //redeclare replaceable .Workspace.Controller.SubSystems.CompressorControl compressorControl_crkA(isOffSSTmin = false,isOffSDTmax = false,isOffDGTmax = false),
  //redeclare replaceable .Workspace.Controller.SubSystems.CompressorControl compressorControl_crkB,crkA_isOff = false,crkB_isOff = false);
  replaceable.Workspace.Controller.SubSystems.BaseClasses.CompressorControlBase compressorControl_moduleA_crkA(
    crkIsOff=isOff_moduleA_crkA,
    compressorFrequency_max=max_compressor_speed_moduleA_crkA,
    compressorFrequency_min=min_compressor_speed_moduleA_crkA)
    annotation (Placement(transformation(extent={{-20.0,72.00000000000001},{16.0,107.99999999999999}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus_moduleA_crkA
    annotation (Placement(transformation(extent={{-138.28999553840197,93.71000446159802},{-97.71000446159803,134.28999553840197}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-138.289995538402,45.710004461598004},{-97.71000446159803,86.28999553840197}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_moduleA_crkA
    annotation (Placement(transformation(extent={{-130.0,70.0},{-110.0,90.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-127.99999999999999,24.000000000000014},{-107.99999999999999,44.000000000000014}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal_moduleA_crkA
    annotation (Placement(transformation(extent={{137.13204291358713,69.13204291358714},{178.86795708641287,110.86795708641286}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{95.13204291358713,23.132042913587142},{136.86795708641287,64.86795708641286}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus_moduleA_crkB
    annotation (Placement(transformation(extent={{-138.2690746423837,-38.2690746423837},{-97.73092535761629,2.2690746423837}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-138.2690746423837,-56.2690746423837},{-97.73092535761629,-15.7309253576163}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_moduleA_crkB
    annotation (Placement(transformation(extent={{-128.0,-52.0},{-108.0,-32.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-128.0,-78.0},{-108.0,-58.0}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.CompressorControlBase compressorControl_moduleA_crkB(
    crkIsOff=isOff_moduleA_crkB,
    compressorFrequency_min=min_compressor_speed_moduleA_crkB,
    compressorFrequency_max=max_compressor_speed_moduleA_crkB)
    annotation (Placement(transformation(extent={{-20.0,-50.00000000000001},{16.0,-13.999999999999993}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal_moduleA_crkB
    annotation (Placement(transformation(extent={{133.13204291358713,-50.86795708641286},{174.86795708641287,-9.132042913587142}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{95.13204291358713,-50.86795708641286},{136.86795708641287,-9.132042913587142}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation capacity_error_moduleA(
    ID=1,
    measurement=capacity_moduleA,
    setPoint=loadDistribution_DesignCapa_duplex.SetPoint_capacity_moduleA,
    gain=1/500000)
    annotation (Placement(transformation(extent={{-83.94448733196893,19.71272833333333},{-39.722179334697735,42.95393833333333}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController capacity_controller_moduleA(
    AV_min=0,
    AV_max=1)
    annotation (Placement(transformation(extent={{-20.087600000000002,19.912316},{0.0876000000000019,40.087683999999996}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.SubSystems.BaseClasses.LoadDistribution loadDistribution_moduleA(
    crkA_max_speed=max_compressor_speed_moduleA_crkA,
    crkB_max_speed=max_compressor_speed_moduleA_crkB,
    crkA_min_speed=min_compressor_speed_moduleA_crkA,
    crkB_min_speed=min_compressor_speed_moduleA_crkB)
    annotation (Placement(transformation(extent={{18.166666666666664,19.333333333333332},{38.166666666666664,39.33333333333333}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.CompressorControlBase compressorControl_moduleB_crkA(
    compressorFrequency_max=max_compressor_speed_moduleB_crkA,
    compressorFrequency_min=min_compressor_speed_moduleB_crkA,
    crkIsOff=isOff_moduleB_crkA)
    annotation (Placement(transformation(extent={{-14.0,-201.99999999999997},{22.0,-166.00000000000003}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus_moduleB_crkA
    annotation (Placement(transformation(extent={{-121.78363151901199,-191.60651668735957},{-81.20364044220803,-151.02652561055558}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-138.289995538402,45.710004461598004},{-97.71000446159803,86.28999553840197}},origin={-30,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_moduleB_crkA
    annotation (Placement(transformation(extent={{-106.73460108634711,-214.48520710059174},{-86.73460108634711,-194.48520710059174}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-127.99999999999999,24.000000000000014},{-107.99999999999999,44.000000000000014}},origin={-30,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal_moduleB_crkA
    annotation (Placement(transformation(extent={{135.13204291358713,-204.86795708641284},{176.86795708641287,-163.13204291358716}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{97.13204291358713,89.13204291358716},{138.86795708641287,130.86795708641284}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus_moduleB_crkB
    annotation (Placement(transformation(extent={{-117.00367572873081,-305.75428174297537},{-76.46552644396341,-265.21613245820794}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-138.2690746423837,-56.2690746423837},{-97.73092535761629,-15.7309253576163}},origin={-30,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_moduleB_crkB
    annotation (Placement(transformation(extent={{-106.73460108634711,-335.48520710059177},{-86.73460108634711,-315.48520710059177}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-128.0,-78.0},{-108.0,-58.0}},origin={-30,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.CompressorControlBase compressorControl_moduleB_crkB(
    compressorFrequency_max=max_compressor_speed_moduleB_crkB,
    compressorFrequency_min=min_compressor_speed_moduleB_crkB,
    crkIsOff=isOff_moduleB_crkB)
    annotation (Placement(transformation(extent={{-14.73460108634712,-322.48520710059165},{21.26539891365288,-286.48520710059165}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal_moduleB_crkB
    annotation (Placement(transformation(extent={{135.13204291358713,-322.86795708641284},{176.86795708641287,-281.13204291358716}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{97.13204291358713,-118.86795708641284},{138.86795708641287,-77.13204291358716}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.SubSystems.BaseClasses.LoadDistribution loadDistribution_moduleB(
    crkB_min_speed=min_compressor_speed_moduleB_crkB,
    crkA_min_speed=min_compressor_speed_moduleB_crkA,
    crkB_max_speed=max_compressor_speed_moduleB_crkB,
    crkA_max_speed=max_compressor_speed_moduleB_crkA)
    annotation (Placement(transformation(extent={{23.265398913652888,-254.48520710059174},{43.26539891365289,-234.48520710059174}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController capacity_controller_moduleB(
    AV_max=1,
    AV_min=0)
    annotation (Placement(transformation(extent={{-14.822201086347121,-254.57289110059176},{5.352998913652883,-234.3975231005917}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation capacity_error_moduleB(
    gain=1/500000,
    setPoint=loadDistribution_DesignCapa_duplex.SetPoint_capacity_moduleB,
    measurement=capacity_moduleB,
    ID=1)
    annotation (Placement(transformation(extent={{-78.8457550849827,-254.10581210059175},{-34.6234470877115,-230.86460210059172}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.SubSystems.BaseClasses.LoadDistribution_DesignCapa_duplex loadDistribution_DesignCapa_duplex(
    capacityDesign_moduleA_max=capacityDesign_moduleA_max,
    capacityDesign_moduleB_max=capacityDesign_moduleB_max)
    annotation (Placement(transformation(extent={{-321.2059094121457,-167.20590941214567},{-194.79409058785433,-40.79409058785433}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput capacity_moduleB
    annotation (Placement(transformation(extent={{-83.78188895116939,-232.3878456895531},{-63.93555974647374,-212.54151648485748}},origin={0.0,0.0},rotation=0.0)));
protected
  .Modelica.Blocks.Interfaces.RealInput capacity_moduleA
    annotation (Placement(transformation(extent={{-79.92316460234781,44.076835397652175},{-60.07683539765219,63.923164602347825}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(compressorControl_moduleA_crkA.measurementBus,measurementBus_moduleA_crkA)
    annotation (Line(points={{-20,100.79999999999998},{-20,114},{-118,114}},color={255,204,51}));
  connect(compressorControl_moduleA_crkA.limitsBus,limitsBus_moduleA_crkA)
    annotation (Line(points={{-20,79.20000000000002},{-120,79.20000000000002},{-120,80}},color={255,204,51}));
  connect(compressorControl_moduleA_crkB.measurementBus,measurementBus_moduleA_crkB)
    annotation (Line(points={{-20,-21.199999999999996},{-118,-21.199999999999996},{-118,-18}},color={255,204,51}));
  connect(compressorControl_moduleA_crkB.limitsBus,limitsBus_moduleA_crkB)
    annotation (Line(points={{-20,-42.800000000000004},{-118,-42.800000000000004},{-118,-42}},color={255,204,51}));
  connect(compressorControl_moduleB_crkA.measurementBus,measurementBus_moduleB_crkA)
    annotation (Line(points={{-14,-173.20000000000002},{-101.49363598061001,-173.20000000000002},{-101.49363598061001,-171.31652114895758}},color={255,204,51}));
  connect(compressorControl_moduleB_crkA.limitsBus,limitsBus_moduleB_crkA)
    annotation (Line(points={{-14,-194.79999999999998},{-96.73460108634711,-194.79999999999998},{-96.73460108634711,-204.48520710059174}},color={255,204,51}));
  connect(compressorControl_moduleB_crkB.measurementBus,measurementBus_moduleB_crkB)
    annotation (Line(points={{-14.734601086347112,-293.6852071005917},{-96.73460108634711,-293.6852071005917},{-96.73460108634711,-285.48520710059177}},color={255,204,51}));
  connect(compressorControl_moduleB_crkB.limitsBus,limitsBus_moduleB_crkB)
    annotation (Line(points={{-14.734601086347112,-315.2852071005917},{-96.73460108634711,-315.2852071005917},{-96.73460108634711,-325.48520710059177}},color={255,204,51}));
  connect(compressorControl_moduleA_crkA.actuatorSignal,actuatorSignal_moduleA_crkA)
    annotation (Line(points={{16,90},{158,90}},color={0,0,127}));
  connect(compressorControl_moduleA_crkB.actuatorSignal,actuatorSignal_moduleA_crkB)
    annotation (Line(points={{16,-32},{154,-32},{154,-30}},color={0,0,127}));
  connect(compressorControl_moduleB_crkA.actuatorSignal,actuatorSignal_moduleB_crkA)
    annotation (Line(points={{22,-184},{156,-184}},color={0,0,127}));
  connect(compressorControl_moduleB_crkB.actuatorSignal,actuatorSignal_moduleB_crkB)
    annotation (Line(points={{21.265398913652888,-304.48520710059177},{156,-304.48520710059177},{156,-302}},color={0,0,127}));
  connect(capacity_error_moduleA.sensor,capacity_controller_moduleA.errorSignal)
    annotation (Line(points={{-40.16440241467045,30.40368493333333},{-20.087600000000002,30.40368493333333},{-20.087600000000002,30}},color={28,108,200}));
  connect(capacity_error_moduleB.sensor,capacity_controller_moduleB.errorSignal)
    annotation (Line(points={{-35.065670167684225,-243.41485550059173},{-14.822201086347114,-243.41485550059173},{-14.822201086347114,-244.48520710059174}},color={28,108,200}));
  connect(loadDistribution_moduleA.crkA_speed,compressorControl_moduleA_crkA.compressorFrequency)
    annotation (Line(points={{38.166666666666664,35.33333333333333},{48.166666666666664,35.33333333333333},{48.166666666666664,61.33333333333333},{-35.833333333333336,61.33333333333333},{-35.833333333333336,90},{-20,90}},color={0,0,127}));
  connect(loadDistribution_moduleA.crkB_speed,compressorControl_moduleA_crkB.compressorFrequency)
    annotation (Line(points={{38.166666666666664,23.333333333333332},{48.166666666666664,23.333333333333332},{48.166666666666664,-2.8666666666666707},{-35.833333333333336,-2.8666666666666707},{-35.833333333333336,-32},{-20,-32}},color={0,0,127}));
  connect(capacity_controller_moduleA.actuatorSignal,loadDistribution_moduleA.normalized_total_load)
    annotation (Line(points={{0.6928560000000026,30},{0.6928560000000026,29.333333333333332},{18.166666666666664,29.333333333333332}},color={0,0,127}));
  connect(loadDistribution_moduleB.crkB_speed,compressorControl_moduleB_crkB.compressorFrequency)
    annotation (Line(points={{43.26539891365289,-250.48520710059174},{53.26539891365289,-250.48520710059174},{53.26539891365289,-276.6852071005917},{-30.734601086347112,-276.6852071005917},{-30.734601086347112,-304.48520710059177},{-14.734601086347112,-304.48520710059177}},color={0,0,127}));
  connect(loadDistribution_moduleB.crkA_speed,compressorControl_moduleB_crkA.compressorFrequency)
    annotation (Line(points={{43.26539891365289,-238.48520710059174},{53.26539891365289,-238.48520710059174},{53.26539891365289,-212.48520710059174},{-30.734601086347112,-212.48520710059174},{-30.734601086347112,-184},{-14,-184}},color={0,0,127}));
  connect(capacity_controller_moduleB.actuatorSignal,loadDistribution_moduleB.normalized_total_load)
    annotation (Line(points={{5.95825491365289,-244.48520710059174},{23.265398913652888,-244.48520710059174}},color={0,0,127}));
  connect(capacity_moduleA,measurementBus_moduleA_crkA.capacity)
    annotation (Line(points={{-70,54},{-82,54},{-82,114},{-118,114}},color={0,0,127}));
  connect(loadDistribution_DesignCapa_duplex.ControllerSignal,limitsBus_moduleA_crkA.capacity_setpoint)
    annotation (Line(points={{-317.91920212271407,-103.74717636235141},{-360,-103.74717636235141},{-360,80},{-120,80}},color={0,0,127}));
  connect(loadDistribution_DesignCapa_duplex.LWT,measurementBus_moduleB_crkA.T_lwt)
    annotation (Line(points={{-317.91920212271407,-110.06776730356599},{-366,-110.06776730356599},{-366,-171.31652114895758},{-101.49363598061001,-171.31652114895758}},color={0,0,127}));
  connect(capacity_moduleB,measurementBus_moduleB_crkA.capacity)
    annotation (Line(points={{-73.85872434882157,-222.4646810872053},{-117.70057984144482,-222.4646810872053},{-117.70057984144482,-171.31652114895758},{-101.49363598061001,-171.31652114895758}},color={0,0,127}));
  connect(loadDistribution_DesignCapa_duplex.EWT,measurementBus_moduleA_crkA.T_ewt)
    annotation (Line(points={{-317.91920212271407,-97.42658542113683},{-370.8646113521378,-97.42658542113683},{-370.8646113521378,114},{-118,114}},color={0,0,127}));
end CompleteCompressorControlBase_duplex;
