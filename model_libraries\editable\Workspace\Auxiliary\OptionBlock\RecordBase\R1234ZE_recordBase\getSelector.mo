within Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase;
function getSelector
  input.Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.Selector Selector;
  output.Workspace.Auxiliary.OptionBlock.RecordBase.UnitBase_2C Unit;
protected
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.Unit_500_R1234ze Unit_500_R1234ze;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.Unit_600_R1234ze Unit_600_R1234ze;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.Unit_700_R1234ze Unit_700_R1234ze;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.Unit_800_R1234ze Unit_800_R1234ze;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.Unit_900_R1234ze Unit_900_R1234ze;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.Unit_1000_R1234ze Unit_1000_R1234ze;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.Unit_1100_R1234ze Unit_1100_R1234ze;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.Unit_1200_R1234ze Unit_1200_R1234ze;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.Unit_1300_R1234ze Unit_1300_R1234ze;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.Unit_1450_R1234ze Unit_1450_R1234ze;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.Unit_700_Duplex_R1234ze Unit_700_Duplex_R1234ze;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.Unit_800_Duplex_R1234ze Unit_800_Duplex_R1234ze;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R1234ZE_recordBase.Unit_900_Duplex_R1234ze Unit_900_Duplex_R1234ze;
  .Workspace.Auxiliary.OptionBlock.RecordBase.UnitBase_2C[:] Unit_base={Unit_500_R1234ze,Unit_600_R1234ze,Unit_700_R1234ze,Unit_800_R1234ze,Unit_900_R1234ze,Unit_1000_R1234ze,Unit_1100_R1234ze,Unit_1200_R1234ze,Unit_1300_R1234ze,Unit_1450_R1234ze,Unit_700_Duplex_R1234ze,Unit_800_Duplex_R1234ze,Unit_900_Duplex_R1234ze};
algorithm
  Unit := Unit_base[Integer(
    Selector)];
end getSelector;
