within Workspace.Surrogate;
model CondAirM_Surrogate
  parameter.Modelica.SIunits.Length length=1.404
    "tube length";
  extends Workspace.Surrogate.MCHX_Cond.SubComponents.Configuration(
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a);
  .BOLT.InternalLibrary.Refrigerant.Aggregation.ComputeStreamsAggregates computeStreamsAggregates(
    mediumName =refBase.refName,
    checkMedium=true,
    streamID=streamID,
    mRef=summary.mass,
    pow=0,
    CoolCap=k_CoolCap*summary.Q_flow_ref,
    HeatCap=k_HeatCap*summary.Q_flow_air)
    annotation (Placement(transformation(extent={{38,76},{58,96}})));
  .Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.Summary summary(
    Z_U=condAirM_Sub.ZU_input,
    Z_dpr=condAirM_Sub.Zdpr_input,
    Z_dpa=1,
    velocity_air=0,
    massFlux_ref={0,0},
    Q_flow_ref=
      if isOff then
        0
      else
        condAirM_Sub.Q_flow_ref*nCoils,
    Q_flow_air=
      if isOff then
        0
      else
        condAirM_Sub.Q_flow_air*nCoils,
    dp_ref=
      if isOff or isOff_ref then
        0
      else
        condAirM_Sub.dp_ref,
    dp_air=
      if isOff then
        0
      else
        condAirM_Sub.dp_air,
    mass=
      if isOff or isOff_ref then
        0
      else
        condAirM_Sub.mass_ref*nCoils,
    delta_Tsat=
      if isOff or isOff_ref then
        0
      else
        condAirM_Sub.delta_Tsat,
    iFlag=-999,
    volume=0,
    isOff=isOff)
    annotation (Placement(transformation(extent={{-98.0,82.0},{-78.0,102.0}},rotation=0.0,origin={0.0,0.0})));
  .BOLT.InternalLibrary.BuildingBlocks.Refrigerant.Ports.FluidPort_a port_a_ref(
    RefMedium = RefMedium,
    streamID=streamID)
    annotation (Placement(transformation(extent={{-112.0,-10.0},{-92.0,10.0}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{-110,-10},{-90,10}})));
  .BOLT.InternalLibrary.BuildingBlocks.Refrigerant.Ports.FluidPort_b port_b_ref(
    RefMedium = RefMedium,
    streamID=streamID)
    annotation (Placement(transformation(extent={{90,-10},{110,10}}),iconTransformation(extent={{90,-10},{110,10}})));
  .Workspace.Surrogate.CondAirM_Sub_Surrogate condAirM_Sub(
    redeclare package AirMedium=AirMedium,
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a,
    nCoils=nCoils,
    x_out_start=x_out_start,
    OAT=OAT,
    p_in_air_gage_start=p_in_air_gage_start,
    isOff=isOff,
    streamID=streamID,
    length=length,
    Z_U=Z_U,
    Z_U_fixed=Z_U_fixed,
    capacity=capacity,
    capacity_fixed=capacity_fixed,
    dpr=dpr,
    dpr_fixed=dpr_fixed,
    use_ZU_in=use_ZU_in,
    use_Z_fanScaling=use_Z_fanScaling,
    Z_dpr=Z_dpr,
    Z_dpr_fixed=Z_dpr_fixed,
    use_Zdpr_in=use_Zdpr_in,
    isOff_ref=isOff_ref,
    selector=selector,
    Tsat_in_start=Tsat_in_start)
    annotation (Placement(transformation(extent={{-45.0,-52.0},{53.0,52.0}},rotation=0.0,origin={0.0,0.0})));
  .BOLT.InternalLibrary.BuildingBlocks.Air.Ports.FluidPort_a port_a_air(
    redeclare package Medium=AirMedium)
    annotation (Placement(transformation(extent={{-10,-110},{10,-90}}),iconTransformation(extent={{-10,-110},{10,-90}})));
  .BOLT.InternalLibrary.BuildingBlocks.Air.Ports.FluidPort_b port_b_air(
    redeclare package Medium=AirMedium)
    annotation (Placement(transformation(extent={{-10,90},{10,110}}),iconTransformation(extent={{-10,90},{10,110}})));
  .Modelica.Blocks.Interfaces.RealInput ZU_in if use_ZU_in
    "External Z_U"
    annotation (Placement(transformation(extent={{-142,-62},{-100,-20}},rotation=0),iconTransformation(extent={{-118,-38},{-100,-20}})));
  .Modelica.Blocks.Interfaces.RealInput Zdpr_in if use_Zdpr_in
    "External Z_dpr"
    annotation (Placement(transformation(extent={{-143.0,-33.0},{-101.0,9.0}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{-118,-38},{-100,-20}})));
  .Modelica.Blocks.Interfaces.RealInput Z_fanScaling if use_Z_fanScaling
    "External Z_fanScaling"
    annotation (Placement(transformation(extent={{-142,-102},{-100,-60}},rotation=0),iconTransformation(extent={{-118,-78},{-100,-60}})));
  parameter.Workspace.Surrogate.MCHX_Cond.Selector selector=Surrogate.MCHX_Cond.Selector.MCHX_Cond_R134a;
  .BOLT.InternalLibrary.BuildingBlocks.Refrigerant.FlowAdapters.Multiply divider(
    nParallel=nCoils,
    streamID=streamID,
    RefMedium = RefMedium)
    annotation (Placement(transformation(extent={{-84.0,-10.0},{-64.0,10.0}},rotation=0.0,origin={0.0,0.0})));
  .BOLT.InternalLibrary.BuildingBlocks.Refrigerant.FlowAdapters.Multiply multiply(
    nParallel=nCoils,
    streamID=streamID,
    RefMedium = RefMedium,
    multiplier=true)
    annotation (Placement(transformation(extent={{62.0,-10.0},{82.0,10.0}},rotation=0.0,origin={0.0,0.0})));
equation
  connect(port_b_ref,port_b_ref)
    annotation (Line(points={{100,0},{100,0}},color={255,0,0}));
  connect(ZU_in,condAirM_Sub.ZU_in)
    annotation (Line(points={{-121,-41},{-82,-41},{-82,-18.200000000000003},{-49.41,-18.200000000000003}},color={0,0,127}));
  connect(Z_fanScaling,condAirM_Sub.Z_fanScaling)
    annotation (Line(points={{-121,-81},{-74,-81},{-74,-34.84},{-49.41,-34.84}},color={0,0,127}));
  connect(port_a_ref,divider.port_a)
    annotation (Line(points={{-102,0},{-84,0}},color={255,0,0}));
  connect(divider.port_b,condAirM_Sub.port_a_ref)
    annotation (Line(points={{-64,0},{-72,0},{-72,1.04},{-45.98,1.04}},color={255,0,0}));
  connect(multiply.port_b,port_b_ref)
    annotation (Line(points={{82,0},{100,0}},color={255,0,0}));
  connect(multiply.port_a,condAirM_Sub.port_b_ref)
    annotation (Line(points={{62,0},{53.98,0}},color={255,0,0}));
  connect(condAirM_Sub.port_b_air,port_b_air)
    annotation (Line(points={{4,45.760000000000005},{4,72.88},{0,72.88},{0,100}},color={0,0,255}));
  connect(port_a_air,condAirM_Sub.port_a_air)
    annotation (Line(points={{0,-100},{0,-72.36},{4,-72.36},{4,-44.72}},color={0,0,255}));
  connect(Zdpr_in,condAirM_Sub.Zdpr_in)
    annotation (Line(points={{-122,-12},{-82,-12},{-82,-13},{-49.41,-13}},color={0,0,127}));
  annotation (
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100,-100},{100,100}})),
    Icon(
      coordinateSystem(
        preserveAspectRatio=false),
      graphics={
        Rectangle(
          extent={{-88,64},{-72,-66}},
          fillPattern=FillPattern.HorizontalCylinder,
          pattern=LinePattern.None,
          lineColor={0,0,0},
          fillColor={238,46,47}),
        Rectangle(
          extent={{72,64},{88,-66}},
          fillPattern=FillPattern.HorizontalCylinder,
          pattern=LinePattern.None,
          lineColor={0,0,0},
          fillColor={238,46,47}),
        Line(
          points={{-72,6},{-60,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,6},{-60,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,6},{-34,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,6},{-34,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,6},{-8,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,6},{-8,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,6},{18,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,6},{18,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,6},{44,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,6},{44,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,6},{70,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,-18},{-60,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-72,-18},{-60,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,-18},{-34,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,-18},{-34,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,-18},{-8,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,-18},{-8,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,-18},{18,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,-18},{18,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,-18},{44,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,-18},{44,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,-42},{-60,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-72,-42},{-60,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,-42},{-34,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,-42},{-34,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,-42},{-8,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,-42},{-8,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,-42},{18,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,-42},{18,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,-42},{44,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,-42},{44,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,-18},{70,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,-42},{70,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,30},{-60,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-72,30},{-60,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,30},{-34,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,30},{-34,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,30},{-8,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,30},{-8,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,30},{18,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,30},{18,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,30},{44,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,30},{44,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,30},{70,16}},
          color={0,0,0},
          thickness=0.5),
        Rectangle(
          extent={{-72,64},{72,54}},
          lineColor={0,0,0},
          lineThickness=1,
          fillPattern=FillPattern.HorizontalCylinder,
          fillColor={238,46,47}),
        Line(
          points={{-46,54},{-60,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-72,54},{-60,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,54},{-34,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,54},{-34,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,54},{-8,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,54},{-8,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,54},{18,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,54},{18,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,54},{44,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,54},{44,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,54},{70,40}},
          color={0,0,0},
          thickness=0.5),
        Rectangle(
          extent={{-72,64},{72,54}},
          lineThickness=1,
          lineColor={0,0,0}),
        Rectangle(
          extent={{-88,64},{-72,-66}},
          lineThickness=1,
          lineColor={0,0,0}),
        Rectangle(
          extent={{72,64},{88,-66}},
          lineThickness=1,
          lineColor={0,0,0}),
        Rectangle(
          extent={{-72,40},{72,30}},
          lineColor={0,0,0},
          lineThickness=1,
          fillPattern=FillPattern.HorizontalCylinder,
          fillColor={238,46,47}),
        Rectangle(
          extent={{-72,40},{72,30}},
          lineThickness=1,
          lineColor={0,0,0}),
        Rectangle(
          extent={{-72,16},{72,6}},
          lineColor={0,0,0},
          lineThickness=1,
          fillPattern=FillPattern.HorizontalCylinder,
          fillColor={238,46,47}),
        Rectangle(
          extent={{-72,16},{72,6}},
          lineThickness=1,
          lineColor={0,0,0}),
        Rectangle(
          extent={{-72,-8},{72,-18}},
          lineColor={0,0,0},
          lineThickness=1,
          fillPattern=FillPattern.HorizontalCylinder,
          fillColor={238,46,47}),
        Rectangle(
          extent={{-72,-8},{72,-18}},
          lineThickness=1,
          lineColor={0,0,0}),
        Rectangle(
          extent={{-72,-32},{72,-42}},
          lineColor={0,0,0},
          lineThickness=1,
          fillPattern=FillPattern.HorizontalCylinder,
          fillColor={238,46,47}),
        Rectangle(
          extent={{-72,-32},{72,-42}},
          lineThickness=1,
          lineColor={0,0,0}),
        Rectangle(
          extent={{-72,-56},{72,-66}},
          lineColor={0,0,0},
          lineThickness=1,
          fillPattern=FillPattern.HorizontalCylinder,
          fillColor={238,46,47}),
        Rectangle(
          extent={{-72,-56},{72,-66}},
          lineThickness=1,
          lineColor={0,0,0})}),
    Documentation(
      info="<html>
</html>",
      revisions="<html>
<p></p>
</html>"));
end CondAirM_Surrogate;
