within Workspace.Controller.SubSystems.BaseClasses;
model LoadDistribution_1
  "Computes load distribution for both compressors"
  parameter Real crkA_min_speed
    "Minimum speed of compressor A (Hz)";
  parameter Real crkB_min_speed
    "Minimum speed of compressor B (Hz)";
  parameter Real crkA_max_speed
    "Maximum speed of compressor A (Hz)";
  parameter Real crkB_max_speed
    "Maximum speed of compressor B (Hz)";
  parameter Real crkC_min_speed
    "Minimum speed of compressor C (Hz)";
  parameter Real crkD_min_speed
    "Minimum speed of compressor D (Hz)";
  parameter Real crkC_max_speed
    "Maximum speed of compressor C (Hz)";
  parameter Real crkD_max_speed
    "Maximum speed of compressor D (Hz)";
  .Modelica.Blocks.Interfaces.RealInput normalized_total_load
    annotation (Placement(transformation(extent={{-10.64857109674557,-10.648571096745577},{10.64857109674557,10.648571096745577}},origin={-87.20775456658987,24.617249320048465},rotation=-180.0)));
  .Modelica.Blocks.Interfaces.RealOutput crkA_speed
    annotation (Placement(transformation(extent={{122.2773095466228,89.78092573190531},{142.2773095466228,109.78092573190531}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput crkB_speed
    annotation (Placement(transformation(extent={{119.93205573010661,32.06530452173292},{139.9320557301066,52.06530452173292}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Add addA
    annotation (Placement(transformation(extent={{68.37730332454105,89.98321789804777},{88.37730332454105,109.98321789804777}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Gain gainA(
    k=crkA_max_speed-crkA_min_speed)
    annotation (Placement(transformation(extent={{19.323344011956458,108.06611814997582},{42.69350829504088,131.43628243306023}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Gain gainB(
    k=crkB_max_speed-crkB_min_speed)
    annotation (Placement(transformation(extent={{18.246973588564394,50.048401387888894},{41.617137871648815,73.41856567097332}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression crkA_min(
    y=crkA_min_speed)
    annotation (Placement(transformation(extent={{20.651009813168272,83.86269009088788},{40.65100981316827,103.86269009088788}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression crkB_min(
    y=crkB_min_speed)
    annotation (Placement(transformation(extent={{19.600234737804755,25.733483529431098},{39.600234737804755,45.7334835294311}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Add addB
    annotation (Placement(transformation(extent={{69.93205573010661,31.733483529431105},{89.93205573010661,51.733483529431105}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput crkC_speed
    annotation (Placement(transformation(extent={{119.61579729268368,-34.89927782308567},{139.61579729268368,-14.899277823085669}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Gain gainC(
    k=crkC_max_speed-crkC_min_speed)
    annotation (Placement(transformation(extent={{17.930715151141477,-16.58435996462788},{41.3008794342259,6.785804318456542}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression crkC_min(
    y=crkC_min_speed)
    annotation (Placement(transformation(extent={{19.615797292683688,-40.89927782308567},{39.61579729268369,-20.89927782308567}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Add addC
    annotation (Placement(transformation(extent={{69.61579729268368,-34.89927782308567},{89.61579729268368,-14.899277823085669}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput crkD_speed
    annotation (Placement(transformation(extent={{118.37590927865278,-102.17868056458367},{138.37590927865278,-82.17868056458367}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Gain gainD(
    k=crkD_max_speed-crkD_min_speed)
    annotation (Placement(transformation(extent={{16.690827137110574,-83.86376270612588},{40.060991420194995,-60.49359842304146}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression crkD_min(
    y=crkD_min_speed)
    annotation (Placement(transformation(extent={{18.707730270954627,-108.17868056458367},{38.70773027095463,-88.17868056458367}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Add addD
    annotation (Placement(transformation(extent={{68.37590927865278,-102.17868056458367},{88.37590927865278,-82.17868056458367}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(addA.y,crkA_speed)
    annotation (Line(points={{89.37730332454105,99.98321789804777},{132.2773095466228,99.98321789804777},{132.2773095466228,99.78092573190531}},color={0,0,127}));
  connect(normalized_total_load,gainB.u)
    annotation (Line(points={{-87.20775456658987,24.617249320048465},{-32.851678408618284,24.617249320048465},{-32.851678408618284,61.733483529431105},{15.909957160255951,61.733483529431105}},color={0,0,127}));
  connect(normalized_total_load,gainA.u)
    annotation (Line(points={{-87.20775456658987,24.617249320048465},{-32.7520837046367,24.617249320048465},{-32.7520837046367,119.75120029151803},{16.986327583648013,119.75120029151803}},color={0,0,127}));
  connect(gainB.y,addB.u1)
    annotation (Line(points={{42.785655730106605,61.7334835294311},{49.932055730106605,61.7334835294311},{49.932055730106605,47.7334835294311},{67.93205573010661,47.7334835294311}},color={0,0,127}));
  connect(crkB_min.y,addB.u2)
    annotation (Line(points={{40.600234737804755,35.7334835294311},{67.93205573010661,35.7334835294311}},color={0,0,127}));
  connect(addB.y,crkB_speed)
    annotation (Line(points={{90.93205573010661,41.7334835294311},{129.9320557301066,41.7334835294311},{129.9320557301066,42.06530452173292}},color={0,0,127}));
  connect(gainC.u,normalized_total_load)
    annotation (Line(points={{15.593698722833034,-4.899277823085669},{-32.7174139555201,-4.899277823085669},{-32.7174139555201,24.617249320048465},{-87.20775456658987,24.617249320048465}},color={0,0,127}));
  connect(gainC.y,addC.u1)
    annotation (Line(points={{42.46939729268369,-4.899277823085669},{49.61579729268369,-4.899277823085669},{49.61579729268369,-18.89927782308567},{67.61579729268368,-18.89927782308567}},color={0,0,127}));
  connect(crkC_min.y,addC.u2)
    annotation (Line(points={{40.61579729268369,-30.89927782308567},{67.61579729268368,-30.89927782308567}},color={0,0,127}));
  connect(gainD.u,normalized_total_load)
    annotation (Line(points={{14.353810708802131,-72.17868056458367},{-32.63428865743971,-72.17868056458367},{-32.63428865743971,24.617249320048465},{-87.20775456658987,24.617249320048465}},color={0,0,127}));
  connect(gainD.y,addD.u1)
    annotation (Line(points={{41.22950927865279,-72.17868056458367},{48.37590927865279,-72.17868056458367},{48.37590927865279,-86.17868056458367},{66.37590927865278,-86.17868056458367}},color={0,0,127}));
  connect(addD.y,crkD_speed)
    annotation (Line(points={{89.37590927865278,-92.17868056458367},{128.37590927865278,-92.17868056458367}},color={0,0,127}));
  connect(addC.y,crkC_speed)
    annotation (Line(points={{90.61579729268368,-24.89927782308567},{129.61579729268368,-24.89927782308567}},color={0,0,127}));
  connect(crkD_min.y,addD.u2)
    annotation (Line(points={{39.70773027095463,-98.17868056458367},{66.37590927865278,-98.17868056458367}},color={0,0,127}));
  connect(gainA.y,addA.u1)
    annotation (Line(points={{43.8620165091951,119.75120029151803},{50.17182336827895,119.75120029151803},{50.17182336827895,105.98321789804777},{66.37730332454105,105.98321789804777}},color={0,0,127}));
  connect(crkA_min.y,addA.u2)
    annotation (Line(points={{41.65100981316827,93.86269009088788},{52.22193416505326,93.86269009088788},{52.22193416505326,93.98321789804777},{66.37730332454105,93.98321789804777}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=true,
        extent={{-100,-100},{100,100}}),
      graphics={
        Line(
          points={{100,60},{60,60},{0,0}},
          color={0,0,127}),
        Line(
          points={{100,-60},{60,-60},{0,0}},
          color={0,0,127}),
        Line(
          points={{-100,0},{0,0}},
          color={0,0,127}),
        Rectangle(
          extent={{-40,40},{40,-40}},
          lineColor={0,0,0},
          fillColor={235,235,235},
          fillPattern=FillPattern.Solid),
        Text(
          extent={{-100,140},{100,100}},
          lineColor={28,108,200},
          textString="%name")}));
end LoadDistribution_1;
