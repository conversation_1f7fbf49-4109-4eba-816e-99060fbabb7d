within Workspace.System.BaseCycle.Optimization;
model CL_500_900_2passes_opti
  extends.Workspace.System.BaseCycle.Optimization.System_XBV_opti(
    choiceBlock(
      FAN_SPEED_selector=.Workspace.Auxiliary.OptionBlock.FanSpeed.Selector.FAN_VS,
      SixtyHz_selector=.Workspace.Auxiliary.OptionBlock.SixtyHzOption.SixtyHz_selector.SixtyHz,
      Selector=.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Selector.Unit_600),
    isOffA(
      fixed=true),
    equipment(
      CompressorA(
        isDifferentRef=false,
        is_NG3=false),
      CompressorB(
        isLoopBreaker=true),
      nodecondAiroutB(
        dTsh_fixed=false),
      nodecondAiroutA(
        dTsh_fixed=false),
      condAirB(
        fastMode=true,
        use_Z_U_expression=true),
      globalParameters(
        capacity_design=900000),
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a,
      motorA(
        use_shaftSpeed_in=false),
      FanA(
        nStage=1),
      use_bf=true,
      use_en=true,
      condAirA(
        use_Z_U_expression=true),
      sourceAirA(
        Tdb_fixed=true),
      sourceAirB(
        Tdb_fixed=true)),
    UseStateMachine=true,
    controllerSettings_crkA(
      capacity_setpoint=targetCapacity,
      dT_sbc_max=5),
    controllerSettings_crkB(
      capacity_setpoint=targetCapacity,
      dT_sbc_max=5),
    controller(
      isOffSSTmin_comp=false,
      isOffDSHmin_EXV=false,
      isOffDGTmax_EXV=false,
      isOffSDTmax_comp=false,
      isOffDGTmax_comp=false,
      isOffDGTmax_fan=false,
      isOffSDTmax_fan=false,
      isOffSDTmin_fan=false,
      isOffSSTmax_EXV=false,
      isOffSSTmin_EXV=false),
    ECAT(
      RefrigerantType_nd=.BOLT.InternalLibrary.ECATBlock.Types.RefrigerantTypes.R134a,
      EvapBrineType_nd=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW,
      CondBrineType_nd=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW,
      EvapBrineLWT_K(
        setPoint=7+273.15),
      EvapBrineEWT_K(
        setPoint=12+273.15,
        fixed=true)));
  annotation (
    Icon(
      graphics={
        Text(
          textString="Text",
          origin={44,-30},
          extent={{0.6816829616425224,0.2303023726851734},{0,0}}),
        Text(
          textString="CL",
          extent={{-98,75},{98,-75}},
          lineColor={255,255,255},
          origin={0,-11})}));
end CL_500_900_2passes_opti;
