within Workspace.System.R134a.Surrogate;
model CL_30XBV_OnePasse_Calib
  extends.Workspace.System.BaseCycle.XBV_2C.Surrogate.System_30XBV(
    equipment(
      n_passes_evap=1),
    choiceBlock(
      HighAmbiant_selector=.Workspace.Auxiliary.OptionBlock.HighAmbiantTemperature.HighAmbiant_selector.HighAmbiant),
    ECAT(
      CondBrineFlowRate_m3s(
        value=1)),
    controllerSettings_crkA(
      dT_sbc_min=controllerSettings_crkA.dT_sbc_max),
    controllerSettings_crkB(
      dT_sbc_min=controllerSettings_crkB.dT_sbc_max))
    annotation (Icon(graphics={Text(textString="CL",origin={2,-12},extent={{-100,75},{100,-75}})}));
  annotation (
    Icon(
      graphics={
        Text(
          textString="CL",
          origin={-2,-10},
          extent={{-96.01685393258424,70.66502830561285},{97,-70}})}));
end CL_30XBV_OnePasse_Calib;
