within Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM;
record SurrogateInput
  package SI=Modelica.SIunits;
  parameter SI.MassFlowRate m_flow_in_ref=0
    annotation (Evaluate=false);
  parameter Real x_in=0
    annotation (Evaluate=false);
  parameter Real x_out=0
    annotation (Evaluate=false);
  parameter SI.VolumeFlowRate V_flow_in_air=0
    annotation (Evaluate=false);
  parameter SI.Temperature T_in_air=0
    annotation (Evaluate=false);
  parameter Real Z_dpr=0
    annotation (Evaluate=false);
  parameter Real Z_U=0
    annotation (Evaluate=false);
end SurrogateInput;
