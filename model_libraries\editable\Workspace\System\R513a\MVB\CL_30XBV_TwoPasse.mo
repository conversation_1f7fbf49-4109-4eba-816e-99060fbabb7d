within Workspace.System.R513a.MVB;
model CL_30XBV_TwoPasse
  extends.Workspace.System.BaseCycle.XBV_2C.MVB.System_30XBV(
    ModuleA(
      shaftPumpUser(
        streamID=1),
      c_load2_bf_pow_opt17A=0.16,
      c_load_bf_pow_opt17A=-0.16,
      bf_pow_min_opt17A=0.96,n_passes_evap = 2),
    ECAT(
      AmbientAirDBTemp_K(
        setPoint=273.15 + 35),
      EvapBrineLWT_K(
        setPoint=7+273.15),
      EvapBrineEWT_K(
        setPoint=12+273.15),
      ExternalSystemPressureDrop_Pa(
        setPoint=15000)),choice<PERSON>lock(MEPS_ME_selector = Workspace.Auxiliary.OptionBlock.MEPS_ME.MEPS_ME_selector.STANDARD,Selector_ModuleA = Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Selector.Unit_30XBV_0900,HighAmbiant_selector = Workspace.Auxiliary.OptionBlock.HighAmbiantTemperature.HighAmbiant_selector.HighAmbiant,SixtyHz_selector = Workspace.Auxiliary.OptionBlock.SixtyHzOption.SixtyHz_selector.SixtyHz),controllerSettings_crkA(SDT_max = 273.15 + (if controllerSettings_crkA.isHighAmbientOption then (if controllerSettings_crkA.is60HZ and controllerSettings_crkA.OAT_sdt_offset > (52 + 273.15) then 71 else 70) else 67)),controllerSettings_crkB(SDT_max = 273.15 + (if controllerSettings_crkB.isHighAmbientOption then (if controllerSettings_crkB.is60HZ and controllerSettings_crkB.OAT_sdt_offset > (52 + 273.15) then 71 else 70) else 67)),controller(completeCompressorControl_base(capacity_controller(AV_min = if StateMachine.currentModeID == Workspace.Controller.StateMachine.ModeID30KAV.CrkAB_1 then 0.06 else 0))),ECO_ON_inter = 12.036,ECO_ON_slope = 1.1607)
    annotation (Icon(graphics={Text(textString="CL",origin={2,-12},extent={{-100,75},{100,-75}})}));
  annotation (
    Icon(
      graphics={
        Text(
          textString="CL",
          origin={-2,-10},
          extent={{-96.01685393258424,70.66502830561285},{97,-70}})}));
end CL_30XBV_TwoPasse;
