within Workspace.Controller.SubSystems.Tests;
model test_CompleteCompressor
  parameter Modelica.SIunits.Power capacity_setpoint=1780000;
  .Workspace.Controller.SubSystems.Tests.test_CompleteCompressorControlBase_duplex test_CompleteCompressorControlBase_duplex(
    capacityDesign_moduleA_max=1000000,
    capacityDesign_moduleB_max=1000000,
    capacityDesign_moduleA_min=200000,
    capacityDesign_moduleB_min=200000,
    max_compressor_speed_moduleA_crkA=90,
    min_compressor_speed_moduleA_crkA=20,
    max_compressor_speed_moduleA_crkB=90,
    min_compressor_speed_moduleA_crkB=20,
    max_compressor_speed_moduleB_crkA=90,
    min_compressor_speed_moduleB_crkA=20,
    max_compressor_speed_moduleB_crkB=90,
    min_compressor_speed_moduleB_crkB=20)
    annotation (Placement(transformation(extent={{10.382489580081568,-17.617510419918432},{85.61751041991843,57.61751041991843}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression scA(
    y=-4.99998775875855)
    annotation (Placement(transformation(extent={{-281.63466762835174,242.76250652580984},{-259.57806415638606,264.8191099977755}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dshA(
    y=15.547403308551111)
    annotation (Placement(transformation(extent={{-281.63466762835174,262.61344965057896},{-259.57806415638606,284.67005312254463}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression eshA(
    y=9.999698856248642)
    annotation (Placement(transformation(extent={{-281.63466762835174,284.67005312254463},{-259.57806415638606,306.7266565945103}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sshA(
    y=1.1638616600903333)
    annotation (Placement(transformation(extent={{-281.63466762835174,304.5209962473138},{-259.57806415638606,326.5775997192795}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dgtA(
    y=345.81412729285097)
    annotation (Placement(transformation(extent={{-283.84032797554835,333.1945807608692},{-261.7837245035827,355.2511842328349}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sdtA(
    y=330.26672398429986)
    annotation (Placement(transformation(extent={{-283.84032797554835,350.83986353844176},{-261.7837245035827,372.89646701040743}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sstA(
    y=276.5981498519809)
    annotation (Placement(transformation(extent={{-283.84032797554835,370.6908066632109},{-261.7837245035827,392.74741013517655}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_levelB(
    y=0.97)
    annotation (Placement(transformation(extent={{38.1860827151509,392.74741013517655},{16.1294792431852,414.8040136071422}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sstB(
    y=276.5981498519809)
    annotation (Placement(transformation(extent={{38.1860827151509,372.89646701040743},{16.1294792431852,394.9530704823731}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sdtB(
    y=330.26672398429986)
    annotation (Placement(transformation(extent={{38.1860827151509,353.0455238856383},{16.1294792431852,375.102127357604}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dgtB(
    y=345.81412729285097)
    annotation (Placement(transformation(extent={{38.1860827151509,335.40024110806576},{16.1294792431852,357.45684458003143}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sshB(
    y=1.1638616600903333)
    annotation (Placement(transformation(extent={{41.02830173598285,306.97169826401716},{18.97169826401715,329.02830173598284}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression eshB(
    y=9.999698856248642)
    annotation (Placement(transformation(extent={{40.39174306234749,286.87571346974124},{18.335139590381786,308.9323169417069}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dshB(
    y=15.547403308551111)
    annotation (Placement(transformation(extent={{40.39174306234749,264.8191099977755},{18.335139590381786,286.8757134697412}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression scB(
    y=-4.99998775875855)
    annotation (Placement(transformation(extent={{40.39174306234749,244.9681668730064},{18.335139590381786,267.02477034497207}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression totalCapacity(
    y=881727.3961005812)
    annotation (Placement(transformation(extent={{-134.95825453977983,385.8547215501873},{-112.90165106781416,407.911325022153}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression oat(
    y=308.15)
    annotation (Placement(transformation(extent={{-134.95825453977983,370.4150991198113},{-112.90165106781416,392.471702591777}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ewt(
    y=285.15)
    annotation (Placement(transformation(extent={{-134.4068394529807,349.5991795931437},{-112.35023598101503,371.65578306510935}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression lwt(
    y=280.15)
    annotation (Placement(transformation(extent={{-134.95825453977983,330.713212870273},{-112.90165106781416,352.7698163422387}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus_moduleA_crkA
    annotation (Placement(transformation(extent={{-191.2025933932924,208.2990636008634},{-147.089386449361,252.41227054479475}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-55.913,90.783},{-15.913,130.783}})));
  .Workspace.Interfaces.MeasurementBus measurementBus_moduleA_crkB
    annotation (Placement(transformation(extent={{-93.05070794304501,209.40189377446166},{-48.93750099911361,253.51510071839311}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{18.087,90.783},{58.087,130.783}})));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_levelA(
    y=0.97)
    annotation (Placement(transformation(extent={{-284.9431581491466,390.26604224458043},{-262.8865546771809,412.3226457165461}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression totalCapacity2(
    y=881727.3961005812)
    annotation (Placement(transformation(extent={{245.0128109750549,387.84358329248244},{267.06941444702056,409.9001867644481}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression oat2(
    y=308.15)
    annotation (Placement(transformation(extent={{245.0128109750549,372.40396086210643},{267.06941444702056,394.4605643340721}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ewt2(
    y=285.15)
    annotation (Placement(transformation(extent={{245.56422606185401,351.5880413354388},{267.6208295338197,373.64464480740446}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression lwt2(
    y=280.15)
    annotation (Placement(transformation(extent={{245.0128109750549,332.70207461256814},{267.06941444702056,354.7586780845338}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_levelA2(
    y=0.97)
    annotation (Placement(transformation(extent={{95.02790736568815,392.25490398687555},{117.08451083765382,414.3115074588412}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_levelB2(
    y=0.97)
    annotation (Placement(transformation(extent={{418.15714822998564,394.73627187747167},{396.10054475801996,416.79287534943734}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sstB2(
    y=276.5981498519809)
    annotation (Placement(transformation(extent={{418.15714822998564,374.88532875270255},{396.10054475801996,396.9419322246682}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sstA2(
    y=276.5981498519809)
    annotation (Placement(transformation(extent={{96.1307375392864,372.679668405506},{118.18734101125207,394.73627187747167}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sdtA2(
    y=330.26672398429986)
    annotation (Placement(transformation(extent={{96.1307375392864,352.8287252807369},{118.18734101125207,374.88532875270255}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sdtB2(
    y=330.26672398429986)
    annotation (Placement(transformation(extent={{418.15714822998564,355.03438562793343},{396.10054475801996,377.0909890998991}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dgtA2(
    y=345.81412729285097)
    annotation (Placement(transformation(extent={{96.1307375392864,335.1834425031643},{118.18734101125207,357.24004597513}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dgtB2(
    y=345.81412729285097)
    annotation (Placement(transformation(extent={{418.15714822998564,337.38910285036087},{396.10054475801996,359.44570632232654}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sshA2(
    y=1.1638616600903333)
    annotation (Placement(transformation(extent={{98.33639788648301,306.5098579896089},{120.39300135844869,328.5664614615746}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sshB2(
    y=1.1638616600903333)
    annotation (Placement(transformation(extent={{420.9993672508176,308.9605600063123},{398.94276377885194,331.01716347827795}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression eshA2(
    y=9.999698856248642)
    annotation (Placement(transformation(extent={{98.33639788648301,286.65891486483974},{120.39300135844869,308.7155183368054}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression eshB2(
    y=9.999698856248642)
    annotation (Placement(transformation(extent={{420.36280857718225,288.86457521203636},{398.3062051052166,310.92117868400203}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dshA2(
    y=15.547403308551111)
    annotation (Placement(transformation(extent={{98.33639788648301,264.60231139287407},{120.39300135844869,286.65891486483974}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dshB2(
    y=15.547403308551111)
    annotation (Placement(transformation(extent={{420.36280857718225,266.8079717400706},{398.3062051052166,288.8645752120363}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression scA2(
    y=-4.99998775875855)
    annotation (Placement(transformation(extent={{98.33639788648301,244.75136826810495},{120.39300135844869,266.8079717400706}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression scB2(
    y=-4.99998775875855)
    annotation (Placement(transformation(extent={{420.36280857718225,246.9570286153015},{398.3062051052166,269.0136320872672}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus_moduleB_crkA
    annotation (Placement(transformation(extent={{188.76847212154235,210.28792534315852},{232.88167906547375,254.40113228708987}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-55.913,90.783},{-15.913,130.783}})));
  .Workspace.Interfaces.MeasurementBus measurementBus_moduleB_crkB
    annotation (Placement(transformation(extent={{286.92035757178974,211.39075551675677},{331.0335645157211,255.50396246068823}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{18.087,90.783},{58.087,130.783}})));
  .Modelica.Blocks.Sources.RealExpression T_sst_max_limit_block(
    y=295)
    annotation (Placement(transformation(extent={{-448.0181058203978,269.35996630262554},{-403.9818941796022,298.64003369737446}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_exv_block(
    y=273)
    annotation (Placement(transformation(extent={{-446.4962337748888,227.5522291421465},{-402.4600221340932,256.83229653689546}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_comp_block(
    y=367)
    annotation (Placement(transformation(extent={{-446.4962337748888,207.05618196582222},{-402.4600221340932,236.3362493605712}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_max_limit_comp_block(
    y=349)
    annotation (Placement(transformation(extent={{-446.4962337748888,145.20203959441503},{-402.4600221340932,174.482106989164}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_min_limit_block(
    y=293)
    annotation (Placement(transformation(extent={{-448.0181058203978,103.35996630262551},{-403.9818941796022,132.6400336973745}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_sbc_setpoint_block(
    y=-5)
    annotation (Placement(transformation(extent={{-446.4962337748888,82.24989469570473},{-402.4600221340932,111.52996209045371}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_esh_setpoint_block(
    y=10)
    annotation (Placement(transformation(extent={{-446.4962337748888,61.75384751938045},{-402.4600221340932,91.03391491412943}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_dsh_min_limit_block(
    y=7)
    annotation (Placement(transformation(extent={{-446.4962337748888,41.25780034305617},{-402.4600221340932,70.53786773780514}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression capacity_setpoint_block(
    y=capacity_setpoint)
    annotation (Placement(transformation(extent={{-446.4962337748888,20.761753166731886},{-402.4600221340932,50.041820561480854}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression fanSpeed_setpoint_block(
    y=50)
    annotation (Placement(transformation(extent={{-446.4962337748888,0.265705990407616},{-402.4600221340932,29.545773385156586}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_comp_block(
    y=273)
    annotation (Placement(transformation(extent={{-446.4962337748888,248.04827631847078},{-402.4600221340932,277.32834371321974}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_max_limit_fan_block(
    y=338)
    annotation (Placement(transformation(extent={{-446.0181058203978,125.35996630262551},{-401.9818941796022,154.6400336973745}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_exv_block(
    y=366)
    annotation (Placement(transformation(extent={{-446.4962337748888,186.56013478949794},{-402.4600221340932,215.84020218424692}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_fan_block(
    y=365)
    annotation (Placement(transformation(extent={{-446.4962337748888,166.06408761317365},{-402.4600221340932,195.34415500792264}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ecoEXV_max_opening_block(
    y=1.1)
    annotation (Placement(transformation(extent={{-446.4962337748888,-21.32834371321976},{-402.4600221340932,7.951723681529211}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_level_setpoint_block(
    y=0.97)
    annotation (Placement(transformation(extent={{-446.4962337748888,-41.82439088954404},{-402.4600221340932,-12.544323494795071}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_moduleA
    annotation (Placement(transformation(extent={{-381.280067394749,94.71993260525102},{-322.719932605251,153.28006739474898}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_max_limit_block2(
    y=295)
    annotation (Placement(transformation(extent={{-299.8390620503378,27.275868229776933},{-255.8028504095422,56.55593562452586}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_comp_block2(
    y=273)
    annotation (Placement(transformation(extent={{-298.31719000482883,5.964178245622207},{-254.28097836403322,35.24424564037113}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_exv_block2(
    y=273)
    annotation (Placement(transformation(extent={{-298.31719000482883,-14.531868930702075},{-254.28097836403322,14.748198464046851}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_comp_block2(
    y=367)
    annotation (Placement(transformation(extent={{-298.31719000482883,-35.027916107026385},{-254.28097836403322,-5.747848712277403}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_exv_block2(
    y=366)
    annotation (Placement(transformation(extent={{-298.31719000482883,-55.52396328335067},{-254.28097836403322,-26.243895888601685}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_fan_block2(
    y=365)
    annotation (Placement(transformation(extent={{-298.31719000482883,-76.02001045967495},{-254.28097836403322,-46.73994306492597}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_max_limit_comp_block2(
    y=349)
    annotation (Placement(transformation(extent={{-298.31719000482883,-96.88205847843358},{-254.28097836403322,-67.6019910836846}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_max_limit_fan_block2(
    y=338)
    annotation (Placement(transformation(extent={{-298.31719000482883,-117.37810565475786},{-254.28097836403322,-88.09803826000888}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_min_limit_block2(
    y=293)
    annotation (Placement(transformation(extent={{-299.8390620503378,-138.7241317702231},{-255.8028504095422,-109.44406437547411}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_sbc_setpoint_block2(
    y=-5)
    annotation (Placement(transformation(extent={{-298.31719000482883,-159.8342033771439},{-254.28097836403322,-130.5541359823949}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_esh_setpoint_block2(
    y=10)
    annotation (Placement(transformation(extent={{-298.31719000482883,-180.33025055346818},{-254.28097836403322,-151.0501831587192}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_dsh_min_limit_block2(
    y=7)
    annotation (Placement(transformation(extent={{-298.31719000482883,-200.82629772979246},{-254.28097836403322,-171.54623033504348}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression capacity_setpoint_block2(
    y=2e6)
    annotation (Placement(transformation(extent={{-298.31719000482883,-221.32234490611674},{-254.28097836403322,-192.04227751136776}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression fanSpeed_setpoint_block2(
    y=50)
    annotation (Placement(transformation(extent={{-298.31719000482883,-241.818392082441},{-254.28097836403322,-212.538324687692}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ecoEXV_max_opening_block2(
    y=1.1)
    annotation (Placement(transformation(extent={{-298.31719000482883,-263.41244178606837},{-254.28097836403322,-234.1323743913194}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_level_setpoint_block2(
    y=0.97)
    annotation (Placement(transformation(extent={{-298.31719000482883,-283.9084889623926},{-254.28097836403322,-254.62842156764364}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_moduleB
    annotation (Placement(transformation(extent={{-229.4509763528327,-149.22017894654738},{-170.89084156333473,-90.66004415704941}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(sstA.y,measurementBus_moduleA_crkA.T_sst)
    annotation (Line(points={{-260.68089432998437,381.7191083991936},{-238.62429085801867,381.7191083991936},{-238.62429085801867,230.35566707282896},{-169.1459899213267,230.35566707282896}},color={0,0,127}));
  connect(sdtA.y,measurementBus_moduleA_crkA.T_sdt)
    annotation (Line(points={{-260.68089432998437,361.8681652744244},{-238.62429085801867,361.8681652744244},{-238.62429085801867,230.35566707282896},{-169.1459899213267,230.35566707282896}},color={0,0,127}));
  connect(dgtA.y,measurementBus_moduleA_crkA.T_dgt)
    annotation (Line(points={{-260.68089432998437,344.22288249685187},{-238.62429085801867,344.22288249685187},{-238.62429085801867,230.35566707282896},{-169.1459899213267,230.35566707282896}},color={0,0,127}));
  connect(sshA.y,measurementBus_moduleA_crkA.dT_ssh)
    annotation (Line(points={{-258.4752339827878,315.54929798329647},{-238.62429085801867,315.54929798329647},{-238.62429085801867,230.35566707282896},{-169.1459899213267,230.35566707282896}},color={0,0,127}));
  connect(eshA.y,measurementBus_moduleA_crkA.dT_esh)
    annotation (Line(points={{-258.4752339827878,295.6983548585274},{-238.62429085801867,295.6983548585274},{-238.62429085801867,230.35566707282896},{-169.1459899213267,230.35566707282896}},color={0,0,127}));
  connect(dshA.y,measurementBus_moduleA_crkA.dT_dsh)
    annotation (Line(points={{-258.4752339827878,273.6417513865616},{-238.62429085801867,273.6417513865616},{-238.62429085801867,230.35566707282896},{-169.1459899213267,230.35566707282896}},color={0,0,127}));
  connect(scA.y,measurementBus_moduleA_crkA.dT_sbc)
    annotation (Line(points={{-258.4752339827878,253.79080826179256},{-238.62429085801867,253.79080826179256},{-238.62429085801867,230.35566707282896},{-169.1459899213267,230.35566707282896}},color={0,0,127}));
  connect(lwt.y,measurementBus_moduleA_crkA.T_lwt)
    annotation (Line(points={{-111.79882089421591,341.7415146062558},{-148.19221662295928,341.7415146062558},{-148.19221662295928,230.35566707282896},{-169.1459899213267,230.35566707282896}},color={0,0,127}));
  connect(ewt.y,measurementBus_moduleA_crkA.T_ewt)
    annotation (Line(points={{-111.24740580741673,360.62748132912634},{-148.19221662295928,360.62748132912634},{-148.19221662295928,230.35566707282896},{-169.1459899213267,230.35566707282896}},color={0,0,127}));
  connect(oat.y,measurementBus_moduleA_crkA.T_oat)
    annotation (Line(points={{-111.79882089421591,381.44340085579404},{-148.19221662295928,381.44340085579404},{-148.19221662295928,230.35566707282896},{-169.1459899213267,230.35566707282896}},color={0,0,127}));
  connect(totalCapacity.y,measurementBus_moduleA_crkA.capacity)
    annotation (Line(points={{-111.79882089421591,396.88302328617},{-148.19221662295928,396.88302328617},{-148.19221662295928,230.35566707282896},{-169.1459899213267,230.35566707282896}},color={0,0,127}));
  connect(rel_cooler_levelA.y,measurementBus_moduleA_crkA.rel_cooler_level)
    annotation (Line(points={{-261.7837245035827,401.2943439805631},{-238.62429085801867,401.2943439805631},{-238.62429085801867,230.35566707282896},{-169.1459899213267,230.35566707282896}},color={0,0,127}));
  connect(lwt.y,measurementBus_moduleA_crkB.T_lwt)
    annotation (Line(points={{-111.79882089421591,341.7415146062558},{-95.2563682902416,341.7415146062558},{-95.2563682902416,231.45849724642727},{-70.99410447107931,231.45849724642727}},color={0,0,127}));
  connect(ewt.y,measurementBus_moduleA_crkB.T_ewt)
    annotation (Line(points={{-111.24740580741673,360.62748132912634},{-95.2563682902416,360.62748132912634},{-95.2563682902416,231.45849724642727},{-70.99410447107931,231.45849724642727}},color={0,0,127}));
  connect(oat.y,measurementBus_moduleA_crkB.T_oat)
    annotation (Line(points={{-111.79882089421591,381.44340085579404},{-95.2563682902416,381.44340085579404},{-95.2563682902416,231.45849724642727},{-70.99410447107931,231.45849724642727}},color={0,0,127}));
  connect(totalCapacity.y,measurementBus_moduleA_crkB.capacity)
    annotation (Line(points={{-111.79882089421591,396.88302328617},{-95.2563682902416,396.88302328617},{-95.2563682902416,231.45849724642727},{-70.99410447107931,231.45849724642727}},color={0,0,127}));
  connect(scB.y,measurementBus_moduleA_crkB.dT_sbc)
    annotation (Line(points={{17.232309416783494,255.99646860898906},{-0.4129733607890671,255.99646860898906},{-0.4129733607890671,231.45849724642727},{-70.99410447107931,231.45849724642727}},color={0,0,127}));
  connect(dshB.y,measurementBus_moduleA_crkB.dT_dsh)
    annotation (Line(points={{17.232309416783494,275.84741173375824},{-0.4129733607890671,275.84741173375824},{-0.4129733607890671,231.45849724642727},{-70.99410447107931,231.45849724642727}},color={0,0,127}));
  connect(eshB.y,measurementBus_moduleA_crkB.dT_esh)
    annotation (Line(points={{17.232309416783494,297.9040152057239},{-0.4129733607890671,297.9040152057239},{-0.4129733607890671,231.45849724642727},{-70.99410447107931,231.45849724642727}},color={0,0,127}));
  connect(sshB.y,measurementBus_moduleA_crkB.dT_ssh)
    annotation (Line(points={{17.868868090418857,317.99999999999994},{-0.4129733607890671,317.99999999999994},{-0.4129733607890671,231.4584972464272},{-70.99410447107931,231.4584972464272}},color={0,0,127}));
  connect(dgtB.y,measurementBus_moduleA_crkB.T_dgt)
    annotation (Line(points={{15.026649069586881,346.4285428440485},{-0.4129733607890671,346.4285428440485},{-0.4129733607890671,231.45849724642727},{-70.99410447107931,231.45849724642727}},color={0,0,127}));
  connect(sdtB.y,measurementBus_moduleA_crkB.T_sdt)
    annotation (Line(points={{15.026649069586881,364.07382562162104},{-0.4129733607890671,364.07382562162104},{-0.4129733607890671,231.45849724642727},{-70.99410447107931,231.45849724642727}},color={0,0,127}));
  connect(sstB.y,measurementBus_moduleA_crkB.T_sst)
    annotation (Line(points={{15.026649069586881,383.9247687463902},{-0.4129733607890671,383.9247687463902},{-0.4129733607890671,231.45849724642727},{-70.99410447107931,231.45849724642727}},color={0,0,127}));
  connect(rel_cooler_levelB.y,measurementBus_moduleA_crkB.rel_cooler_level)
    annotation (Line(points={{15.026649069586881,403.7757118711593},{-0.4129733607890671,403.7757118711593},{-0.4129733607890671,231.45849724642727},{-70.99410447107931,231.45849724642727}},color={0,0,127}));
  connect(sstA2.y,measurementBus_moduleB_crkA.T_sst)
    annotation (Line(points={{119.29017118485041,383.7079701414887},{141.3467746568161,383.7079701414887},{141.3467746568161,232.34452881512408},{210.82507559350807,232.34452881512408}},color={0,0,127}));
  connect(sdtA2.y,measurementBus_moduleB_crkA.T_sdt)
    annotation (Line(points={{119.29017118485041,363.85702701671954},{141.3467746568161,363.85702701671954},{141.3467746568161,232.34452881512408},{210.82507559350807,232.34452881512408}},color={0,0,127}));
  connect(dgtA2.y,measurementBus_moduleB_crkA.T_dgt)
    annotation (Line(points={{119.29017118485041,346.211744239147},{141.3467746568161,346.211744239147},{141.3467746568161,232.34452881512408},{210.82507559350807,232.34452881512408}},color={0,0,127}));
  connect(sshA2.y,measurementBus_moduleB_crkA.dT_ssh)
    annotation (Line(points={{121.49583153204696,317.5381597255916},{141.3467746568161,317.5381597255916},{141.3467746568161,232.34452881512408},{210.82507559350807,232.34452881512408}},color={0,0,127}));
  connect(eshA2.y,measurementBus_moduleB_crkA.dT_esh)
    annotation (Line(points={{121.49583153204696,297.6872166008225},{141.3467746568161,297.6872166008225},{141.3467746568161,232.34452881512408},{210.82507559350807,232.34452881512408}},color={0,0,127}));
  connect(dshA2.y,measurementBus_moduleB_crkA.dT_dsh)
    annotation (Line(points={{121.49583153204696,275.63061312885674},{141.3467746568161,275.63061312885674},{141.3467746568161,232.34452881512408},{210.82507559350807,232.34452881512408}},color={0,0,127}));
  connect(scA2.y,measurementBus_moduleB_crkA.dT_sbc)
    annotation (Line(points={{121.49583153204696,255.77967000408768},{141.3467746568161,255.77967000408768},{141.3467746568161,232.34452881512408},{210.82507559350807,232.34452881512408}},color={0,0,127}));
  connect(lwt2.y,measurementBus_moduleB_crkA.T_lwt)
    annotation (Line(points={{268.17224462061887,343.7303763485509},{231.7788488918755,343.7303763485509},{231.7788488918755,232.34452881512408},{210.82507559350807,232.34452881512408}},color={0,0,127}));
  connect(ewt2.y,measurementBus_moduleB_crkA.T_ewt)
    annotation (Line(points={{268.72365970741805,362.61634307142145},{231.7788488918755,362.61634307142145},{231.7788488918755,232.34452881512408},{210.82507559350807,232.34452881512408}},color={0,0,127}));
  connect(oat2.y,measurementBus_moduleB_crkA.T_oat)
    annotation (Line(points={{268.17224462061887,383.43226259808915},{231.7788488918755,383.43226259808915},{231.7788488918755,232.34452881512408},{210.82507559350807,232.34452881512408}},color={0,0,127}));
  connect(totalCapacity2.y,measurementBus_moduleB_crkA.capacity)
    annotation (Line(points={{268.17224462061887,398.8718850284651},{231.7788488918755,398.8718850284651},{231.7788488918755,232.34452881512408},{210.82507559350807,232.34452881512408}},color={0,0,127}));
  connect(rel_cooler_levelA2.y,measurementBus_moduleB_crkA.rel_cooler_level)
    annotation (Line(points={{118.1873410112521,403.2832057228582},{141.3467746568161,403.2832057228582},{141.3467746568161,232.34452881512408},{210.82507559350807,232.34452881512408}},color={0,0,127}));
  connect(lwt2.y,measurementBus_moduleB_crkB.T_lwt)
    annotation (Line(points={{268.17224462061887,343.7303763485509},{284.7146972245932,343.7303763485509},{284.7146972245932,233.44735898872239},{308.97696104375547,233.44735898872239}},color={0,0,127}));
  connect(ewt2.y,measurementBus_moduleB_crkB.T_ewt)
    annotation (Line(points={{268.72365970741805,362.61634307142145},{284.7146972245932,362.61634307142145},{284.7146972245932,233.44735898872239},{308.97696104375547,233.44735898872239}},color={0,0,127}));
  connect(oat2.y,measurementBus_moduleB_crkB.T_oat)
    annotation (Line(points={{268.17224462061887,383.43226259808915},{284.7146972245932,383.43226259808915},{284.7146972245932,233.44735898872239},{308.97696104375547,233.44735898872239}},color={0,0,127}));
  connect(totalCapacity2.y,measurementBus_moduleB_crkB.capacity)
    annotation (Line(points={{268.17224462061887,398.8718850284651},{284.7146972245932,398.8718850284651},{284.7146972245932,233.44735898872239},{308.97696104375547,233.44735898872239}},color={0,0,127}));
  connect(scB2.y,measurementBus_moduleB_crkB.dT_sbc)
    annotation (Line(points={{397.20337493161827,257.9853303512842},{379.5580921540457,257.9853303512842},{379.5580921540457,233.44735898872239},{308.97696104375547,233.44735898872239}},color={0,0,127}));
  connect(dshB2.y,measurementBus_moduleB_crkB.dT_dsh)
    annotation (Line(points={{397.20337493161827,277.83627347605335},{379.5580921540457,277.83627347605335},{379.5580921540457,233.44735898872239},{308.97696104375547,233.44735898872239}},color={0,0,127}));
  connect(eshB2.y,measurementBus_moduleB_crkB.dT_esh)
    annotation (Line(points={{397.20337493161827,299.892876948019},{379.5580921540457,299.892876948019},{379.5580921540457,233.44735898872239},{308.97696104375547,233.44735898872239}},color={0,0,127}));
  connect(sshB2.y,measurementBus_moduleB_crkB.dT_ssh)
    annotation (Line(points={{397.83993360525363,319.98886174229506},{379.5580921540457,319.98886174229506},{379.5580921540457,233.44735898872233},{308.97696104375547,233.44735898872233}},color={0,0,127}));
  connect(dgtB2.y,measurementBus_moduleB_crkB.T_dgt)
    annotation (Line(points={{394.99771458442166,348.4174045863436},{379.5580921540457,348.4174045863436},{379.5580921540457,233.44735898872239},{308.97696104375547,233.44735898872239}},color={0,0,127}));
  connect(sdtB2.y,measurementBus_moduleB_crkB.T_sdt)
    annotation (Line(points={{394.99771458442166,366.06268736391615},{379.5580921540457,366.06268736391615},{379.5580921540457,233.44735898872239},{308.97696104375547,233.44735898872239}},color={0,0,127}));
  connect(sstB2.y,measurementBus_moduleB_crkB.T_sst)
    annotation (Line(points={{394.99771458442166,385.9136304886853},{379.5580921540457,385.9136304886853},{379.5580921540457,233.44735898872239},{308.97696104375547,233.44735898872239}},color={0,0,127}));
  connect(rel_cooler_levelB2.y,measurementBus_moduleB_crkB.rel_cooler_level)
    annotation (Line(points={{394.99771458442166,405.7645736134544},{379.5580921540457,405.7645736134544},{379.5580921540457,233.44735898872239},{308.97696104375547,233.44735898872239}},color={0,0,127}));
  connect(T_sst_max_limit_block.y,limitsBus_moduleA.T_sst_max_limit)
    annotation (Line(points={{-401.7800835975624,284},{-352,284},{-352,124}},color={0,0,127}));
  connect(T_sst_min_limit_comp_block.y,limitsBus_moduleA.T_sst_min_limit_comp)
    annotation (Line(points={{-400.25821155205347,262.6883100158453},{-352,262.6883100158453},{-352,124}},color={0,0,127}));
  connect(T_sst_min_limit_exv_block.y,limitsBus_moduleA.T_sst_min_limit_exv)
    annotation (Line(points={{-400.25821155205347,242.192262839521},{-352,242.192262839521},{-352,124}},color={0,0,127}));
  connect(T_dgt_max_limit_comp_block.y,limitsBus_moduleA.T_dgt_max_limit_comp)
    annotation (Line(points={{-400.25821155205347,221.6962156631967},{-352,221.6962156631967},{-352,124}},color={0,0,127}));
  connect(T_dgt_max_limit_exv_block.y,limitsBus_moduleA.T_dgt_max_limit_exv)
    annotation (Line(points={{-400.25821155205347,201.20016848687243},{-352,201.20016848687243},{-352,124}},color={0,0,127}));
  connect(T_dgt_max_limit_fan_block.y,limitsBus_moduleA.T_dgt_max_limit_fan)
    annotation (Line(points={{-400.25821155205347,180.70412131054815},{-352,180.70412131054815},{-352,124}},color={0,0,127}));
  connect(T_sdt_max_limit_comp_block.y,limitsBus_moduleA.T_sdt_max_limit_comp)
    annotation (Line(points={{-400.25821155205347,159.84207329178952},{-352,159.84207329178952},{-352,124}},color={0,0,127}));
  connect(T_sdt_max_limit_fan_block.y,limitsBus_moduleA.T_sdt_max_limit_fan)
    annotation (Line(points={{-399.7800835975624,140},{-352,140},{-352,124}},color={0,0,127}));
  connect(T_sdt_min_limit_block.y,limitsBus_moduleA.T_sdt_min_limit)
    annotation (Line(points={{-401.7800835975624,118},{-352,118},{-352,124}},color={0,0,127}));
  connect(dT_sbc_setpoint_block.y,limitsBus_moduleA.dT_sbc_setpoint)
    annotation (Line(points={{-400.25821155205347,96.88992839307923},{-352,96.88992839307923},{-352,124}},color={0,0,127}));
  connect(dT_esh_setpoint_block.y,limitsBus_moduleA.dT_esh_setpoint)
    annotation (Line(points={{-400.25821155205347,76.39388121675495},{-352,76.39388121675495},{-352,124}},color={0,0,127}));
  connect(dT_dsh_min_limit_block.y,limitsBus_moduleA.dT_dsh_min_limt)
    annotation (Line(points={{-400.25821155205347,55.89783404043066},{-352,55.89783404043066},{-352,124}},color={0,0,127}));
  connect(capacity_setpoint_block.y,limitsBus_moduleA.capacity_setpoint)
    annotation (Line(points={{-400.25821155205347,35.401786864106384},{-352,35.401786864106384},{-352,124}},color={0,0,127}));
  connect(fanSpeed_setpoint_block.y,limitsBus_moduleA.fanSpeed_setpoint)
    annotation (Line(points={{-400.25821155205347,14.905739687782102},{-352,14.905739687782102},{-352,124}},color={0,0,127}));
  connect(ecoEXV_max_opening_block.y,limitsBus_moduleA.ecoEXV_max_opening)
    annotation (Line(points={{-400.25821155205347,-6.688310015845268},{-352,-6.688310015845268},{-352,124}},color={0,0,127}));
  connect(rel_cooler_level_setpoint_block.y,limitsBus_moduleA.rel_cooler_level_setpoint)
    annotation (Line(points={{-400.25821155205347,-27.184357192169543},{-352,-27.184357192169543},{-352,124}},color={0,0,127}));
  connect(T_sst_max_limit_block2.y,limitsBus_moduleB.T_sst_max_limit)
    annotation (Line(points={{-253.6010398275024,41.91590192715137},{-253.6010398275024,48},{-200.17090895808366,48},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(T_sst_min_limit_comp_block2.y,limitsBus_moduleB.T_sst_min_limit_comp)
    annotation (Line(points={{-252.07916778199348,20.604211942996642},{-200.17090895808366,20.604211942996642},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(T_sst_min_limit_exv_block2.y,limitsBus_moduleB.T_sst_min_limit_exv)
    annotation (Line(points={{-252.07916778199348,0.10816476667235975},{-200.17090895808366,0.10816476667235975},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(T_dgt_max_limit_comp_block2.y,limitsBus_moduleB.T_dgt_max_limit_comp)
    annotation (Line(points={{-252.07916778199348,-20.387882409651922},{-200.17090895808366,-20.387882409651922},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(T_dgt_max_limit_exv_block2.y,limitsBus_moduleB.T_dgt_max_limit_exv)
    annotation (Line(points={{-252.07916778199348,-40.883929585976205},{-200.17090895808366,-40.883929585976205},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(T_dgt_max_limit_fan_block2.y,limitsBus_moduleB.T_dgt_max_limit_fan)
    annotation (Line(points={{-252.07916778199348,-61.37997676230049},{-200.17090895808366,-61.37997676230049},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(T_sdt_max_limit_comp_block2.y,limitsBus_moduleB.T_sdt_max_limit_comp)
    annotation (Line(points={{-252.07916778199348,-82.24202478105911},{-200.17090895808366,-82.24202478105911},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(T_sdt_max_limit_fan_block2.y,limitsBus_moduleB.T_sdt_max_limit_fan)
    annotation (Line(points={{-252.07916778199348,-102.7380719573834},{-200.17090895808366,-102.7380719573834},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(T_sdt_min_limit_block2.y,limitsBus_moduleB.T_sdt_min_limit)
    annotation (Line(points={{-253.6010398275024,-124.08409807284863},{-200.17090895808366,-124.08409807284863},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(dT_sbc_setpoint_block2.y,limitsBus_moduleB.dT_sbc_setpoint)
    annotation (Line(points={{-252.07916778199348,-145.1941696797694},{-200.17090895808366,-145.1941696797694},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(dT_esh_setpoint_block2.y,limitsBus_moduleB.dT_esh_setpoint)
    annotation (Line(points={{-252.07916778199348,-165.69021685609368},{-200.17090895808366,-165.69021685609368},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(dT_dsh_min_limit_block2.y,limitsBus_moduleB.dT_dsh_min_limt)
    annotation (Line(points={{-252.07916778199348,-186.18626403241797},{-200.17090895808366,-186.18626403241797},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(capacity_setpoint_block2.y,limitsBus_moduleB.capacity_setpoint)
    annotation (Line(points={{-252.07916778199348,-206.68231120874225},{-200.17090895808366,-206.68231120874225},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(fanSpeed_setpoint_block2.y,limitsBus_moduleB.fanSpeed_setpoint)
    annotation (Line(points={{-252.07916778199348,-227.17835838506653},{-200.17090895808366,-227.17835838506653},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(ecoEXV_max_opening_block2.y,limitsBus_moduleB.ecoEXV_max_opening)
    annotation (Line(points={{-252.07916778199348,-248.7724080886939},{-200.17090895808366,-248.7724080886939},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(rel_cooler_level_setpoint_block2.y,limitsBus_moduleB.rel_cooler_level_setpoint)
    annotation (Line(points={{-252.07916778199348,-269.2684552650182},{-200.17090895808366,-269.2684552650182},{-200.17090895808366,-119.94011155179842}},color={0,0,127}));
  connect(test_CompleteCompressorControlBase_duplex.measurementBus_moduleA_crkA,measurementBus_moduleA_crkA)
    annotation (Line(points={{3.6113377044962505,44.827556877146165},{3.6113377044962505,92},{-169.1459899213267,92},{-169.1459899213267,230.35566707282908}},color={255,204,51}));
  connect(test_CompleteCompressorControlBase_duplex.measurementBus_moduleB_crkA,measurementBus_moduleB_crkA)
    annotation (Line(points={{-7.673915421479279,44.827556877146165},{-42,44.827556877146165},{-42,172},{210.82507559350805,172},{210.82507559350805,232.3445288151242}},color={255,204,51}));
  connect(test_CompleteCompressorControlBase_duplex.measurementBus_moduleB_crkB,measurementBus_moduleB_crkB)
    annotation (Line(points={{-7.673915421479279,6.4576962488293645},{-7.673915421479279,-120},{314,-120},{314,233.4473589887225},{308.9769610437554,233.4473589887225}},color={255,204,51}));
  connect(test_CompleteCompressorControlBase_duplex.measurementBus_moduleA_crkB,measurementBus_moduleA_crkB)
    annotation (Line(points={{3.6113377044962505,6.4576962488293645},{-70.99410447107931,6.4576962488293645},{-70.99410447107931,231.45849724642738}},color={255,204,51}));
  connect(test_CompleteCompressorControlBase_duplex.limitsBus_moduleA_crkA,limitsBus_moduleA)
    annotation (Line(points={{3.6113377044962576,32.789953542772274},{3.6113377044962576,60},{-190,60},{-190,124},{-352,124}},color={255,204,51}));
  connect(test_CompleteCompressorControlBase_duplex.limitsBus_moduleA_crkB,limitsBus_moduleA)
    annotation (Line(points={{3.6113377044962505,-5.579907085544534},{3.6113377044962505,-26},{-174.19433114775188,-26},{-174.19433114775188,124},{-352,124}},color={255,204,51}));
  connect(test_CompleteCompressorControlBase_duplex.limitsBus_moduleB_crkA,limitsBus_moduleB)
    annotation (Line(points={{-7.673915421479279,32.789953542772274},{-103.9224121897815,32.789953542772274},{-103.9224121897815,-119.9401115517984},{-200.17090895808371,-119.9401115517984}},color={255,204,51}));
  connect(test_CompleteCompressorControlBase_duplex.limitsBus_moduleB_crkB,limitsBus_moduleB)
    annotation (Line(points={{-7.673915421479279,-5.579907085544534},{-7.673915421479279,-62.760009318671464},{-200.17090895808371,-62.760009318671464},{-200.17090895808371,-119.9401115517984}},color={255,204,51}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end test_CompleteCompressor;
