within KAV_initiation.System30KAV.Controller30KAV.SubSystems.Tests.EXVControl;
model EXVControl_T_sst_max
  "Test override triggered by changing T_sst"
  extends BaseClasses.EXVControlBaseTest(
    T_sst(
      height=0,
      offset=290.15-0.1),
    eXVControl());
  .Modelica.Blocks.Sources.RealExpression rel_cooler_level_limit(
    y=0.97)
    annotation (Placement(transformation(extent={{-95.83332112552287,-77.83332112552284},{-76.16667887447713,-58.16667887447715}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_level(
    y=0.97)
    annotation (Placement(transformation(extent={{-97.83332112552287,156.16667887447716},{-78.16667887447713,175.83332112552284}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal2
    annotation (Placement(transformation(extent={{96.0,-8.0},{116.0,12.0}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(rel_cooler_level.y,eXVControl.measurementBus.rel_cooler_level)
    annotation (Line(points={{-77.18334676192484,166},{-40,166},{-40,41.90728105597978},{-27.178801759966298,41.90728105597978}},color={0,0,127}));
  connect(rel_cooler_level_limit.y,eXVControl.limitsBus.rel_cooler_level_setpoint)
    annotation (Line(points={{-75.18334676192484,-68},{-40,-68},{-40,2.09271894402022},{-27.178801759966298,2.09271894402022}},color={0,0,127}));
  connect(eXVControl.actuatorSignal_sst,actuatorSignal2)
    annotation (Line(points={{39.1788017599663,16.027815683206065},{72.58940087998315,16.027815683206065},{72.58940087998315,2},{106,2}},color={0,0,127}));
end EXVControl_T_sst_max;
