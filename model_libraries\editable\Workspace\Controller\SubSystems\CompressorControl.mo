within KAV_initiation.System30KAV.Controller30KAV.SubSystems;
model CompressorControl
  extends.Workspace.Controller.SubSystems.BaseClasses.CompressorControlBase;
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_sst_min_error(
    measurement=T_sst,
    setPoint=T_sst_min_limit_comp,
    ID=2,
    gain=-1/273.15,
    isOff=isOffSSTmin)
    annotation (Placement(transformation(extent={{-86.8338512066936,-2.0},{-32.068143842873724,18.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation compressorFrequency_error(
    ID=1,
    measurement=actuatorSignal,
    gain=1/compressorFrequency_max,
    setPoint=compressorFrequency)
    annotation (Placement(transformation(extent={{-87.38285368190995,14.0},{-32.61714631809006,34.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min
    annotation (Placement(transformation(extent={{-21.398169803474865,8.935163529858464},{-3.2684968631917926,27.064836470141536}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min2
    annotation (Placement(transformation(extent={{5.514529830284642,6.347863163617969},{20.8188035030487,21.65213683638203}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min3
    annotation (Placement(transformation(extent={{32.55964110971273,2.5596411097127216},{47.44035889028727,17.44035889028728}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController(
    AV_min=compressorFrequency_min,
    AV_max=compressorFrequency_max,
    AV_start=compressorFrequency_max,
    isOff=crkIsOff)
    annotation (Placement(transformation(extent={{60.0,-10.0},{80.0,10.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean isOffSSTmin=false;
  parameter Boolean isOffSDTmax=false;
  parameter Boolean isOffDGTmax=false;
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_sdt_max_error(
    measurement=T_sdt,
    setPoint=T_sdt_max_limit_comp,
    ID=3,
    gain=1/340.15,
    isOff=isOffSDTmax)
    annotation (Placement(transformation(extent={{-87.73250257782469,-18.0},{-32.758145078414344,2.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_dgt_max_error(
    measurement=T_dgt,
    setPoint=T_dgt_max_limit_comp,
    ID=4,
    gain=1/367.15,
    isOff=isOffDGTmax)
    annotation (Placement(transformation(extent={{-87.23911697031966,-34.24929055358682},{-32.877592667635,-14.249290553586818}},origin={0.0,0.0},rotation=0.0)));
protected
  .Modelica.Blocks.Interfaces.RealInput T_sst_min_limit_comp
    annotation (Placement(transformation(extent={{-82.68216823968166,-70.68216823968166},{-61.31783176031834,-49.31783176031834}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput T_sdt_max_limit_comp
    annotation (Placement(transformation(extent={{-82.68216823968166,-86.68216823968166},{-61.31783176031834,-65.31783176031834}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput T_dgt_max_limit_comp
    annotation (Placement(transformation(extent={{-82.68216823968166,-102.68216823968166},{-61.31783176031834,-81.31783176031834}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput T_sst
    annotation (Placement(transformation(extent={{-82.68216823968166,81.31783176031834},{-61.31783176031834,102.68216823968166}},origin={0,-2},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput T_sdt
    annotation (Placement(transformation(extent={{-82.68216823968166,65.31783176031834},{-61.31783176031834,86.68216823968166}},origin={0,-2},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput T_dgt
    annotation (Placement(transformation(extent={{-82.68216823968166,49.31783176031834},{-61.31783176031834,70.68216823968166}},origin={0,-2},rotation=0.0)));
equation
  connect(T_sst,measurementBus.T_sst)
    annotation (Line(points={{-72,90},{-99.8967,90},{-99.8967,60.1033}},color={0,0,127}));
  connect(T_sdt,measurementBus.T_sdt)
    annotation (Line(points={{-72,74},{-99.8967,74},{-99.8967,60.1033}},color={0,0,127}));
  connect(T_dgt,measurementBus.T_dgt)
    annotation (Line(points={{-72,58},{-94,58},{-94,60.1033},{-99.8967,60.1033}},color={0,0,127}));
  connect(compressorFrequency_error.sensor,min.u1)
    annotation (Line(points={{-33.164803391728256,23.2},{-21.398169803474865,23.2},{-21.398169803474865,23.07630842327926}},color={28,108,200}));
  connect(T_sst_min_error.sensor,min.u2)
    annotation (Line(points={{-32.61580091651192,7.2},{-26,7.2},{-26,12.561098117915078},{-21.398169803474865,12.561098117915078}},color={28,108,200}));
  connect(min.y,min2.u1)
    annotation (Line(points={{-3.2684968631917943,18},{5.5145266666666695,18},{5.5145266666666695,18.2852}},color={28,108,200}));
  connect(min2.y,min3.u1)
    annotation (Line(points={{20.8188,14},{20.8188,14.166600978560876},{32.55964110971273,14.166600978560876}},color={28,108,200}));
  connect(min3.y,setpointController.errorSignal)
    annotation (Line(points={{47.44035889028727,10},{54,10},{54,0},{60,0}},color={28,108,200}));
  connect(setpointController.actuatorSignal,actuatorSignal)
    annotation (Line(points={{80.6,0},{100,0}},color={0,0,127}));
  connect(T_sst_min_limit_comp,limitsBus.T_sst_min_limit_comp)
    annotation (Line(points={{-72,-60},{-86,-60},{-86,-59.95},{-99.95,-59.95}},color={0,0,127}));
  connect(T_sdt_max_limit_comp,limitsBus.T_sdt_max_limit_comp)
    annotation (Line(points={{-72,-76},{-99.95,-76},{-99.95,-59.95}},color={0,0,127}));
  connect(T_dgt_max_limit_comp,limitsBus.T_dgt_max_limit_comp)
    annotation (Line(points={{-72,-92},{-99.95,-92},{-99.95,-59.95}},color={0,0,127}));
  connect(T_sdt_max_error.sensor,min2.u2)
    annotation (Line(points={{-33.30788865340845,-8.8},{0,-8.8},{0,9.408717898170782},{5.514529830284642,9.408717898170782}},color={28,108,200}));
  connect(min3.u2,T_dgt_max_error.sensor)
    annotation (Line(points={{32.55964110971273,5.535784665827633},{28,5.535784665827633},{28,-25.049290553586818},{-33.42120791066184,-25.049290553586818}},color={28,108,200}));
end CompressorControl;
