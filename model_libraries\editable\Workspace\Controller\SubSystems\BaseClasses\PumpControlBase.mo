within Workspace.System.XF_MTL.Controller.Subsystems.BaseClasses;
model PumpControlBase
  parameter Boolean crkIsOff=false
    "Specify whether the controller is on or off";
  .Modelica.Blocks.Interfaces.RealOutput ActuatorSignal
    annotation (Placement(transformation(extent={{126,8},{146,28}},origin={0,0},rotation=0)));
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-110.00294523682533,23.997054763174674},{-13.997054763174674,120.00294523682533}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus
    annotation (Placement(transformation(extent={{-105.5080928277033,-73.5080928277033},{-14.491907172296706,17.508092827703294}},origin={0.0,0.0},rotation=0.0)));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end PumpControlBase;
