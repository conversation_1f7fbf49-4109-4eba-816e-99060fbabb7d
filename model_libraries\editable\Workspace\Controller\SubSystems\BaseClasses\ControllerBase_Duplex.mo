within Workspace.Controller.SubSystems.BaseClasses;
partial model ControllerBase_Duplex
  extends.BOLT.InternalLibrary.BuildingBlocks.Icons.ControllerPackage;
  parameter Boolean isOff_moduleA_crkA=false;
  parameter Boolean isOff_moduleA_crkB=false;
  parameter Boolean isOff_moduleB_crkA=false;
  parameter Boolean isOff_moduleB_crkB=false;
  parameter Boolean isOff_ecoA_moduleA=false;
  parameter Boolean isOff_ecoB_moduleA=false;
  parameter Boolean isOff_ecoA_moduleB=false;
  parameter Boolean isOff_ecoB_moduleB=false;
  parameter Real max_compressor_speed_moduleA_crkA
    "Maximum speed of compressor module A (Hz)"
    annotation (Dialog(group="compressor speed module A"));
  parameter Real min_compressor_speed_moduleA_crkA
    "Minimum speed of compressor module A (Hz)"
    annotation (Dialog(group="compressor speed module A"));
  parameter Real max_compressor_speed_moduleA_crkB
    "Minimum speed of compressor module B (Hz)"
    annotation (Dialog(group="compressor speed module A"));
  parameter Real min_compressor_speed_moduleA_crkB
    "Maximum speed of compressor module B (Hz)"
    annotation (Dialog(group="compressor speed module A"));
  parameter Real max_compressor_speed_moduleB_crkA
    "Maximum speed of compressor module A (Hz)"
    annotation (Dialog(group="compressor speed module B"));
  parameter Real min_compressor_speed_moduleB_crkA
    "Minimum speed of compressor module A (Hz)"
    annotation (Dialog(group="compressor speed module B"));
  parameter Real max_compressor_speed_moduleB_crkB
    "Minimum speed of compressor module B (Hz)"
    annotation (Dialog(group="compressor speed module B"));
  parameter Real min_compressor_speed_moduleB_crkB
    "Maximum speed of compressor module B (Hz)"
    annotation (Dialog(group="compressor speed module B"));
  .Workspace.Interfaces.MeasurementBus measurementBus_moduleA_crkA
    annotation (Placement(transformation(extent={{-116.0,58.0},{-76.0,98.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-83.0,96.0},{-43.0,136.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_moduleA_crkA
    annotation (Placement(transformation(extent={{-106.53884282723261,37.461157172767386},{-85.46115717276739,58.538842827232614}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-43.538842827232614,105.46115717276739},{-22.461157172767386,126.53884282723261}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus_moduleA_crkB
    annotation (Placement(transformation(extent={{-106.0,-20.0},{-66.0,20.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{60.0,96.0},{100.0,136.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_moduleA_crkB
    annotation (Placement(transformation(extent={{-93.79099766511365,-39.79099766511365},{-74.20900233488635,-20.20900233488635}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{42.20900233488635,106.20900233488635},{61.79099766511365,125.79099766511365}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.CompleteCompressorControlBase_duplex completeCompressorControlBase_duplex(
    max_compressor_speed_moduleA_crkA=max_compressor_speed_moduleA_crkA,
    min_compressor_speed_moduleA_crkA=min_compressor_speed_moduleA_crkA,
    max_compressor_speed_moduleA_crkB=max_compressor_speed_moduleA_crkB,
    min_compressor_speed_moduleA_crkB=min_compressor_speed_moduleA_crkB,
    max_compressor_speed_moduleB_crkA=max_compressor_speed_moduleB_crkA,
    min_compressor_speed_moduleB_crkA=min_compressor_speed_moduleB_crkA,
    max_compressor_speed_moduleB_crkB=max_compressor_speed_moduleB_crkB,
    min_compressor_speed_moduleB_crkB=min_compressor_speed_moduleB_crkB,
    isOff_moduleA_crkA=isOff_moduleA_crkA,
    isOff_moduleA_crkB=isOff_moduleA_crkB,
    isOff_moduleB_crkA=isOff_moduleB_crkA,
    isOff_moduleB_crkB=isOff_moduleB_crkB)
    annotation (Placement(transformation(extent={{-8.267901940130045,-164.26790194013006},{36.267901940130045,-119.73209805986994}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.FanControlBase fanControl_moduleA_crkA(
    crkIsOff=isOff_moduleA_crkA)
    annotation (Placement(transformation(extent={{-10.0,90.0},{10.0,110.0}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EXVControlBase eXVControl_moduleA_crkA(
    crkIsOff=isOff_moduleA_crkA)
    annotation (Placement(transformation(extent={{-10.0,60.0},{10.0,80.0}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EXVControlBase eXVControl_moduleA_crkB(
    crkIsOff=isOff_moduleA_crkB)
    annotation (Placement(transformation(extent={{-8.222222222222221,-45.55555555555556},{11.777777777777779,-25.555555555555557}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EcoEXVControlBase ecoEXVControl_moduleA_crkA(
    crkIsOff=isOff_ecoA_moduleA)
    annotation (Placement(transformation(extent={{-10.0,30.0},{10.0,50.0}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EcoEXVControlBase ecoEXVControl_moduleA_crkB(
    crkIsOff=isOff_ecoB_moduleA)
    annotation (Placement(transformation(extent={{-8.222222222222221,-75.55555555555556},{11.777777777777779,-55.55555555555556}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.FanControlBase fanControl_moduleA_crkB(
    crkIsOff=isOff_moduleA_crkB)
    annotation (Placement(transformation(extent={{-8.222222222222221,-15.555555555555557},{11.777777777777779,4.444444444444443}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus_moduleB_crkA
    annotation (Placement(transformation(extent={{-111.77777777777777,-262.8888888888889},{-71.77777777777777,-222.8888888888889}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-83.0,96.0},{-43.0,136.0}},origin={0.0,-225},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_moduleB_crkA
    annotation (Placement(transformation(extent={{-102.31662060501039,-277.42773171612157},{-81.23893495054516,-256.35004606165626}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-43.538842827232614,105.46115717276739},{-22.461157172767386,126.53884282723261}},origin={0.0,-225},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.FanControlBase fanControl_moduleB_crkA(
    crkIsOff=isOff_moduleB_crkA)
    annotation (Placement(transformation(extent={{-1.7777777777777928,-222.88888888888889},{18.222222222222207,-202.88888888888889}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EXVControlBase eXVControl_moduleB_crkA(
    crkIsOff=isOff_moduleB_crkA)
    annotation (Placement(transformation(extent={{-1.7777777777777928,-252.8888888888889},{18.222222222222207,-232.8888888888889}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EcoEXVControlBase ecoEXVControl_moduleB_crkA(
    crkIsOff=isOff_ecoA_moduleB)
    annotation (Placement(transformation(extent={{-1.7777777777777928,-282.8888888888889},{18.222222222222207,-262.8888888888889}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput fan_moduleA_crkA
    annotation (Placement(transformation(extent={{98.0,90.0},{118.0,110.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,195},rotation=180)));
  .Modelica.Blocks.Interfaces.RealOutput ecoExv_moduleA_crkA
    annotation (Placement(transformation(extent={{98.0,28.0},{118.0,48.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,175},rotation=180)));
  .Modelica.Blocks.Interfaces.RealOutput sstmax_moduleA_crkA
    annotation (Placement(transformation(extent={{98.0,48.0},{118.0,68.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,155},rotation=180)));
  .Modelica.Blocks.Interfaces.RealOutput exv_moduleA_crkA
    annotation (Placement(transformation(extent={{98.0,68.0},{118.0,88.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,135},rotation=180)));
  .Modelica.Blocks.Interfaces.RealOutput compressor_moduleA_crkA
    annotation (Placement(transformation(extent={{100.0,-140.0},{120.0,-120.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,115},rotation=180)));
  .Modelica.Blocks.Interfaces.RealOutput compressor_moduleA_crkB
    annotation (Placement(transformation(extent={{100.0,-156.0},{120.0,-136.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,95},rotation=180)));
  .Modelica.Blocks.Interfaces.RealOutput fan_moduleA_crkB
    annotation (Placement(transformation(extent={{99.77777777777777,-15.555555555555557},{119.77777777777777,4.444444444444443}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,75},rotation=180)));
  .Modelica.Blocks.Interfaces.RealOutput exv_moduleA_crkB
    annotation (Placement(transformation(extent={{99.77777777777777,-35.55555555555556},{119.77777777777777,-15.555555555555557}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,55},rotation=180)));
  .Modelica.Blocks.Interfaces.RealOutput sstmax_moduleA_crkB
    annotation (Placement(transformation(extent={{108.0,-52.0},{128.0,-32.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,35},rotation=180)));
  .Modelica.Blocks.Interfaces.RealOutput ecoExv_moduleA_crkB
    annotation (Placement(transformation(extent={{112.0,-76.0},{132.0,-56.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,15},rotation=180)));
  .Modelica.Blocks.Interfaces.RealOutput KaInput_crk_moduleA
    annotation (Placement(transformation(extent={{152.0,-96.0},{172.0,-76.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,-5},rotation=180)));
  .Modelica.Blocks.Interfaces.RealOutput pumpUser_crk_moduleA
    annotation (Placement(transformation(extent={{154.0,-108.0},{174.0,-88.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,-25},rotation=180)));
  .Workspace.Interfaces.LimitsBus limitsBus_moduleB_crkB
    annotation (Placement(transformation(extent={{-102.01321988733588,-345.3465532206692},{-82.43122455710858,-325.7645578904419}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{42.20900233488635,106.20900233488635},{61.79099766511365,125.79099766511365}},origin={0.0,-225},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EXVControlBase eXVControl_moduleB_crkB(
    crkIsOff=isOff_moduleB_crkB)
    annotation (Placement(transformation(extent={{-2.2222222222222143,-359.55555555555554},{17.777777777777786,-339.55555555555554}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-2.2222222222222143,-359.55555555555554},{17.777777777777786,-339.55555555555554}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EcoEXVControlBase ecoEXVControl_moduleB_crkB(
    crkIsOff=isOff_ecoB_moduleB)
    annotation (Placement(transformation(extent={{-2.2222222222222143,-389.55555555555554},{17.777777777777786,-369.55555555555554}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-2.2222222222222143,-389.55555555555554},{17.777777777777786,-369.55555555555554}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.FanControlBase fanControl_moduleB_crkB(
    crkIsOff=isOff_moduleB_crkB)
    annotation (Placement(transformation(extent={{-2.2222222222222143,-329.55555555555554},{17.777777777777786,-309.55555555555554}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-2.2222222222222143,-329.55555555555554},{17.777777777777786,-309.55555555555554}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus_moduleB_crkB
    annotation (Placement(transformation(extent={{-112.22222222222223,-333.55555555555554},{-72.22222222222223,-293.55555555555554}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,-255},rotation=0.0),iconTransformation(extent={{60.0,96.0},{100.0,136.0}},origin={0.0,-225},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput fan_moduleB_crkA
    annotation (Placement(transformation(extent={{104.0,-222.0},{124.0,-202.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,-180},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput exv_moduleB_crkA
    annotation (Placement(transformation(extent={{104.0,-244.0},{124.0,-224.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput sstmax_moduleB_crkA
    annotation (Placement(transformation(extent={{104.0,-262.0},{124.0,-242.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,-20},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput ecoExv_moduleB_crkA
    annotation (Placement(transformation(extent={{104.0,-284.0},{124.0,-264.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,-40},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput compressor_moduleB_crkA
    annotation (Placement(transformation(extent={{100.0,-124.0},{120.0,-104.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,-60},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput compressor_moduleB_crkB
    annotation (Placement(transformation(extent={{100.0,-172.0},{120.0,-152.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,-80},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput fan_moduleB_crkB
    annotation (Placement(transformation(extent={{100.0,-328.0},{120.0,-308.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,-100},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput exv_moduleB_crkB
    annotation (Placement(transformation(extent={{100.0,-348.0},{120.0,-328.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,-120},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput sstmax_moduleB_crkB
    annotation (Placement(transformation(extent={{100.0,-370.0},{120.0,-350.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,-140},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput ecoExv_moduleB_crkB
    annotation (Placement(transformation(extent={{100.0,-390.0},{120.0,-370.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,-160},rotation=0.0)));
  .Workspace.Controller.SubSystems.PumpUserControl pumpUserControl_moduleA
    annotation (Placement(transformation(extent={{-9.363064008394538,-108.6033578174187},{10.636935991605462,-88.6033578174187}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.SubSystems.PumpUserControl pumpUserControl_moduleB
    annotation (Placement(transformation(extent={{-4.0,-428.0},{16.0,-408.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput KaInput_crk_moduleB
    annotation (Placement(transformation(extent={{122.0,-418.0},{142.0,-398.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,-200},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput pumpUser_crk_moduleB
    annotation (Placement(transformation(extent={{116.0,-444.0},{136.0,-424.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{98.0,88.0},{118.0,108.0}},origin={0.0,-220},rotation=0.0)));
equation
  connect(measurementBus_moduleA_crkA,fanControl_moduleA_crkA.measurementBus)
    annotation (Line(points={{-96,78},{-116,78},{-116,106},{-10,106}},color={255,204,51}));
  connect(fanControl_moduleA_crkA.limitsBus,limitsBus_moduleA_crkA)
    annotation (Line(points={{-10,94},{-54,94},{-54,48},{-96,48}},color={255,204,51}));
  connect(eXVControl_moduleA_crkA.measurementBus,measurementBus_moduleA_crkA)
    annotation (Line(points={{-10,76},{-96,76},{-96,78}},color={255,204,51}));
  connect(eXVControl_moduleA_crkA.limitsBus,limitsBus_moduleA_crkA)
    annotation (Line(points={{-10,64},{-54,64},{-54,48},{-96,48}},color={255,204,51}));
  connect(ecoEXVControl_moduleA_crkA.measurementBus,measurementBus_moduleA_crkA)
    annotation (Line(points={{-10,46},{-116,46},{-116,78},{-96,78}},color={255,204,51}));
  connect(ecoEXVControl_moduleA_crkA.limitsBus,limitsBus_moduleA_crkA)
    annotation (Line(points={{-10,34},{-54,34},{-54,48},{-96,48}},color={255,204,51}));
  connect(fanControl_moduleA_crkB.measurementBus,measurementBus_moduleA_crkB)
    annotation (Line(points={{-8.222222222222221,0.4444444444444571},{-86,0.4444444444444571},{-86,0}},color={255,204,51}));
  connect(fanControl_moduleA_crkB.limitsBus,limitsBus_moduleA_crkB)
    annotation (Line(points={{-8.222222222222221,-11.555555555555543},{-54.22222222222222,-11.555555555555543},{-54.22222222222222,-30},{-84,-30}},color={255,204,51}));
  connect(eXVControl_moduleA_crkB.measurementBus,measurementBus_moduleA_crkB)
    annotation (Line(points={{-8.222222222222221,-29.555555555555557},{-54,-29.555555555555557},{-54,0},{-86,0}},color={255,204,51}));
  connect(eXVControl_moduleA_crkB.limitsBus,limitsBus_moduleA_crkB)
    annotation (Line(points={{-8.222222222222221,-41.55555555555554},{-54.22222222222222,-41.55555555555554},{-54.22222222222222,-30},{-84,-30}},color={255,204,51}));
  connect(ecoEXVControl_moduleA_crkB.measurementBus,measurementBus_moduleA_crkB)
    annotation (Line(points={{-8.222222222222221,-59.55555555555554},{-54.16789087093384,-59.55555555555554},{-54.16789087093384,0},{-86,0}},color={255,204,51}));
  connect(ecoEXVControl_moduleA_crkB.limitsBus,limitsBus_moduleA_crkB)
    annotation (Line(points={{-8.222222222222221,-71.55555555555554},{-54.22222222222222,-71.55555555555554},{-54.22222222222222,-30},{-84,-30}},color={255,204,51}));
  connect(fanControl_moduleA_crkA.actuatorSignal,fan_moduleA_crkA)
    annotation (Line(points={{10,100},{108,100}},color={0,0,127}));
  connect(eXVControl_moduleA_crkA.actuatorSignal_sst,sstmax_moduleA_crkA)
    annotation (Line(points={{10,67.4},{60,67.4},{60,58},{108,58}},color={0,0,127}));
  connect(ecoEXVControl_moduleA_crkA.actuatorSignal,ecoExv_moduleA_crkA)
    annotation (Line(points={{10,40},{102,40},{102,38},{108,38}},color={0,0,127}));
  connect(fanControl_moduleA_crkB.actuatorSignal,fan_moduleA_crkB)
    annotation (Line(points={{11.777777777777779,-5.555555555555557},{109.77777777777777,-5.555555555555557}},color={0,0,127}));
  connect(eXVControl_moduleA_crkB.actuatorSignal,exv_moduleA_crkB)
    annotation (Line(points={{11.777777777777779,-35.55555555555556},{60.77777777777778,-35.55555555555556},{60.77777777777778,-25.555555555555557},{109.77777777777777,-25.555555555555557}},color={0,0,127}));
  connect(eXVControl_moduleA_crkB.actuatorSignal_sst,sstmax_moduleA_crkB)
    annotation (Line(points={{11.777777777777779,-38.15555555555555},{60.77777777777778,-38.15555555555555},{60.77777777777778,-42},{118,-42}},color={0,0,127}));
  connect(ecoEXVControl_moduleA_crkB.actuatorSignal,ecoExv_moduleA_crkB)
    annotation (Line(points={{11.777777777777779,-65.55555555555556},{122,-65.55555555555556},{122,-66}},color={0,0,127}));
  connect(measurementBus_moduleB_crkA,fanControl_moduleB_crkA.measurementBus)
    annotation (Line(points={{-91.7777777777778,-242.8888888888889},{-47.77777777777779,-242.8888888888889},{-47.77777777777779,-206.8888888888889},{-1.7777777777777928,-206.8888888888889}},color={255,204,51}));
  connect(fanControl_moduleB_crkA.limitsBus,limitsBus_moduleB_crkA)
    annotation (Line(points={{-1.7777777777777928,-218.8888888888889},{-47.77777777777779,-218.8888888888889},{-47.77777777777779,-266.8888888888889},{-91.7777777777778,-266.8888888888889}},color={255,204,51}));
  connect(eXVControl_moduleB_crkA.measurementBus,measurementBus_moduleB_crkA)
    annotation (Line(points={{-1.7777777777777928,-236.8888888888889},{-47.77777777777779,-236.8888888888889},{-47.77777777777779,-242.8888888888889},{-91.7777777777778,-242.8888888888889}},color={255,204,51}));
  connect(eXVControl_moduleB_crkA.limitsBus,limitsBus_moduleB_crkA)
    annotation (Line(points={{-1.7777777777777928,-248.8888888888889},{-47.77777777777779,-248.8888888888889},{-47.77777777777779,-266.8888888888889},{-91.7777777777778,-266.8888888888889}},color={255,204,51}));
  connect(ecoEXVControl_moduleB_crkA.measurementBus,measurementBus_moduleB_crkA)
    annotation (Line(points={{-1.7777777777777928,-266.8888888888889},{-47.77777777777779,-266.8888888888889},{-47.77777777777779,-242.8888888888889},{-91.7777777777778,-242.8888888888889}},color={255,204,51}));
  connect(ecoEXVControl_moduleB_crkA.limitsBus,limitsBus_moduleB_crkA)
    annotation (Line(points={{-1.7777777777777928,-278.8888888888889},{-47.77777777777779,-278.8888888888889},{-47.77777777777779,-266.8888888888889},{-91.7777777777778,-266.8888888888889}},color={255,204,51}));
  connect(fanControl_moduleB_crkB.measurementBus,measurementBus_moduleB_crkB)
    annotation (Line(points={{-2.2222222222222143,-313.55555555555554},{-92.22222222222223,-313.55555555555554}},color={255,204,51}));
  connect(fanControl_moduleB_crkB.limitsBus,limitsBus_moduleB_crkB)
    annotation (Line(points={{-2.2222222222222143,-325.55555555555554},{-48.222222222222214,-325.55555555555554},{-48.222222222222214,-335.55555555555554},{-92.22222222222223,-335.55555555555554}},color={255,204,51}));
  connect(eXVControl_moduleB_crkB.measurementBus,measurementBus_moduleB_crkB)
    annotation (Line(points={{-2.2222222222222143,-343.55555555555554},{-48.222222222222214,-343.55555555555554},{-48.222222222222214,-313.55555555555554},{-92.22222222222223,-313.55555555555554}},color={255,204,51}));
  connect(eXVControl_moduleB_crkB.limitsBus,limitsBus_moduleB_crkB)
    annotation (Line(points={{-2.2222222222222143,-355.55555555555554},{-48.222222222222214,-355.55555555555554},{-48.222222222222214,-335.55555555555554},{-92.22222222222223,-335.55555555555554}},color={255,204,51}));
  connect(ecoEXVControl_moduleB_crkB.measurementBus,measurementBus_moduleB_crkB)
    annotation (Line(points={{-2.2222222222222143,-373.55555555555554},{-48.222222222222214,-373.55555555555554},{-48.222222222222214,-313.55555555555554},{-92.22222222222223,-313.55555555555554}},color={255,204,51}));
  connect(ecoEXVControl_moduleB_crkB.limitsBus,limitsBus_moduleB_crkB)
    annotation (Line(points={{-2.2222222222222143,-385.55555555555554},{-48.222222222222214,-385.55555555555554},{-48.222222222222214,-335.55555555555554},{-92.22222222222223,-335.55555555555554}},color={255,204,51}));
  connect(completeCompressorControlBase_duplex.measurementBus_moduleB_crkA,measurementBus_moduleB_crkA)
    annotation (Line(points={{-18.956494871392465,-127.30318471951416},{-116,-127.30318471951416},{-116,-242},{-92,-242}},color={255,204,51}));
  connect(completeCompressorControlBase_duplex.measurementBus_moduleA_crkA,measurementBus_moduleA_crkA)
    annotation (Line(points={{-12.276124289353454,-127.30318471951416},{-116,-127.30318471951416},{-116,78},{-96,78}},color={255,204,51}));
  connect(completeCompressorControlBase_duplex.measurementBus_moduleB_crkB,measurementBus_moduleB_crkB)
    annotation (Line(points={{-18.956494871392465,-150.01644469844683},{-116,-150.01644469844683},{-116,-313.55555555555554},{-92.22222222222223,-313.55555555555554}},color={255,204,51}));
  connect(completeCompressorControlBase_duplex.measurementBus_moduleA_crkB,measurementBus_moduleA_crkB)
    annotation (Line(points={{-12.27612428935345,-150.01644469844683},{-116,-150.01644469844683},{-116,0},{-86,0}},color={255,204,51}));
  connect(completeCompressorControlBase_duplex.limitsBus_moduleB_crkA,limitsBus_moduleB_crkA)
    annotation (Line(points={{-18.956494871392465,-134.4289133403558},{-116,-134.4289133403558},{-116,-266.8888888888889},{-91.77777777777777,-266.8888888888889}},color={255,204,51}));
  connect(completeCompressorControlBase_duplex.limitsBus_moduleA_crkA,limitsBus_moduleA_crkA)
    annotation (Line(points={{-12.276124289353447,-134.4289133403558},{-116,-134.4289133403558},{-116,48},{-96,48}},color={255,204,51}));
  connect(completeCompressorControlBase_duplex.limitsBus_moduleB_crkB,limitsBus_moduleB_crkB)
    annotation (Line(points={{-18.956494871392465,-157.14217331928845},{-116,-157.14217331928845},{-116,-335.55555555555554},{-92.22222222222223,-335.55555555555554}},color={255,204,51}));
  connect(completeCompressorControlBase_duplex.limitsBus_moduleA_crkB,limitsBus_moduleA_crkB)
    annotation (Line(points={{-12.276124289353454,-157.14217331928845},{-116,-157.14217331928845},{-116,-30},{-84,-30}},color={255,204,51}));
  connect(eXVControl_moduleA_crkA.actuatorSignal,exv_moduleA_crkA)
    annotation (Line(points={{10,70},{59,70},{59,78},{108,78}},color={0,0,127}));
  connect(completeCompressorControlBase_duplex.actuatorSignal_moduleB_crkA,compressor_moduleB_crkA)
    annotation (Line(points={{40.276124289353454,-117.50530786585693},{110,-117.50530786585693},{110,-114}},color={0,0,127}));
  connect(completeCompressorControlBase_duplex.actuatorSignal_moduleA_crkA,compressor_moduleA_crkA)
    annotation (Line(points={{39.83076625055085,-132.20212314634279},{104,-132.20212314634279},{104,-130},{110,-130}},color={0,0,127}));
  connect(completeCompressorControlBase_duplex.actuatorSignal_moduleA_crkB,compressor_moduleA_crkB)
    annotation (Line(points={{39.83076625055085,-148.68037058203902},{102,-148.68037058203902},{102,-146},{110,-146}},color={0,0,127}));
  connect(completeCompressorControlBase_duplex.actuatorSignal_moduleB_crkB,compressor_moduleB_crkB)
    annotation (Line(points={{40.276124289353454,-163.82254390132746},{110,-163.82254390132746},{110,-162}},color={0,0,127}));
  connect(fanControl_moduleB_crkA.actuatorSignal,fan_moduleB_crkA)
    annotation (Line(points={{18.222222222222207,-212.88888888888889},{106,-212.88888888888889},{106,-212},{114,-212}},color={0,0,127}));
  connect(eXVControl_moduleB_crkA.actuatorSignal_sst,sstmax_moduleB_crkA)
    annotation (Line(points={{18.222222222222207,-245.4888888888889},{66,-245.4888888888889},{66,-252},{114,-252}},color={0,0,127}));
  connect(eXVControl_moduleB_crkA.actuatorSignal,exv_moduleB_crkA)
    annotation (Line(points={{18.222222222222207,-242.8888888888889},{64,-242.8888888888889},{64,-234},{114,-234}},color={0,0,127}));
  connect(ecoEXVControl_moduleB_crkA.actuatorSignal,ecoExv_moduleB_crkA)
    annotation (Line(points={{18.222222222222207,-272.8888888888889},{106,-272.8888888888889},{106,-274},{114,-274}},color={0,0,127}));
  connect(fanControl_moduleB_crkB.actuatorSignal,fan_moduleB_crkB)
    annotation (Line(points={{17.777777777777786,-319.55555555555554},{100,-319.55555555555554},{100,-318},{110,-318}},color={0,0,127}));
  connect(eXVControl_moduleB_crkB.actuatorSignal,exv_moduleB_crkB)
    annotation (Line(points={{17.777777777777786,-349.55555555555554},{63.88888888888889,-349.55555555555554},{63.88888888888889,-338},{110,-338}},color={0,0,127}));
  connect(eXVControl_moduleB_crkB.actuatorSignal_sst,sstmax_moduleB_crkB)
    annotation (Line(points={{17.777777777777786,-352.15555555555557},{63.88888888888889,-352.15555555555557},{63.88888888888889,-360},{110,-360}},color={0,0,127}));
  connect(ecoEXVControl_moduleB_crkB.actuatorSignal,ecoExv_moduleB_crkB)
    annotation (Line(points={{17.777777777777786,-379.55555555555554},{63.88888888888889,-379.55555555555554},{63.88888888888889,-380},{110,-380}},color={0,0,127}));
  connect(pumpUserControl_moduleA.measurementBus,measurementBus_moduleA_crkA)
    annotation (Line(points={{-11.96306400839454,-88.20335781741869},{-54.22679334994751,-88.20335781741869},{-54.22679334994751,78},{-96,78}},color={255,204,51}));
  connect(pumpUserControl_moduleA.limitsBus,limitsBus_moduleA_crkA)
    annotation (Line(points={{-11.563064008394539,-105.6033578174187},{-54.96063893625393,-105.6033578174187},{-54.96063893625393,48},{-96,48}},color={255,204,51}));
  connect(pumpUserControl_moduleA.ActuatorSignal_Ka,KaInput_crk_moduleA)
    annotation (Line(points={{19.436935991605463,-93.8033578174187},{19.436935991605463,-86},{162,-86}},color={0,0,127}));
  connect(pumpUserControl_moduleA.ActuatorSignal,pumpUser_crk_moduleA)
    annotation (Line(points={{19.436935991605463,-97.6033578174187},{164,-97.6033578174187},{164,-98}},color={0,0,127}));
  connect(pumpUserControl_moduleB.ActuatorSignal_Ka,KaInput_crk_moduleB)
    annotation (Line(points={{24.8,-413.2},{24.8,-408},{132,-408}},color={0,0,127}));
  connect(pumpUserControl_moduleB.ActuatorSignal,pumpUser_crk_moduleB)
    annotation (Line(points={{24.8,-417},{68.4,-417},{68.4,-434},{126,-434}},color={0,0,127}));
  connect(pumpUserControl_moduleB.measurementBus,measurementBus_moduleB_crkA)
    annotation (Line(points={{-6.600000000000001,-407.6},{-47.8476798018592,-407.6},{-47.8476798018592,-242.8888888888889},{-91.77777777777777,-242.8888888888889}},color={255,204,51}));
  connect(pumpUserControl_moduleB.limitsBus,limitsBus_moduleB_crkA)
    annotation (Line(points={{-6.200000000000001,-425},{-48.402078181564804,-425},{-48.402078181564804,-266.8888888888889},{-91.77777777777777,-266.8888888888889}},color={255,204,51}));
end ControllerBase_Duplex;
