within KAV_initiation.System30KAV.Controller30KAV.Components.Functions;
function subcooling
  "Used to calculate subcooling setpoint in EXV controller settings"
  import SI=Modelica.SIunits;
  import BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax;
  import Refrigerant=.Workspace.Controller.Components.Types.RefrigerantSelector;
  input Refrigerant refrigerant
    "Refrigerant in the system";
  input SI.Frequency compressorFrequency
    "Compressor Frequency";
  input SI.Frequency compressorFrequency_min
    "Mininum Compressor Frequency";
  input SI.Frequency compressorFrequency_max
    "Maximum Compressor Frequency";
  input SI.TemperatureDifference dT_sbc_min
    "Minimum subcooling";
  input SI.TemperatureDifference dT_sbc_max
    "Maximum subcooling";
  input SI.Temperature T_oat
    "Outside air temperature";
  input SI.Temperature T_oat_min
    "Mininum Outside air temperature";
  input SI.Temperature T_oat_max
    "Maximum Outside air temperature";
  input Real smoothing
    "Min/Max Smoothing";
  output SI.TemperatureDifference dT_sbc_setpoint;
protected
  Real compressorLoad
    "Compressor load [%]";
  Real CAP_min;
  Real dT_sbc_setpoint1;
  Real dT_sbc_setpoint2;
algorithm
  assert(
    refrigerant == Refrigerant.R134a or refrigerant == Refrigerant.R1234ze,
    "Not supported refrigerant chosen.");
  compressorLoad :=(compressorFrequency/compressorFrequency_max)*100;
  CAP_min :=(compressorFrequency_min/compressorFrequency_max)*100;
  dT_sbc_setpoint1 :=(dT_sbc_max-dT_sbc_min)*(compressorLoad-CAP_min)/(100-CAP_min);
  if refrigerant == Refrigerant.R134a then
    dT_sbc_setpoint2 := max(
      dT_sbc_min-dT_sbc_max,
      min(
        0.0,
        ((T_oat-T_oat_max)/((T_oat_max-T_oat_min)/(dT_sbc_max-dT_sbc_min)))));
  else
    // R1234ze
    dT_sbc_setpoint2 := 0;
  end if;
  dT_sbc_setpoint := dT_sbc_min+dT_sbc_setpoint1+dT_sbc_setpoint2;
  dT_sbc_setpoint :=-softMax(
    dT_sbc_min,
    min(
      dT_sbc_setpoint,
      dT_sbc_max),
    smoothing);
end subcooling;
