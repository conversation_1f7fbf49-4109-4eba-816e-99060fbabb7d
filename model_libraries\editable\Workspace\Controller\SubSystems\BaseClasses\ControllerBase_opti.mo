within Workspace.Controller.SubSystems.BaseClasses;
partial model ControllerBase_opti
  extends.BOLT.InternalLibrary.BuildingBlocks.Icons.ControllerPackage;
  parameter Boolean crkA_isOff=false
    "Specify whether the circuit A is on or off";
  parameter Boolean crkB_isOff=false
    "Specify whether the circuit B is on or off";
  parameter Boolean ecoA_isOff=false
    "Specify whether the economizer EXV on circuit A is on or off";
  parameter Boolean ecoB_isOff=false
    "Specify whether the economizer EXV on circuit B is on or off";
  parameter Real crkA_min_speed=23
    "Minimum speed of compressor A (Hz)";
  parameter Real crkB_min_speed=27
    "Minimum speed of compressor B (Hz)";
  parameter Real crkA_max_speed=95
    "Maximum speed of compressor A (Hz)";
  parameter Real crkB_max_speed=105
    "Maximum speed of compressor B (Hz)";
  //Variables for optimization
  parameter Real min_speed=23
    annotation (Dialog(tab="General",group="Compressor"));
  parameter Real max_speed=95
    annotation (Dialog(tab="General",group="Compressor"));
  parameter Boolean CrkIsOff=false;
  parameter Boolean EcoIsOff=false
    annotation (Dialog(tab="General",group="EXV Economizer"));
  parameter Real minBoundFan=0.15
    annotation (Dialog(tab="Optimization",group="Bounds"));
  parameter Real maxBoundFan=1.0
    annotation (Dialog(tab="Optimization",group="Bounds"));
  parameter Real minBoundEXV=0.1
    annotation (Dialog(tab="Optimization",group="Bounds"));
  parameter Real maxBoundEXV=1.0
    annotation (Dialog(tab="Optimization",group="Bounds"));
  parameter Real minBoundEXVEco=0.1
    annotation (Dialog(tab="Optimization",group="Bounds"));
  parameter Real maxBoundEXVEco=1.0
    annotation (Dialog(tab="Optimization",group="Bounds"));
  parameter.Modelica.SIunits.Frequency minBoundCpA=19.0
    annotation (Dialog(tab="Optimization",group="Bounds"));
  parameter.Modelica.SIunits.Frequency maxBoundCpA=95.0
    annotation (Dialog(tab="Optimization",group="Bounds"));
  parameter.Modelica.SIunits.Frequency minBoundCpB=19.0
    annotation (Dialog(tab="Optimization",group="Bounds"));
  parameter.Modelica.SIunits.Frequency maxBoundCpB=95.0
    annotation (Dialog(tab="Optimization",group="Bounds"));
  parameter Real speedFanA=950.0
    annotation (Dialog(tab="Optimization",group="Fixed values"));
  parameter Real openingEXVA=0.5
    annotation (Dialog(tab="Optimization",group="Fixed values"));
  parameter Real openingEXVECOA=0.5
    annotation (Dialog(tab="Optimization",group="Fixed values"));
  parameter.Modelica.SIunits.Frequency speedCpA=95.0
    annotation (Dialog(tab="Optimization",group="Fixed values"));
  parameter Real speedFanB=950.0
    annotation (Dialog(tab="Optimization",group="Fixed values"));
  parameter Real openingEXVB=0.5
    annotation (Dialog(tab="Optimization",group="Fixed values"));
  parameter Real openingEXVECOB=0.5
    annotation (Dialog(tab="Optimization",group="Fixed values"));
  parameter.Modelica.SIunits.Frequency speedCpB=95.0
    annotation (Dialog(tab="Optimization",group="Fixed values"));
  parameter Boolean useFanActuatorA=false
    annotation (Dialog(tab="Optimization",group="Activations circuit A"));
  parameter Boolean useEXVActuatorA=false
    annotation (Dialog(tab="Optimization",group="Activations circuit A"));
  parameter Boolean useEXVEcoActuatorA=false
    annotation (Dialog(tab="Optimization",group="Activations circuit A"));
  parameter Boolean useCpActuatorA=false
    annotation (Dialog(tab="Optimization",group="Activations circuit A"));
  parameter Boolean useFanActuatorB=false
    annotation (Dialog(tab="Optimization",group="Activations circuit B"));
  parameter Boolean useEXVActuatorB=false
    annotation (Dialog(tab="Optimization",group="Activations circuit B"));
  parameter Boolean useEXVEcoActuatorB=false
    annotation (Dialog(tab="Optimization",group="Activations circuit B"));
  parameter Boolean useCpActuatorB=false
    annotation (Dialog(tab="Optimization",group="Activations circuit B"));
  .Modelica.Blocks.Logical.Switch switchFanCtrlA
    annotation (Placement(transformation(extent={{62.59099392465541,102.42688735825567},{74.65381165771646,114.4897050913167}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.BooleanExpression fanCtrlOffA(
    y=useFanActuatorA)
    annotation (Placement(transformation(extent={{30.62240279118592,98.45829622478618},{50.62240279118592,118.45829622478618}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator fan_actuatorA(
    fixed=not useFanActuatorA,
    maxBound=maxBoundFan,
    minBound=minBoundFan,
    isOff=not useFanActuatorA,
    setPoint=speedFanA)
    annotation (Placement(transformation(extent={{48.62240279118592,112.45829622478618},{28.62240279118592,132.45829622478618}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator exv_actuatorA(
    fixed=not useEXVActuatorA,
    maxBound=maxBoundEXV,
    minBound=minBoundEXV,
    isOff=not useEXVActuatorA,
    setPoint=openingEXVA)
    annotation (Placement(transformation(extent={{49.52701293704742,78.26751651650912},{29.527012937047417,98.26751651650912}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.BooleanExpression eXVCtrlOffA(
    y=useEXVActuatorA)
    annotation (Placement(transformation(extent={{29.33333333333333,69.33333333333334},{49.33333333333333,89.33333333333334}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Logical.Switch switchEXVCtrlA
    annotation (Placement(transformation(extent={{61.30192446680282,73.30192446680283},{73.36474219986387,85.36474219986385}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.BooleanExpression eXVEcoCtrlOffA(
    y=useEXVEcoActuatorA)
    annotation (Placement(transformation(extent={{29.33333333333333,27.333333333333336},{49.33333333333333,47.333333333333336}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator exvEco_actuatorA(
    fixed=not useEXVEcoActuatorA,
    maxBound=maxBoundEXVEco,
    minBound=minBoundEXVEco,
    isOff=not useEXVEcoActuatorA,
    setPoint=openingEXVECOA)
    annotation (Placement(transformation(extent={{47.33333333333333,41.333333333333336},{27.33333333333333,61.33333333333335}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Logical.Switch switchEXVEcoCtrlA
    annotation (Placement(transformation(extent={{61.30192446680282,31.301924466802824},{73.36474219986387,43.36474219986385}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator cp_actuatorA(
    fixed=not useCpActuatorA,
    maxBound=maxBoundCpA,
    minBound=minBoundCpA,
    isOff=not useCpActuatorA,
    setPoint=speedCpA)
    annotation (Placement(transformation(extent={{51.986874076181216,12.762464199989164},{31.986874076181216,32.76246419998917}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.BooleanExpression cpCtrlOffA(
    y=useCpActuatorA)
    annotation (Placement(transformation(extent={{32.802096696391445,0.7370931663054243},{52.802096696391445,20.737093166305424}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Logical.Switch switchCpCtrlA
    annotation (Placement(transformation(extent={{65.95546520965071,2.7310553334586487},{78.01828294271176,14.793873066519673}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Logical.Switch switchFanCtrlB
    annotation (Placement(transformation(extent={{67.87191651506524,-31.714110125307315},{79.93473424812629,-43.77692785836834}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.BooleanExpression fanCtrlOffB(
    y=useFanActuatorB)
    annotation (Placement(transformation(extent={{32.70243558327027,-47.7455189918378},{52.70243558327027,-27.745518991837812}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator exv_actuatorB(
    setPoint=openingEXVB,
    isOff=not useEXVActuatorB,
    minBound=minBoundEXV,
    maxBound=maxBoundEXV,
    fixed=not useEXVActuatorB)
    annotation (Placement(transformation(extent={{55.09775560592639,-86.57738350775804},{35.09775560592639,-66.57738350775804}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.BooleanExpression eXVCtrlOffB(
    y=useEXVActuatorB)
    annotation (Placement(transformation(extent={{34.6142559237432,-76.87048188329068},{54.6142559237432,-56.87048188329068}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Logical.Switch switchEXVCtrlB
    annotation (Placement(transformation(extent={{66.58284705721269,-60.83907301676017},{78.64566479027374,-72.90189074982119}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.BooleanExpression eXVEcoCtrlOffB(
    y=useEXVEcoActuatorB)
    annotation (Placement(transformation(extent={{38.34862735512303,-110.86825738747679},{58.34862735512303,-90.86825738747679}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator exvEco_actuatorB(
    setPoint=openingEXVECOB,
    isOff=not useEXVEcoActuatorB,
    minBound=minBoundEXVEco,
    maxBound=maxBoundEXVEco,
    fixed=not useEXVEcoActuatorB)
    annotation (Placement(transformation(extent={{59.549517153448576,-122.47537577408123},{39.549517153448576,-102.47537577408123}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Logical.Switch switchEXVEcoCtrlB
    annotation (Placement(transformation(extent={{67.64981032332123,-94.30336688789204},{79.71262805638229,-106.36618462095306}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator cp_actuatorB(
    isOff=not useCpActuatorB,
    minBound=minBoundCpB,
    maxBound=maxBoundCpB,
    fixed=not useCpActuatorB,
    setPoint=speedCpB)
    annotation (Placement(transformation(extent={{51.37461972544071,-32.87133058276231},{31.37461972544071,-12.871330582762312}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Logical.Switch switchCpCtrlB
    annotation (Placement(transformation(extent={{66.52798823869999,-5.56467094738349},{78.59080597176104,-17.627488680444515}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.BooleanExpression cpCtrlOffB(
    y=useCpActuatorB)
    annotation (Placement(transformation(extent={{32.841138092386494,-20.154932480651997},{52.841138092386494,-0.15493248065199694}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator fan_actuatorB(
    setPoint=speedFanB,
    isOff=not useFanActuatorB,
    minBound=minBoundFan,
    maxBound=maxBoundFan,
    fixed=not useFanActuatorB)
    annotation (Placement(transformation(extent={{52.30288048243286,-60.95308227760499},{32.30288048243286,-40.95308227760499}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus_crkA
    annotation (Placement(transformation(extent={{-120.0,50.0},{-80.0,90.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_crkA
    annotation (Placement(transformation(extent={{-110.53884282723261,19.461157172767386},{-89.46115717276739,40.538842827232614}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus_crkB
    annotation (Placement(transformation(extent={{-120.0,-50.0},{-80.0,-10.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_crkB
    annotation (Placement(transformation(extent={{-109.79099766511365,-80.79099766511365},{-90.20900233488635,-61.20900233488635}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.CompleteCompressorControlBase completeCompressorControl_base(
    crkA_isOff=crkA_isOff,
    crkB_isOff=crkB_isOff,
    crkA_min_speed=crkA_min_speed,
    crkB_min_speed=crkB_min_speed,
    crkA_max_speed=crkA_max_speed,
    crkB_max_speed=crkB_max_speed)
    annotation (Placement(transformation(extent={{-6.265628717631785,-7.86607361679458},{13.73437158039144,12.133926681228644}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.FanControlBase fanControl_crkA(
    crkIsOff=crkA_isOff)
    annotation (Placement(transformation(extent={{-9.095389854138546,87.23688116689176},{10.904610145861454,107.23688116689176}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EXVControlBase eXVControl_crkA(
    crkIsOff=crkA_isOff)
    annotation (Placement(transformation(extent={{-9.09538985413851,56.78457609396102},{10.90461014586149,76.78457609396102}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.FanControlBase fanControl_crkB(
    crkIsOff=crkB_isOff)
    annotation (Placement(transformation(extent={{-10.0,-38.0},{10.0,-18.0}},origin={0,-2},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EXVControlBase eXVControl_crkB(
    crkIsOff=crkB_isOff)
    annotation (Placement(transformation(extent={{-10.0,-68.0},{10.0,-48.0}},origin={0,-2},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EcoEXVControlBase ecoEXVControl_crkA(
    crkIsOff=ecoA_isOff)
    annotation (Placement(transformation(extent={{-8.025371033683705,24.73910951915905},{11.974628966316295,44.73910951915905}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EcoEXVControlBase ecoEXVControl_crkB(
    crkIsOff=ecoB_isOff)
    annotation (Placement(transformation(extent={{-10.53348163305423,-105.86829796359686},{9.46651836694577,-85.86829796359686}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput fan_crkA
    annotation (Placement(transformation(extent={{101.43751855427351,98.19542150635723},{121.43751855427351,118.19542150635723}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput compressor_crkA
    annotation (Placement(transformation(extent={{103.0172217273265,-0.10094389591622743},{123.0172217273265,19.899056104083773}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput exv_crkA
    annotation (Placement(transformation(extent={{101.43751855427351,76.60945385566268},{121.43751855427351,96.60945385566268}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput fan_crkB
    annotation (Placement(transformation(extent={{101.4229959900697,-47.52465393949728},{121.4229959900697,-27.524653939497277}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput compressor_crkB
    annotation (Placement(transformation(extent={{101.42299599006961,-21.496800875063183},{121.42299599006961,-1.4968008750631832}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput exv_crkB
    annotation (Placement(transformation(extent={{99.28906945785259,-74.99339680225692},{119.28906945785259,-54.993396802256925}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput ecoExv_crkA
    annotation (Placement(transformation(extent={{101.86720837355767,26.578138915705168},{121.86720837355767,46.57813891570517}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput ecoExv_crkB
    annotation (Placement(transformation(extent={{101.56790602930407,-108.32598863698561},{121.56790602930407,-88.32598863698561}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput sstmax_crkA
    annotation (Placement(transformation(extent={{101.43751855427351,50.164106566399724},{121.43751855427351,70.16410656639972}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput sstmax_crkB
    annotation (Placement(transformation(extent={{100.35603272396114,-89.25902537087708},{120.35603272396114,-69.25902537087708}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(fanControl_crkA.limitsBus,limitsBus_crkA)
    annotation (Line(points={{-9.095389854138546,91.23688116689176},{-32,91.23688116689176},{-32,30},{-100,30}},color={255,204,51}));
  connect(eXVControl_crkA.measurementBus,measurementBus_crkA)
    annotation (Line(points={{-9.09538985413851,72.78457609396102},{-56,72.78457609396102},{-56,70},{-100,70}},color={255,204,51}));
  connect(eXVControl_crkA.limitsBus,limitsBus_crkA)
    annotation (Line(points={{-9.09538985413851,60.78457609396102},{-32,60.78457609396102},{-32,30},{-100,30}},color={255,204,51}));
  connect(completeCompressorControl_base.limitsBus_crkA,limitsBus_crkA)
    annotation (Line(points={{-6.265628717631785,6.133926591821677},{-32,6.133926591821677},{-32,30},{-100,30}},color={255,204,51}));
  connect(completeCompressorControl_base.measurementBus_crkB,measurementBus_crkB)
    annotation (Line(points={{-6.265628717631785,-1.966073528877729},{-56,-1.966073528877729},{-56,-30},{-100,-30}},color={255,204,51}));
  connect(completeCompressorControl_base.limitsBus_crkB,limitsBus_crkB)
    annotation (Line(points={{-6.265628717631785,-5.966073588482374},{-32,-5.966073588482374},{-32,-71},{-100,-71}},color={255,204,51}));
  connect(fanControl_crkB.measurementBus,measurementBus_crkB)
    annotation (Line(points={{-10,-24},{-56,-24},{-56,-30},{-100,-30}},color={255,204,51}));
  connect(fanControl_crkB.limitsBus,limitsBus_crkB)
    annotation (Line(points={{-10,-36},{-32,-36},{-32,-71},{-100,-71}},color={255,204,51}));
  connect(eXVControl_crkB.measurementBus,measurementBus_crkB)
    annotation (Line(points={{-10,-54},{-56,-54},{-56,-30},{-100,-30}},color={255,204,51}));
  connect(eXVControl_crkB.limitsBus,limitsBus_crkB)
    annotation (Line(points={{-10,-66},{-32,-66},{-32,-71},{-100,-71}},color={255,204,51}));
  connect(ecoEXVControl_crkB.measurementBus,measurementBus_crkB)
    annotation (Line(points={{-10.53348163305423,-89.86829796359686},{-56,-89.86829796359686},{-56,-30},{-100,-30}},color={255,204,51}));
  connect(ecoEXVControl_crkB.limitsBus,limitsBus_crkB)
    annotation (Line(points={{-10.53348163305423,-101.86829796359686},{-32,-101.86829796359686},{-32,-71},{-100,-71}},color={255,204,51}));
  connect(ecoEXVControl_crkA.measurementBus,measurementBus_crkA)
    annotation (Line(points={{-8.025371033683705,40.73910951915905},{-56,40.73910951915905},{-56,70},{-100,70}},color={255,204,51}));
  connect(ecoEXVControl_crkA.limitsBus,limitsBus_crkA)
    annotation (Line(points={{-8.025371033683705,28.73910951915905},{-32,28.73910951915905},{-32,30},{-100,30}},color={255,204,51}));
  connect(completeCompressorControl_base.measurementBus_crkA,measurementBus_crkA)
    annotation (Line(points={{-6.5742246206693675,10.038974065876298},{-56,10.038974065876298},{-56,70},{-100,70}},color={255,204,51}));
  connect(measurementBus_crkA,fanControl_crkA.measurementBus)
    annotation (Line(points={{-100,70},{-54,70},{-54,103.23688116689176},{-9.095389854138546,103.23688116689176}},color={255,204,51}));
  connect(fan_actuatorA.value,switchFanCtrlA.u1)
    annotation (Line(points={{43.62240279118592,122.45829622478618},{61.384712151349305,122.45829622478618},{61.384712151349305,113.28342331801059}},color={0,0,127}));
  connect(switchFanCtrlA.y,fan_crkA)
    annotation (Line(points={{75.25695254436951,108.45829622478618},{93.34723554932151,108.45829622478618},{93.34723554932151,108.19542150635723},{111.43751855427351,108.19542150635723}},color={0,0,127}));
  connect(switchFanCtrlA.u2,fanCtrlOffA.y)
    annotation (Line(points={{61.384712151349305,108.45829622478618},{51.62240279118592,108.45829622478618}},color={255,0,255}));
  connect(switchFanCtrlA.u3,fanControl_crkA.actuatorSignal)
    annotation (Line(points={{61.384712151349305,103.63316913156177},{61.384712151349305,97.23688116689176},{10.904610145861454,97.23688116689176}},color={0,0,127}));
  connect(exv_actuatorA.value,switchEXVCtrlA.u1)
    annotation (Line(points={{44.52701293704742,88.26751651650912},{50.52701293704742,88.26751651650912},{50.52701293704742,90.15846042655775},{60.095642693496714,90.15846042655775},{60.095642693496714,84.15846042655775}},color={0,0,127}));
  connect(switchEXVCtrlA.y,exv_crkA)
    annotation (Line(points={{73.96788308651692,79.33333333333334},{92.70270082039522,79.33333333333334},{92.70270082039522,86.60945385566268},{111.43751855427351,86.60945385566268}},color={0,0,127}));
  connect(switchEXVCtrlA.u2,eXVCtrlOffA.y)
    annotation (Line(points={{60.095642693496714,79.33333333333334},{50.33333333333333,79.33333333333334}},color={255,0,255}));
  connect(switchEXVCtrlA.u3,eXVControl_crkA.actuatorSignal)
    annotation (Line(points={{60.095642693496714,74.50820624010893},{60.095642693496714,66.78457609396102},{10.90461014586149,66.78457609396102}},color={0,0,127}));
  connect(eXVControl_crkA.actuatorSignal_sst,sstmax_crkA)
    annotation (Line(points={{10.90461014586149,64.18457609396103},{61.171064350067496,64.18457609396103},{61.171064350067496,60.164106566399724},{111.43751855427351,60.164106566399724}},color={0,0,127}));
  connect(exvEco_actuatorA.value,switchEXVEcoCtrlA.u1)
    annotation (Line(points={{42.33333333333333,51.33333333333334},{60.095642693496714,51.33333333333334},{60.095642693496714,42.158460426557745}},color={0,0,127}));
  connect(switchEXVEcoCtrlA.y,ecoExv_crkA)
    annotation (Line(points={{73.96788308651692,37.333333333333336},{92.91754573003729,37.333333333333336},{92.91754573003729,36.57813891570517},{111.86720837355767,36.57813891570517}},color={0,0,127}));
  connect(switchEXVEcoCtrlA.u2,eXVEcoCtrlOffA.y)
    annotation (Line(points={{60.095642693496714,37.333333333333336},{50.33333333333333,37.333333333333336}},color={255,0,255}));
  connect(switchEXVEcoCtrlA.u3,ecoEXVControl_crkA.actuatorSignal)
    annotation (Line(points={{60.095642693496714,32.508206240108926},{60.095642693496714,26.508206240108926},{17.974628966316295,26.508206240108926},{17.974628966316295,34.73910951915905},{11.974628966316295,34.73910951915905}},color={0,0,127}));
  connect(cp_actuatorA.value,switchCpCtrlA.u1)
    annotation (Line(points={{46.986874076181216,22.762464199989168},{64.74918343634461,22.762464199989168},{64.74918343634461,13.58759129321357}},color={0,0,127}));
  connect(switchCpCtrlA.u2,cpCtrlOffA.y)
    annotation (Line(points={{64.74918343634461,8.76246419998916},{59.27564006636803,8.76246419998916},{59.27564006636803,10.737093166305424},{53.802096696391445,10.737093166305424}},color={255,0,255}));
  connect(switchCpCtrlA.y,compressor_crkA)
    annotation (Line(points={{78.62142382936482,8.76246419998916},{95.81932277834565,8.76246419998916},{95.81932277834565,9.899056104083773},{113.0172217273265,9.899056104083773}},color={0,0,127}));
  connect(switchCpCtrlA.u3,completeCompressorControl_base.actuatorSignal_crkA)
    annotation (Line(points={{64.74918343634461,3.937337106764751},{19.505913370204283,3.937337106764751},{19.505913370204283,8.133926621624},{13.73437158039144,8.133926621624}},color={0,0,127}));
  connect(switchCpCtrlB.u2,cpCtrlOffB.y)
    annotation (Line(points={{65.32170646539389,-11.596079813914002},{59.58142227889019,-11.596079813914002},{59.58142227889019,-10.154932480651997},{53.841138092386494,-10.154932480651997}},color={255,0,255}));
  connect(switchCpCtrlB.y,compressor_crkB)
    annotation (Line(points={{79.1939468584141,-11.596079813914002},{95.30847142424184,-11.596079813914002},{95.30847142424184,-11.496800875063183},{111.42299599006961,-11.496800875063183}},color={0,0,127}));
  connect(fan_crkB,switchFanCtrlB.y)
    annotation (Line(points={{111.4229959900697,-37.52465393949728},{95.98043556242452,-37.52465393949728},{95.98043556242452,-37.74551899183783},{80.53787513477934,-37.74551899183783}},color={0,0,127}));
  connect(switchFanCtrlB.u3,fanControl_crkB.actuatorSignal)
    annotation (Line(points={{66.66563474175913,-32.92039189861342},{66.66563474175913,-26.920391898613417},{16,-26.920391898613417},{16,-30},{10,-30}},color={0,0,127}));
  connect(fanCtrlOffB.y,switchFanCtrlB.u2)
    annotation (Line(points={{53.70243558327027,-37.745518991837805},{66.66563474175913,-37.74551899183783}},color={255,0,255}));
  connect(switchFanCtrlB.u1,fan_actuatorB.value)
    annotation (Line(points={{66.66563474175913,-42.570646085062236},{66.66563474175913,-50.95308227760499},{47.30288048243286,-50.95308227760499}},color={0,0,127}));
  connect(switchEXVCtrlB.u3,eXVControl_crkB.actuatorSignal)
    annotation (Line(points={{65.37656528390659,-62.04535479006627},{65.37656528390659,-56.04535479006627},{16,-56.04535479006627},{16,-60},{10,-60}},color={0,0,127}));
  connect(eXVControl_crkB.actuatorSignal_sst,sstmax_crkB)
    annotation (Line(points={{10,-62.6},{25.437648275479916,-62.6},{25.437648275479916,-79.25902537087708},{110.35603272396114,-79.25902537087708}},color={0,0,127}));
  connect(exv_actuatorB.value,switchEXVCtrlB.u1)
    annotation (Line(points={{50.09775560592639,-76.57738350775804},{65.37656528390659,-76.57738350775804},{65.37656528390659,-71.69560897651509}},color={0,0,127}));
  connect(switchEXVCtrlB.u2,eXVCtrlOffB.y)
    annotation (Line(points={{65.37656528390659,-66.87048188329068},{55.6142559237432,-66.87048188329068}},color={255,0,255}));
  connect(switchEXVEcoCtrlB.y,ecoExv_crkB)
    annotation (Line(points={{80.31576894303534,-100.33477575442255},{95.9418374861697,-100.33477575442255},{95.9418374861697,-98.32598863698561},{111.56790602930407,-98.32598863698561}},color={0,0,127}));
  connect(switchEXVCtrlB.y,exv_crkB)
    annotation (Line(points={{79.24880567692679,-66.87048188329068},{94.26893756738968,-66.87048188329068},{94.26893756738968,-64.99339680225692},{109.28906945785259,-64.99339680225692}},color={0,0,127}));
  connect(switchEXVEcoCtrlB.u3,ecoEXVControl_crkB.actuatorSignal)
    annotation (Line(points={{66.44352855001513,-95.50964866119814},{66.44352855001513,-89.50964866119814},{15.46651836694577,-89.50964866119814},{15.46651836694577,-95.86829796359686},{9.46651836694577,-95.86829796359686}},color={0,0,127}));
  connect(eXVEcoCtrlOffB.y,switchEXVEcoCtrlB.u2)
    annotation (Line(points={{59.34862735512303,-100.86825738747679},{62.89607795256908,-100.86825738747679},{62.89607795256908,-100.33477575442255},{66.44352855001513,-100.33477575442255}},color={255,0,255}));
  connect(switchEXVEcoCtrlB.u1,exvEco_actuatorB.value)
    annotation (Line(points={{66.44352855001513,-105.15990284764696},{66.44352855001513,-112.47537577408123},{54.549517153448576,-112.47537577408123}},color={0,0,127}));
  connect(cp_actuatorB.value,switchCpCtrlB.u1)
    annotation (Line(points={{46.37461972544071,-22.87133058276231},{65.32170646539389,-22.87133058276231},{65.32170646539389,-16.421206907138412}},color={0,0,127}));
  connect(completeCompressorControl_base.actuatorSignal_crkB,switchCpCtrlB.u3)
    annotation (Line(points={{13.73437158039144,-3.866073557189935},{19.73437158039144,-3.866073557189935},{19.73437158039144,-0.7709527206895928},{65.32170646539389,-0.7709527206895928},{65.32170646539389,-6.770952720689593}},color={0,0,127}));
end ControllerBase_opti;
