within Workspace.Auxiliary.Records;
record Optimization_summary
  extends.Modelica.Icons.Record;
  output Real EER;
  output Real EER_crkA;
  output Real EER_crkB;
  output.Modelica.SIunits.Power Q_flow_total;
  output.Modelica.SIunits.Power Q_flow_crkA;
  output.Modelica.SIunits.Power Q_flow_crkB;
  output.Modelica.SIunits.Power P_flow_total;
  output.Modelica.SIunits.Power P_flow_crkA;
  output.Modelica.SIunits.Power P_flow_crkB;
end Optimization_summary;
