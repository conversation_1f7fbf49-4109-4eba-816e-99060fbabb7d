within KAV_initiation.System30KAV.Controller30KAV.SubSystems.BaseClasses;
partial model CompleteCompressorControlBase
  extends BOLT.InternalLibrary.BuildingBlocks.Icons.Compressor;
  parameter Boolean crkA_isOff=false;
  parameter Boolean crkB_isOff=false;
  parameter Real crkA_min_speed=23
    "Minimum speed of compressor A (Hz)";
  parameter Real crkB_min_speed=27
    "Minimum speed of compressor B (Hz)";
  parameter Real crkA_max_speed=95
    "Maximum speed of compressor A (Hz)";
  parameter Real crkB_max_speed=105
    "Maximum speed of compressor B (Hz)";
  parameter Real gain_capacity;
  replaceable.Workspace.Controller.SubSystems.BaseClasses.CompressorControlBase compressorControl_crkA(
    crkIsOff=crkA_isOff,
    compressorFrequency_min=crkA_min_speed,
    compressorFrequency_max=crkA_max_speed)
    annotation (Placement(transformation(extent={{-18.0,42.000000000000014},{18.0,77.99999999999999}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus_crkA
    annotation (Placement(transformation(extent={{-124.28999553840198,59.71000446159802},{-83.71000446159802,100.28999553840198}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_crkA
    annotation (Placement(transformation(extent={{-84,36},{-64,56}},origin={-26,-6},rotation=0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal_crkA
    annotation (Placement(transformation(extent={{28.0,54.0},{48.0,74.0}},origin={62,-4},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus_crkB
    annotation (Placement(transformation(extent={{-120.2690746423837,-61.2690746423837},{-79.7309253576163,-20.7309253576163}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_crkB
    annotation (Placement(transformation(extent={{-80.0,-63.0},{-60.0,-43.0}},origin={-30,-28},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.CompressorControlBase compressorControl_crkB(
    crkIsOff=crkB_isOff,
    compressorFrequency_min=crkB_min_speed,
    compressorFrequency_max=crkB_max_speed)
    annotation (Placement(transformation(extent={{-18.0,-78.0},{18.0,-42.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal_crkB
    annotation (Placement(transformation(extent={{28.0,-58.0},{48.0,-38.0}},origin={62,-12},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation capacity_error(
    ID=1,
    measurement=capacity,
    setPoint=capacity_setpoint,
    gain=1/500000)
    annotation (Placement(transformation(extent={{-82.1111539986356,-9.620605},{-37.8888460013644,13.620605}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController capacity_controller(
    AV_min=0,
    AV_max=1)
    annotation (Placement(transformation(extent={{-18.087600000000002,-10.087684},{2.087600000000002,10.087684}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.SubSystems.BaseClasses.LoadDistribution loadDistribution(
    crkA_max_speed=crkA_max_speed,
    crkB_max_speed=crkB_max_speed,
    crkA_min_speed=crkA_min_speed,
    crkB_min_speed=crkB_min_speed)
    annotation (Placement(transformation(extent={{20.0,-10.0},{40.0,10.0}},origin={0.0,0.0},rotation=0.0)));
protected
  .Modelica.Blocks.Interfaces.RealOutput capacity
    annotation (Placement(transformation(extent={{-55.923164602347825,78.07683539765218},{-36.076835397652175,97.92316460234782}},origin={-24,-68},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput capacity_setpoint
    annotation (Placement(transformation(extent={{-79.92316460234782,-27.923164602347825},{-60.076835397652175,-8.076835397652175}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(compressorControl_crkA.measurementBus,measurementBus_crkA)
    annotation (Line(points={{-18,70.79999999999998},{-104,70.79999999999998},{-104,80}},color={255,204,51}));
  connect(compressorControl_crkB.measurementBus,measurementBus_crkB)
    annotation (Line(points={{-18,-49.199999999999996},{-100,-49.199999999999996},{-100,-41}},color={255,204,51}));
  connect(compressorControl_crkB.limitsBus,limitsBus_crkB)
    annotation (Line(points={{-18,-70.8},{-100,-70.8},{-100,-81}},color={255,204,51}));
  connect(compressorControl_crkA.limitsBus,limitsBus_crkA)
    annotation (Line(points={{-18,49.20000000000001},{-100,49.20000000000001},{-100,40}},color={255,204,51}));
  connect(capacity,measurementBus_crkA.capacity)
    annotation (Line(points={{-70,20},{-74.30284495189272,20},{-74.30284495189272,80},{-104,80}},color={0,0,127}));
  connect(capacity_setpoint,limitsBus_crkA.capacity_setpoint)
    annotation (Line(points={{-70,-18},{-99.95,-18},{-99.95,40.05}},color={0,0,127}));
  connect(capacity_error.sensor,capacity_controller.errorSignal)
    annotation (Line(points={{-38.33106908133711,1.0703516},{-18.087600000000002,1.0703516},{-18.087600000000002,0}},color={28,108,200}));
  connect(compressorControl_crkA.actuatorSignal,actuatorSignal_crkA)
    annotation (Line(points={{18,60},{100,60}},color={0,0,127}));
  connect(compressorControl_crkB.actuatorSignal,actuatorSignal_crkB)
    annotation (Line(points={{18,-60},{100,-60}},color={0,0,127}));
  connect(loadDistribution.crkA_speed,compressorControl_crkA.compressorFrequency)
    annotation (Line(points={{40,6},{50,6},{50,32},{-34,32},{-34,60},{-18,60}},color={0,0,127}));
  connect(loadDistribution.crkB_speed,compressorControl_crkB.compressorFrequency)
    annotation (Line(points={{40,-6},{50,-6},{50,-32.2},{-34,-32.2},{-34,-60},{-18,-60}},color={0,0,127}));
  connect(capacity_controller.actuatorSignal,loadDistribution.normalized_total_load)
    annotation (Line(points={{2.6928560000000026,0},{20,0}},color={0,0,127}));
end CompleteCompressorControlBase;
