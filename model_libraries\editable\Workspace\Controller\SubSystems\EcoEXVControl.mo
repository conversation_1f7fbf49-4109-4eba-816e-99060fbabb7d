within KAV_initiation.System30KAV.Controller30KAV.SubSystems;
model EcoEXVControl
  extends.Workspace.Controller.SubSystems.BaseClasses.EcoEXVControlBase;
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController(
    AV_min=0.05,
    AV_max=1,
    AV_start=0.15,
    isOff=crkIsOff)
    annotation (Placement(transformation(extent={{51.73206301429336,-10.267936985706644},{72.26793698570664,10.267936985706644}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation dT_esh_error(
    ID=1,
    measurement=dT_esh,
    setPoint=dT_esh_setpoint,
    gain=-1/10)
    annotation (Placement(transformation(extent={{-83.97508940311462,-10.462539837935777},{-16.02491059688537,14.462539837935777}},origin={0,20},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation ecoEXV_opening_error(
    gain=1,
    setPoint=ecoEXV_max_opening,
    ID=2,
    measurement=actuatorSignal)
    annotation (Placement(transformation(extent={{-84.6306799617937,11.537460162064221},{-15.369320038206304,36.46253983793578}},origin={0,-24},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min
    annotation (Placement(transformation(extent={{5.146040721110445,9.146040721110445},{22.853959278889555,26.853959278889555}},origin={0,-2},rotation=0.0)));
protected
  .Modelica.Blocks.Interfaces.RealOutput dT_esh
    "Discharge superheat"
    annotation (Placement(transformation(extent={{-78.49941158124145,47.500588418758554},{-53.500588418758554,72.49941158124145}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput dT_esh_setpoint
    "Discharge superheat"
    annotation (Placement(transformation(extent={{-78.49941158124145,-72.49941158124145},{-53.500588418758554,-47.500588418758554}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput ecoEXV_max_opening
    "Discharge superheat"
    annotation (Placement(transformation(extent={{-78.49941158124145,-52.499411581241446},{-53.500588418758554,-27.500588418758554}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(setpointController.actuatorSignal,actuatorSignal)
    annotation (Line(points={{72.88401320484904,0},{100,0}},color={0,0,127}));
  connect(dT_esh,measurementBus.dT_esh)
    annotation (Line(points={{-66,60},{-99.9,60},{-99.9,60.1}},color={0,0,127}));
  connect(dT_esh_setpoint,limitsBus.dT_esh_setpoint)
    annotation (Line(points={{-66,-60},{-83,-60},{-83,-59.95},{-99.95,-59.95}},color={0,0,127}));
  connect(min.y,setpointController.errorSignal)
    annotation (Line(points={{22.854,16},{34.7572,16},{34.7572,0},{51.73206301429336,0}},color={28,108,200}));
  connect(ecoEXV_max_opening,limitsBus.ecoEXV_max_opening)
    annotation (Line(points={{-66,-40},{-99.95,-40},{-99.95,-59.95}},color={0,0,127}));
  connect(dT_esh_error.sensor,min.u1)
    annotation (Line(points={{-16.7044,21.003},{-6.3522,21.003},{-6.3522,20.9582},{5.14604,20.9582}},color={28,108,200}));
  connect(ecoEXV_opening_error.sensor,min.u2)
    annotation (Line(points={{-16.0619,-0.997003},{-4.03095,-0.997003},{-4.03095,10.6876},{5.14604,10.6876}},color={28,108,200}));
end EcoEXVControl;
