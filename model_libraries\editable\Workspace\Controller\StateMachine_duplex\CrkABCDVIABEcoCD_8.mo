within StateMachine;
record CrkABCDVIABEcoCD_8
  import BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod;
  extends Mode30XBVBase(
    final modeID=ModeID30XBV.CrkABCDVIABEcoCD_8,
    final CrkA=true,
    final CrkB=true,
    final CrkC=true,
    final CrkD=true,
    final EcoA=false,
    final EcoB=false,
    final EcoC=true,
    final EcoD=true,
    final ViA=true,
    final ViB=true,
    final ViC=false,
    final ViD=false,
    initMethod=ModeInitMethod.External);
end CrkABCDVIABEcoCD_8;
