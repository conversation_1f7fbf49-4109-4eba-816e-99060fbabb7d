within Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM;
record Configuration
  "Configuration of surrogate"
  parameter SurrogateInput Mean
    annotation (Placement(transformation(extent={{-80,80},{-60,100}})));
  parameter SurrogateInput Std
    annotation (Placement(transformation(extent={{-80,80},{-60,100}})));
  parameter LayerBase layer_Tsat_in(
    Mean=Mean,
    Std=Std)
    annotation (Placement(transformation(extent={{-80,80},{-60,100}})));
  parameter LayerBase layer_dp_air(
    Mean=Mean,
    Std=Std)
    annotation (Placement(transformation(extent={{-80,80},{-60,100}})));
  parameter LayerBase layer_delta_Tsat(
    Mean=Mean,
    Std=Std)
    annotation (Placement(transformation(extent={{-80,80},{-60,100}})));
  parameter LayerBase layer_mass_ref(
    Mean=Mean,
    Std=Std)
    annotation (Placement(transformation(extent={{-80,80},{-60,100}})));
end Configuration;
