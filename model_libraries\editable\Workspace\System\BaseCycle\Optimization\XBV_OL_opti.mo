within Workspace.System.BaseCycle.Optimization;
model XBV_OL_opti
  extends.Workspace.Controller.StateMachine.StateMachine30KAV(
    UseStateMachine=true,
    StateMachine(
      currentModeID=.Workspace.Controller.StateMachine.ModeID30KAV.CrkA_10));
  extends.Workspace.System.BaseCycle.XBV_2C.OpenLoop_EN14511(
    motorA(
      use_scaling_motor_in=false),
    FanA(
      use_scalingFactor_in=false),
    FanB(
      use_scalingFactor_in=false),
    motorB(
      use_scaling_motor_in=false),
    //frq_fan_A(y = FanA_sp.setPoint),
    //frq_fan_B(y = FanB_sp.setPoint),
    n_passes_evap=2,
    condAirA(
      T_a_start_air=globalParameters.T_ambient,
      T_b_start_air=globalParameters.T_ambient+10.0,
      Tsat_a_start_ref=globalParameters.Tsat_cond_start,
      Tsat_b_start_ref=globalParameters.Tsat_cond_start-1.0,
      Z_U=0.74,
      use_Z_U_expression=false,
      use_Z_dpr_expression=false,
      n_passes={2},
      n_tubes={{90,30}},
      n_inlets={2},
      n_outlets={1},
      selector_curve=.BOLT.InternalLibrary.HeatExchangers.RefAir.DataBase.MCHXGeo.SelectorCurve.MCHXCurve90,
      tubeOrientation=.BOLT.InternalLibrary.BuildingBlocks.Types.TubeOrientation.Vertical_inletTop,
      userDefined_geo(
        length_tube=1.937,
        diameter_in_header=0.02395,
        length_tube_inlet=0.02286,
        diameter_in_inletTube=0.0157,
        length_outlet=0.02286,
        diameter_in_outlet=0.0157),
      dehumidifying=false,
      fastMode=true,
      selector_curve_num=106,
      Z_dpr_fixed=true,
      Z_dpa_fixed=true),
    condAirB(
      T_a_start_air=globalParameters.T_ambient,
      T_b_start_air=globalParameters.T_ambient+10.0,
      Tsat_a_start_ref=globalParameters.Tsat_cond_start,
      Tsat_b_start_ref=globalParameters.Tsat_cond_start-1.0,
      Z_U=0.74,
      use_Z_U_expression=false,
      use_Z_dpr_expression=false,
      n_passes={2},
      n_tubes={{90,30}},
      n_inlets={2},
      n_outlets={1},
      selector_curve=.BOLT.InternalLibrary.HeatExchangers.RefAir.DataBase.MCHXGeo.SelectorCurve.MCHXCurve90,
      tubeOrientation=.BOLT.InternalLibrary.BuildingBlocks.Types.TubeOrientation.Vertical_inletTop,
      userDefined_geo(
        length_tube=1.937,
        diameter_in_header=0.02395,
        length_tube_inlet=0.02286,
        diameter_in_inletTube=0.0157,
        length_outlet=0.02286,
        diameter_in_outlet=0.0157),
      dehumidifying=false,
      fastMode=true,
      selector_curve_num=106),
    Frq_Comp_B=70,
    CompressorA(
      selector_comp_NG1_NG2=choiceBlock.Unit.selector_Comp_CKA,
      voltage=choiceBlock.Unit.CompVoltage_CKA,
      is_NG3=
        if choiceBlock.Unit.CompType_CKA ==.Workspace.Controller.Components.Types.CompressorSelector.NG3 then
          true
        else
          false),
    CompressorB(
      selector_comp_NG1_NG2=choiceBlock.Unit.selector_Comp_CKB,
      is_NG3=
        if choiceBlock.Unit.CompType_CKB ==.Workspace.Controller.Components.Types.CompressorSelector.NG3 then
          true
        else
          false,
      voltage=choiceBlock.Unit.CompVoltage_CKB),
    VFDA(
      selector_GenScrew=choiceBlock.Unit.selector_VFDA_CKA),
    VFDB(
      selector_GenScrew=choiceBlock.Unit.selector_VFDA_CKB),
    NcompAMax=choiceBlock.Unit.NcompMax_CKA,
    NcompAMin=choiceBlock.Unit.NcompMin_CKA,
    NcompBMax=choiceBlock.Unit.NcompMax_CKB,
    NcompBMin=choiceBlock.Unit.NcompMin_CKB,
    BPHEECOB(
      selectGeo=choiceBlock.Unit.Eco_Geo_CKB,
      nPlate=choiceBlock.Unit.eco_nPlate_CKB,
      Dport_ref_H_a=choiceBlock.Unit.eco_DportH_a_B,
      Dport_ref_H_b=choiceBlock.Unit.eco_DportH_b_B,
      Dport_ref_L_a=choiceBlock.Unit.eco_DportL_a_B,
      Dport_ref_L_b=choiceBlock.Unit.eco_DportL_b_B),
    BPHEECOA(
      selectGeo=choiceBlock.Unit.Eco_Geo_CKA,
      nPlate=choiceBlock.Unit.eco_nPlate_CKA,
      Dport_ref_H_a=choiceBlock.Unit.eco_DportH_a_A,
      Dport_ref_H_b=choiceBlock.Unit.eco_DportH_b_A,
      Dport_ref_L_a=choiceBlock.Unit.eco_DportL_a_A,
      Dport_ref_L_b=choiceBlock.Unit.eco_DportL_b_A),
    evaporator(
      userDefined_geo1(
        n_tubes_pass1=integer(
          (choiceBlock.Unit.evap_n_tubes_pass1+choiceBlock.Unit.evap_n_tubes_pass2)/2),
        n_tubes_pass2=integer(
          (choiceBlock.Unit.evap_n_tubes_pass1+choiceBlock.Unit.evap_n_tubes_pass2)/2),
        diameter_in_shell=choiceBlock.Unit.evap_diameter_in_shell,
        length_tube=choiceBlock.Unit.evap_length_tube_CKA),
      userDefined_geo2(
        length_tube=choiceBlock.Unit.evap_length_tube_CKB,
        diameter_in_shell=choiceBlock.Unit.evap_diameter_in_shell,
        n_tubes_pass1=integer(
          (choiceBlock.Unit.evap_n_tubes_pass1+choiceBlock.Unit.evap_n_tubes_pass2)/(2)),
        n_tubes_pass2=integer(
          (choiceBlock.Unit.evap_n_tubes_pass1+choiceBlock.Unit.evap_n_tubes_pass2)/(2))),
      LTD1=2,
      isOff_1=isOffA,
      isOff_2=isOffB),
    oilseparatorA(
      Di=choiceBlock.Unit.OilSepDiameter_CKA,
      length=choiceBlock.Unit.OilSepLength_CKA,
      dp_reference=choiceBlock.Unit.Oil_sepA_DP,
      m_flow_reference=choiceBlock.Unit.Oil_sepA_MF),
    oilseparatorB(
      Di=choiceBlock.Unit.OilSepDiameter_CKB,
      length=choiceBlock.Unit.OilSepLength_CKB,
      dp_reference=choiceBlock.Unit.Oil_sepB_DP,
      m_flow_reference=choiceBlock.Unit.Oil_sepB_MF),
    suctionlineA(
      dp_reference=choiceBlock.Unit.Suc_lineA_DP,
      m_flow_reference=choiceBlock.Unit.Suc_lineA_MF),
    suctionlineB(
      dp_reference=choiceBlock.Unit.Suc_lineB_DP,
      m_flow_reference=choiceBlock.Unit.Suc_lineB_MF),
    dischargelineA(
      dp_reference=choiceBlock.Unit.Dis_lineA_DP,
      m_flow_reference=choiceBlock.Unit.Dis_lineA_MF),
    dischargelineB(
      dp_reference=choiceBlock.Unit.Dis_lineB_DP,
      m_flow_reference=choiceBlock.Unit.Dis_lineB_MF),
    EXVMainA(
      selector_flowData=.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector.VPF_250_Forward),
    EXVECOB(
      selector_flowData=.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector.VPF_50_Forward),
    EXVMainB(
      selector_flowData=.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector.VPF_250_Forward),
    isOffA=not StateMachine.currentMode.CrkA,
    isOffB=not StateMachine.currentMode.CrkB,
    isOffECOA=not StateMachine.currentMode.EcoA,
    isOffECOB=not StateMachine.currentMode.EcoB,
    Vi_A=StateMachine.currentMode.ViA,
    Vi_B=StateMachine.currentMode.ViB,
    max_motor_frequency=60,
    Frq_Comp_A=70,
    OAT=15,
    frq_fan_B(
      y=motorB.summary.Motor_freq),
    frq_fan_A(
      y=motorA.summary.Motor_freq),
    SubcoolingA=-5,
    SubcoolingB=-5,
    globalParameters(
      capacity_design=capacity_design));
  .BOLT.Control.SteadyState.SetpointControl.Actuator CpA_sp(
    setPoint=Frq_Comp_A,
    minBound=17,
    maxBound=105,
    isOff=isOffA)
    annotation (Placement(transformation(extent={{-80.140740360508,98.67787721970089},{-44.7197936322338,134.0988239479751}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator CpB_sp(
    maxBound=105,
    minBound=17,
    setPoint=Frq_Comp_B,
    isOff=isOffB)
    annotation (Placement(transformation(extent={{43.69254808966991,90.58964503924884},{8.27160136139571,126.01059176752304}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController ecoEXV_A_PI(
    isOff=isOffA or isOffECOA,
    AV_start=0.7,
    AV_value_off=0.5,
    AV_min=0.05,
    AV_max=1)
    annotation (Placement(transformation(extent={{-312.70225090159255,48.60670760478638},{-292.70225090159255,68.60670760478638}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController mainEXV_A_PI(
    AV_min=0.05,
    AV_max=1,
    isOff=isOffA,
    AV_start=0.7)
    annotation (Placement(transformation(extent={{-312.0,-12.0},{-292.0,8.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation error_ecoEXV_A(
    gain=-0.1,
    setPoint=eco_ssh_A,
    measurement=nodeBPHEECOoutA.dTsh,
    isOff=isOffA or isOffECOA)
    annotation (Placement(transformation(extent={{-355.2856833544213,50.84220507589757},{-335.2856833544213,70.84220507589757}},origin={0,0},rotation=0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation error_mainEXV_A(
    gain=0.1,
    setPoint=SubcoolingA,
    isOff=isOffA,
    measurement=nodecondAiroutA.dTsh)
    annotation (Placement(transformation(extent={{-354.1679346188657,-11.751724115214842},{-334.1679346188657,8.248275884785158}},origin={0,0},rotation=0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController mainEXV_B_PI(
    AV_min=0.05,
    AV_max=1,
    AV_start=0.7,
    isOff=isOffB)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={288.6215466884229,7.044861376369866},rotation=-180.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController ecoEXV_B_PI(
    AV_max=1,
    AV_min=0.05,
    AV_value_off=0.5,
    AV_start=0.7,
    isOff=isOffB or isOffECOB)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={286.03556067854987,47.35564127546502},rotation=-180.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation error_ecoEXV_B(
    isOff=isOffB or isOffECOB,
    measurement=nodeBPHEECOoutB.dTsh,
    setPoint=eco_ssh_B,
    gain=-0.1)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={328.6189931313786,45.12014380435383},rotation=-180.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation error_main_EXVB(
    measurement=nodecondAiroutB.dTsh,
    isOff=isOffB,
    setPoint=SubcoolingB,
    gain=0.1)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={332.0,4.0},rotation=-180.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator SSTmaxA(
    setPoint=1,
    minBound=0.1,
    maxBound=1)
    annotation (Placement(transformation(extent={{-52.993236191348956,398.55923156991184},{-88.41418291962316,433.98017829818605}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator SSTmaxB(
    maxBound=1,
    minBound=0.1,
    setPoint=1)
    annotation (Placement(transformation(extent={{-74.25458860217176,387.71600600774127},{-109.67553533044597,423.1369527360155}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.OptionBlock.HighAmbiantTemperature.ChoiceBlock choiceBlock(
    SixtyHz_selector=.Workspace.Auxiliary.OptionBlock.SixtyHzOption.SixtyHz_selector.SixtyHz,
    Selector=.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Selector.Unit_500)
    annotation (Placement(transformation(extent={{93.92669327688974,448.13665565312726},{171.14908614094736,525.3590485171849}},origin={0.0,0.0},rotation=0.0)));
  //output Real VI_A;
  //output Real VI_B;
  parameter Real[15] NG1={0.8,-0.00116,-0.000007,5.82,-1.939,0.1685,0.5217,0.5842,-0.0815,-0.00014,0.000318,0.00003,-0.000008,-0.00851,-0.02069};
  parameter Real[15] NG2={1.038,-0.00457,0.000008,3.465,-0.84,0.05491,0.3949,0.679,-0.0675,-0.00137,0.00029,0.000049,0.000004,-0.00725,-0.02015};
  parameter Real[15] NG3={1.2315,-0.00584,-0.000009,5.396,-0.689,0.03293,0.3352,-0.0458,-0.12187,0.01344,0.000885,-0.000095,0.000032,-0.001057,-0.0068};
  parameter Real[15] fanCoefficients_new={0.8,-0.00116,-0.000007,5.82,-1.939,0.1685,0.5217,0.5842,-0.0815,-0.00014,0.000318,0.00003,-0.000008,-0.00851,-0.02069};
  parameter Boolean use_new_fanCoefficients=false;
  parameter Real[15] fanCoefficients=
    if use_new_fanCoefficients then
      fanCoefficients_new
    else
      if choiceBlock.Unit.CompType_CKA ==.Workspace.Controller.Components.Types.CompressorSelector.NG3 then
        NG3
      elseif choiceBlock.Unit.CompType_CKA ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then
        NG2
      else
        NG1;
  parameter Boolean Use_fancurves=false;
  parameter Real Frq_Fan_A=50;
  parameter Real Frq_Fan_B=50;
  parameter Real capacity_design=choiceBlock.Unit.Capacity_design;
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation error_Fan_A(
    isOff=isOffA,
    measurement=motorA.summary.Motor_freq,
    gain=0.2,
    setPoint=
      if Use_fancurves then
        Fan_speed_A
      else
        Frq_Fan_A)
    annotation (Placement(transformation(extent={{-359.8632246655944,286.1014532680274},{-339.8632246655944,306.1014532680274}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController Fan_A_PI(
    AV_max=60,
    AV_min=10,
    AV_start=60,
    isOff=isOffA)
    annotation (Placement(transformation(extent={{-318.51829496684377,288.0142088470193},{-298.51829496684377,308.0142088470193}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController Fan_A_PI2(
    isOff=isOffB,
    AV_start=60,
    AV_min=10,
    AV_max=60)
    annotation (Placement(transformation(extent={{289.4708300874953,340.3474017328978},{269.4708300874953,360.3474017328978}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation error_Fan_A2(
    setPoint=
      if Use_fancurves then
        Fan_speed_B
      else
        Frq_Fan_B,
    gain=0.2,
    measurement=motorB.summary.Motor_freq,
    isOff=isOffB)
    annotation (Placement(transformation(extent={{341.86316659764077,342.50724158647114},{321.86316659764077,362.50724158647114}},origin={0.0,0.0},rotation=0.0)));
  parameter Real ECO_ON_inter=139.5
    "Value of the OAT max to turn on the ECO at 273.15 K for LWT";
  parameter Real ECO_ON_slope=0.523
    "Slope for the boundary to turn off the ECO according to the OAT and the LWT";
  output Real Fan_speed_A;
  output Real Fan_speed_B;
  // Optimization
  .Workspace.Auxiliary.Records.Optimization_summary Optimization_Outputs
    annotation (Placement(transformation(extent={{-135.50603278070457,471.3956817518385},{-83.63547391170412,523.266240620839}},origin={0.0,0.0},rotation=0.0)));
  output.Modelica.SIunits.Temperature LWT_out;
  output.Modelica.SIunits.Temperature OAT_out;
  output Real EER_out;
  output Real EER_crkA_out;
  output Real EER_crkB_out;
  output.Modelica.SIunits.Temperature EWT_out;
  output.Modelica.SIunits.Power P_flow_total_out;
  output.Modelica.SIunits.Power Q_flow_total_out;
  output.Modelica.SIunits.Power Q_flow_crkA_out;
  output.Modelica.SIunits.Power Q_flow_crkB_out;
  output.Modelica.SIunits.Power P_flow_crkA_out;
  output.Modelica.SIunits.Power P_flow_crkB_out;
  output Real Fan_speed_A_out;
  output Real Fan_speed_B_out;
equation
  ECO_ON_A=
    if sourceAirA.summary.Tdb < ECO_ON_slope*sinkbrine.summary.T+ECO_ON_inter then
      CompressorA.summary.PR > 2.7 and CompressorA.summary.Ncomp/NcompAMax > 0.3
    else
      CompressorA.summary.PR > 2.3 and CompressorA.summary.Ncomp/NcompAMax > 0.3;
  ECO_ON_B=
    if sourceAirA.summary.Tdb < ECO_ON_slope*sinkbrine.summary.T+ECO_ON_inter then
      CompressorB.summary.PR > 2.7 and CompressorB.summary.Ncomp/NcompAMax > 0.3
    else
      CompressorB.summary.PR > 2.3 and CompressorB.summary.Ncomp/NcompAMax > 0.3;
  CAP_LOWsat=systemVariables.CoolCap_total < 0.23*globalParameters.capacity_design;
  //VI_A    = .Workspace.Controller.StateMachine.isVIOn( Frq_Comp_A,
  //                                                    NcompAMax,
  //                                                    NcompAMin,
  //                                                    nodeCpoutA.summary.Tsat,
  //                                                    nodeCpinA.summary.Tsat)  ;
  VI_ON_A=.Workspace.Controller.StateMachine.isVIOn(
    Frq_Comp_A,
    NcompAMax,
    NcompAMin,
    nodeCpoutA.summary.Tsat,
    nodeCpinA.summary.Tsat) > 0.5;
  //VI_B    = .Workspace.Controller.StateMachine.isVIOn( Frq_Comp_B,
  //                                                    NcompBMax,
  //                                                    NcompBMin,
  //                                                    nodeCpoutB.summary.Tsat,
  //                                                    nodeCpinB.summary.Tsat);
  VI_ON_B=.Workspace.Controller.StateMachine.isVIOn(
    Frq_Comp_B,
    NcompBMax,
    NcompBMin,
    nodeCpoutB.summary.Tsat,
    nodeCpinB.summary.Tsat) > 0.5;
  Fan_speed_A=.BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax(
    0,
    .BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMin(
      max_motor_frequency,
      .Workspace.Controller.Components.Functions.fanSpeed(
        Frq_Comp_A,
        n_coilsA,
        LWT+273.15,
        OAT+273.15,
        fanCoefficients),
      1e-4),
    1e-4);
  Fan_speed_B=.BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax(
    0,
    .BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMin(
      max_motor_frequency,
      .Workspace.Controller.Components.Functions.fanSpeed(
        Frq_Comp_B,
        n_coilsB,
        LWT+273.15,
        OAT+273.15,
        fanCoefficients),
      1e-4),
    1e-4);
  // Optimization
  LWT_out=sinkbrine.summary.T;
  OAT_out=sourceAirA.summary.Tdb;
  EWT_out=sourcebrine.summary.T;
  Q_flow_total_out=systemVariables.summary.CoolCap_total;
  Q_flow_crkA_out=systemVariables.summary.CoolCap[1];
  Q_flow_crkB_out=systemVariables.summary.CoolCap[2];
  P_flow_total_out=systemVariables.summary.pow_total;
  P_flow_crkA_out=systemVariables.summary.pow[1];
  P_flow_crkB_out=systemVariables.summary.pow[2];
  EER_out=Q_flow_total_out/P_flow_total_out;
  EER_crkA_out=Q_flow_crkA_out/P_flow_crkA_out;
  EER_crkB_out=Q_flow_crkB_out/P_flow_crkB_out;
  Fan_speed_A_out=motorA.summary.Motor_freq;
  Fan_speed_B_out=motorB.summary.Motor_freq;
  Optimization_Outputs.EER=EER_out;
  Optimization_Outputs.EER_crkA=EER_crkA_out;
  Optimization_Outputs.EER_crkB=EER_crkB_out;
  Optimization_Outputs.P_flow_total=P_flow_total_out;
  Optimization_Outputs.P_flow_crkA=P_flow_crkA_out;
  Optimization_Outputs.P_flow_crkB=P_flow_crkB_out;
  Optimization_Outputs.Q_flow_total=Q_flow_total_out;
  Optimization_Outputs.Q_flow_crkA=Q_flow_crkA_out;
  Optimization_Outputs.Q_flow_crkB=Q_flow_total_out;
  connect(CompressorB.N_speed,CpB_sp.value)
    annotation (Line(points={{47.11909060795743,125.18714238393311},{47.11909060795743,108.30011840338594},{34.83731140760136,108.30011840338594}},color={0,0,127}));
  connect(CompressorA.N_speed,CpA_sp.value)
    annotation (Line(points={{-85.5296104182744,127.83224494300265},{-85.5296104182744,116.38835058383799},{-71.28550367843945,116.38835058383799}},color={0,0,127}));
  connect(EXVECOB.openCommand,ecoEXV_B_PI.actuatorSignal)
    annotation (Line(points={{243.9,54},{259.66778033927494,54},{259.66778033927494,47.35564127546502},{275.43556067854985,47.35564127546502}},color={0,0,127}));
  connect(error_ecoEXV_B.sensor,ecoEXV_B_PI.errorSignal)
    annotation (Line(points={{318.81899313137865,45.920143804353856},{307.42727690496423,45.920143804353856},{307.42727690496423,47.35564127546503},{296.03556067854987,47.35564127546503}},color={28,108,200}));
  connect(mainEXV_B_PI.actuatorSignal,EXVMainB.openCommand)
    annotation (Line(points={{278.02154668842286,7.044861376369865},{260.9607733442114,7.044861376369865},{260.9607733442114,-12},{243.9,-12}},color={0,0,127}));
  connect(error_main_EXVB.sensor,mainEXV_B_PI.errorSignal)
    annotation (Line(points={{322.2,4.799999999999999},{309.3998982962011,4.799999999999999},{309.3998982962011,7.044861376369866},{298.6215466884229,7.044861376369866}},color={28,108,200}));
  connect(mainEXV_A_PI.actuatorSignal,EXVMainA.openCommand)
    annotation (Line(points={{-291.4,-2},{-284.0556157016546,-2},{-284.0556157016546,-4.000000000000001},{-275.9,-4.000000000000001}},color={0,0,127}));
  connect(error_mainEXV_A.sensor,mainEXV_A_PI.errorSignal)
    annotation (Line(points={{-334.36793461886566,-2.5517241152148413},{-323.5895830110875,-2.5517241152148413},{-323.5895830110875,-2},{-312,-2}},color={28,108,200}));
  connect(ecoEXV_A_PI.actuatorSignal,EXVECOA.openCommand)
    annotation (Line(points={{-292.1022509015925,58.60670760478638},{-283.4967413338768,58.60670760478638},{-283.4967413338768,54},{-275.9,54}},color={0,0,127}));
  connect(error_ecoEXV_A.sensor,ecoEXV_A_PI.errorSignal)
    annotation (Line(points={{-335.48568335442127,60.04220507589757},{-323.5895830110875,60.04220507589757},{-323.5895830110875,58.60670760478638},{-312.70225090159255,58.60670760478638}},color={28,108,200}));
  connect(calibrationBlock.SSTmaxA,SSTmaxA.value)
    annotation (Line(points={{-38.9269811438359,393.19904247311706},{-50.387727008626705,393.19904247311706},{-50.387727008626705,416.26970493404895},{-61.848472873417506,416.26970493404895}},color={0,0,127}));
  connect(calibrationBlock.SSTmaxB,SSTmaxB.value)
    annotation (Line(points={{-38.9269811438359,389.9000382761393},{-61.01840321403811,389.9000382761393},{-61.01840321403811,405.4264793718784},{-83.10982528424032,405.4264793718784}},color={0,0,127}));
  connect(Fan_A_PI.actuatorSignal,motorA.frequency_in)
    annotation (Line(points={{-297.91829496684375,298.0142088470193},{-291.91829496684375,298.0142088470193},{-291.91829496684375,330.21121186492957},{-255.13684769568636,330.21121186492957},{-255.13684769568636,324.21121186492957}},color={0,0,127}));
  connect(error_Fan_A.sensor,Fan_A_PI.errorSignal)
    annotation (Line(points={{-340.06322466559436,295.3014532680274},{-329.4056270763387,295.3014532680274},{-329.4056270763387,298.0142088470193},{-318.51829496684377,298.0142088470193}},color={28,108,200}));
  connect(error_Fan_A2.sensor,Fan_A_PI2.errorSignal)
    annotation (Line(points={{322.06316659764076,351.7072415864711},{338.0866715944744,351.7072415864711},{338.0866715944744,350.3474017328978},{289.4708300874953,350.3474017328978}},color={28,108,200}));
  connect(motorB.frequency_in,Fan_A_PI2.actuatorSignal)
    annotation (Line(points={{232.0492639255063,341.2609810755825},{232.0492639255063,350.3474017328978},{268.8708300874953,350.3474017328978}},color={0,0,127}));
end XBV_OL_opti;
