within Workspace.Controller.SubSystems.Tests;
model test_CompleteCompressorControlBase_duplex
  extends.Workspace.Controller.SubSystems.BaseClasses.CompleteCompressorControlBase_duplex(
    redeclare replaceable.Workspace.Controller.SubSystems.CompressorControl compressorControl_moduleA_crkA,
    redeclare replaceable.Workspace.Controller.SubSystems.CompressorControl compressorControl_moduleA_crkB,
    redeclare replaceable.Workspace.Controller.SubSystems.CompressorControl compressorControl_moduleB_crkA,
    redeclare replaceable.Workspace.Controller.SubSystems.CompressorControl compressorControl_moduleB_crkB);
end test_CompleteCompressorControlBase_duplex;
