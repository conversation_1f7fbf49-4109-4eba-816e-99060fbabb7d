within KAV_initiation.System30KAV.Controller30KAV.Tests;
model Test_controllerblock
  parameter Modelica.SIunits.Power cap=150000;
  parameter Modelica.SIunits.Temperature T_sst_min_limit_comp=0;
  parameter Modelica.SIunits.Temperature T_sdt_max_limit_comp=67;
  parameter Modelica.SIunits.Temperature T_dgt_max_limit_comp=94;
  parameter Modelica.SIunits.Temperature T_sst_min_limit_exv=0.5;
  parameter Modelica.SIunits.Temperature T_sst_max_limit_exv=17;
  parameter Modelica.SIunits.Temperature T_dsh_min_limit_exv=7;
  parameter Modelica.SIunits.Temperature T_sc_setpoint_exv=-5;
  parameter Modelica.SIunits.Temperature T_dgt_max_limit_exv=93;
  parameter Modelica.SIunits.Temperature T_ecosh_setpoint_eexv=10;
  parameter Real spd_setpoint_fan=0.8;
  parameter Modelica.SIunits.Temperature T_sdt_min_limit_fan=20;
  parameter Modelica.SIunits.Temperature T_sdt_max_limit_fan=65;
  parameter Modelica.SIunits.Temperature T_dgt_max_limit_fan=92;
  Modelica.Blocks.Sources.RealExpression Tsstmin_exv(
    y=T_sst_min_limit_exv)
    annotation (Placement(transformation(extent={{-15,-7},{15,7}},rotation=0,origin={-77,39})));
  Modelica.Blocks.Sources.RealExpression Tdshmin_exv(
    y=T_dsh_min_limit_exv)
    annotation (Placement(transformation(extent={{-15,-7},{15,7}},rotation=0,origin={-77,19})));
  Modelica.Blocks.Sources.RealExpression Tsstmax_exv(
    y=T_sst_max_limit_exv)
    annotation (Placement(transformation(extent={{-15,-7},{15,7}},rotation=0,origin={-77,29})));
  Modelica.Blocks.Sources.RealExpression Tscstpt_exv(
    y=T_sc_setpoint_exv)
    annotation (Placement(transformation(extent={{-15,-6},{15,6}},rotation=0,origin={-77,48})));
  Modelica.Blocks.Sources.RealExpression Tdgtmax_exv(
    y=T_dgt_max_limit_exv)
    annotation (Placement(transformation(extent={{-15,-7},{15,7}},rotation=0,origin={-77,9})));
  y_equalto_kx_plus_b y_equalto_kx_plus_b1
    annotation (Placement(transformation(extent={{39.0,-26.0},{93.0,34.0}},rotation=0.0,origin={0.0,0.0})));
  Controller controller
    annotation (Placement(transformation(extent={{6.0,-6.0},{26.0,14.0}},rotation=0.0,origin={0.0,0.0})));
  Interfaces.MeasurementBus measurementBusA
    annotation (Placement(transformation(extent={{76,36},{92,54}})));
  Interfaces.MeasurementBus measurementBusB
    annotation (Placement(transformation(extent={{78,-42},{94,-24}})));
  Interfaces.LimitsBus limitsBusA
    annotation (Placement(transformation(extent={{-26,18},{-14,30}})));
  Modelica.Blocks.Sources.RealExpression cap_comp(
    y=cap)
    annotation (Placement(transformation(extent={{-92.0,86.0},{-62.0,98.0}},rotation=0.0,origin={0.0,0.0})));
  Modelica.Blocks.Sources.RealExpression Tsstmin_comp(
    y=T_sst_min_limit_comp)
    annotation (Placement(transformation(extent={{-15,-7},{15,7}},rotation=0,origin={-77,83})));
  Modelica.Blocks.Sources.RealExpression Tsdtmax_comp(
    y=T_sdt_max_limit_comp)
    annotation (Placement(transformation(extent={{-15,-7},{15,7}},rotation=0,origin={-77,73})));
  Modelica.Blocks.Sources.RealExpression Tdgtmax_comp(
    y=T_dgt_max_limit_comp)
    annotation (Placement(transformation(extent={{-15,-7},{15,7}},rotation=0,origin={-77,63})));
  Modelica.Blocks.Sources.RealExpression Tecosh_setpoint_eexv(
    y=T_ecosh_setpoint_eexv)
    annotation (Placement(transformation(extent={{-15,-6},{15,6}},rotation=0,origin={-77,-6})));
  Modelica.Blocks.Sources.RealExpression spd_stpt_fan(
    y=spd_setpoint_fan)
    annotation (Placement(transformation(extent={{-15,-6},{15,6}},rotation=0,origin={-77,-22})));
  Modelica.Blocks.Sources.RealExpression Tsdtmax_fan(
    y=T_sdt_max_limit_fan)
    annotation (Placement(transformation(extent={{-15,-7},{15,7}},rotation=0,origin={-77,-41})));
  Modelica.Blocks.Sources.RealExpression Tsdtmin_fan(
    y=T_sdt_min_limit_fan)
    annotation (Placement(transformation(extent={{-15,-7},{15,7}},rotation=0,origin={-77,-31})));
  Modelica.Blocks.Sources.RealExpression Tdgtmax_fan(
    y=T_dgt_max_limit_fan)
    annotation (Placement(transformation(extent={{-15,-7},{15,7}},rotation=0,origin={-77,-51})));
  Interfaces.LimitsBus limitsBusB
    annotation (Placement(transformation(extent={{-26,-38},{-14,-26}})));
equation
  connect(controller.fan_crkA,y_equalto_kx_plus_b1.x[4])
    annotation (Line(points={{26.8,12.8},{40,12.8},{40,3.7},{52.769999999999996,3.7}},color={0,0,127}));
  connect(controller.compressor_crkA,y_equalto_kx_plus_b1.x[1])
    annotation (Line(points={{26.8,4.976000000000001},{40,4.976000000000001},{40,3.7},{52.769999999999996,3.7}},color={0,0,127}));
  connect(controller.exv_crkA,y_equalto_kx_plus_b1.x[2])
    annotation (Line(points={{26.8,10},{40,10},{40,3.7},{52.769999999999996,3.7}},color={0,0,127}));
  connect(controller.ecoExv_crkA,y_equalto_kx_plus_b1.x[3])
    annotation (Line(points={{26.8,7.4},{38,7.4},{38,3.7},{52.769999999999996,3.7}},color={0,0,127}));
  connect(controller.fan_crkB,y_equalto_kx_plus_b1.x[8])
    annotation (Line(points={{26.8,0.5999999999999996},{40,0.5999999999999996},{40,3.7},{52.769999999999996,3.7}},color={0,0,127}));
  connect(controller.compressor_crkB,y_equalto_kx_plus_b1.x[5])
    annotation (Line(points={{26.8,2.7760000000000007},{40,2.7760000000000007},{40,3.7},{52.769999999999996,3.7}},color={0,0,127}));
  connect(controller.exv_crkB,y_equalto_kx_plus_b1.x[6])
    annotation (Line(points={{26.8,-2},{40,-2},{40,3.7},{52.769999999999996,3.7}},color={0,0,127}));
  connect(controller.ecoExv_crkB,y_equalto_kx_plus_b1.x[7])
    annotation (Line(points={{26.8,-4.603999999999999},{40,-4.603999999999999},{40,3.7},{52.769999999999996,3.7}},color={0,0,127}));
  connect(y_equalto_kx_plus_b1.y[8],measurementBusB.T_sst)
    annotation (Line(points={{79.77,3.7},{79.77,-15.15},{86.04,-15.15},{86.04,-32.955}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(y_equalto_kx_plus_b1.y[9],measurementBusB.T_sdt)
    annotation (Line(points={{79.77,3.7},{79.77,-14.15},{86.04,-14.15},{86.04,-32.955}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(y_equalto_kx_plus_b1.y[10],measurementBusB.T_dgt)
    annotation (Line(points={{79.77,3.7},{79.77,-14.15},{86.04,-14.15},{86.04,-32.955}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(y_equalto_kx_plus_b1.y[11],measurementBusB.dT_sbc)
    annotation (Line(points={{79.77,3.7},{79.77,-14.15},{86.04,-14.15},{86.04,-32.955}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(y_equalto_kx_plus_b1.y[12],measurementBusB.dT_dsh)
    annotation (Line(points={{79.77,3.7},{79.77,-14.15},{86.04,-14.15},{86.04,-32.955}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(y_equalto_kx_plus_b1.y[13],measurementBusB.dT_esh)
    annotation (Line(points={{79.77,3.7},{79.77,-14.15},{86.04,-14.15},{86.04,-32.955}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(measurementBusA,controller.measurementBus_crkA)
    annotation (Line(points={{84,45},{0,45},{0,11},{6,11}},color={255,204,51},thickness=0.5),Text(string="%first",index=-1,extent={{-6,3},{-6,3}}));
  connect(measurementBusB,controller.measurementBus_crkB)
    annotation (Line(points={{86,-33},{0,-33},{0,1},{6,1}},color={255,204,51},thickness=0.5),Text(string="%first",index=-1,extent={{-6,3},{-6,3}}));
  connect(Tsstmin_exv.y,limitsBusA.T_sst_min_limit_exv)
    annotation (Line(points={{-60.5,39},{-39.25,39},{-39.25,24.03},{-19.97,24.03}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tsstmax_exv.y,limitsBusA.T_sst_max_limit)
    annotation (Line(points={{-60.5,29},{-40.25,29},{-40.25,24.03},{-19.97,24.03}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tdshmin_exv.y,limitsBusA.dT_dsh_min_limt)
    annotation (Line(points={{-60.5,19},{-40.25,19},{-40.25,24.03},{-19.97,24.03}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tdgtmax_exv.y,limitsBusA.T_dgt_max_limit_exv)
    annotation (Line(points={{-60.5,9},{-39.25,9},{-39.25,24.03},{-19.97,24.03}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tecosh_setpoint_eexv.y,limitsBusA.dT_esh_setpoint)
    annotation (Line(points={{-60.5,-6},{-42,-6},{-42,24.03},{-19.97,24.03}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tsdtmin_fan.y,limitsBusA.T_sdt_min_limit)
    annotation (Line(points={{-60.5,-31},{-60.5,-3.5},{-19.97,-3.5},{-19.97,24.03}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tdgtmax_fan.y,limitsBusA.T_dgt_max_limit_fan)
    annotation (Line(points={{-60.5,-51},{-60.5,-8.5},{-19.97,-8.5},{-19.97,24.03}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tsdtmax_fan.y,limitsBusA.T_sdt_max_limit_fan)
    annotation (Line(points={{-60.5,-41},{-60.5,-13.5},{-19.97,-13.5},{-19.97,24.03}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tdgtmax_comp.y,limitsBusB.T_dgt_max_limit_comp)
    annotation (Line(points={{-60.5,63},{-60.5,15.5},{-19.97,15.5},{-19.97,-31.97}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tsstmin_exv.y,limitsBusB.T_sst_min_limit_exv)
    annotation (Line(points={{-60.5,39},{-60.5,3.5},{-19.97,3.5},{-19.97,-31.97}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tsstmax_exv.y,limitsBusB.T_sst_max_limit)
    annotation (Line(points={{-60.5,29},{-60.5,-1.5},{-19.97,-1.5},{-19.97,-31.97}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tdshmin_exv.y,limitsBusB.dT_dsh_min_limt)
    annotation (Line(points={{-60.5,19},{-60.5,-6.5},{-19.97,-6.5},{-19.97,-31.97}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tdgtmax_exv.y,limitsBusB.T_dgt_max_limit_exv)
    annotation (Line(points={{-60.5,9},{-60.5,-11.5},{-19.97,-11.5},{-19.97,-31.97}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tecosh_setpoint_eexv.y,limitsBusB.dT_esh_setpoint)
    annotation (Line(points={{-60.5,-6},{-40,-6},{-40,-31.97},{-19.97,-31.97}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tsdtmin_fan.y,limitsBusB.T_sdt_min_limit)
    annotation (Line(points={{-60.5,-31},{-40.25,-31},{-40.25,-31.97},{-19.97,-31.97}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tsdtmax_fan.y,limitsBusB.T_sdt_max_limit_fan)
    annotation (Line(points={{-60.5,-41},{-40.25,-41},{-40.25,-31.97},{-19.97,-31.97}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(Tdgtmax_fan.y,limitsBusB.T_dgt_max_limit_fan)
    annotation (Line(points={{-60.5,-51},{-40.25,-51},{-40.25,-31.97},{-19.97,-31.97}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(limitsBusA,controller.limitsBus_crkA)
    annotation (Line(points={{-20,24},{-8,24},{-8,7},{6,7}},color={255,204,51},thickness=0.5),Text(string="%first",index=-1,extent={{-6,3},{-6,3}}));
  connect(limitsBusB,controller.limitsBus_crkB)
    annotation (Line(points={{-20,-32},{-8,-32},{-8,-3.1000000000000005},{6,-3.1000000000000005}},color={255,204,51},thickness=0.5),Text(string="%first",index=-1,extent={{-6,3},{-6,3}}));
  connect(spd_stpt_fan.y,limitsBusA.fanSpeed_setpoint)
    annotation (Line(points={{-60.5,-22},{-40,-22},{-40,24.03},{-19.97,24.03}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(spd_stpt_fan.y,limitsBusB.fanSpeed_setpoint)
    annotation (Line(points={{-60.5,-22},{-42,-22},{-42,-31.97},{-19.97,-31.97}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(y_equalto_kx_plus_b1.y[7],measurementBusA.dT_esh)
    annotation (Line(points={{79.77,3.7},{79.77,24.85},{84.04,24.85},{84.04,45.045}},color={0,0,127}));
  connect(y_equalto_kx_plus_b1.y[6],measurementBusA.dT_dsh)
    annotation (Line(points={{79.77,3.7},{79.77,24.85},{84.04,24.85},{84.04,45.045}},color={0,0,127}));
  connect(y_equalto_kx_plus_b1.y[5],measurementBusA.dT_sbc)
    annotation (Line(points={{79.77,3.7},{79.77,24.85},{84.04,24.85},{84.04,45.045}},color={0,0,127}));
  connect(y_equalto_kx_plus_b1.y[4],measurementBusA.T_dgt)
    annotation (Line(points={{79.77,3.7},{79.77,24.85},{84.04,24.85},{84.04,45.045}},color={0,0,127}));
  connect(y_equalto_kx_plus_b1.y[3],measurementBusA.T_sdt)
    annotation (Line(points={{79.77,3.7},{79.77,23.85},{84.04,23.85},{84.04,45.045}},color={0,0,127}));
  connect(y_equalto_kx_plus_b1.y[2],measurementBusA.T_sst)
    annotation (Line(points={{79.77,3.7},{79.77,23.85},{84.04,23.85},{84.04,45.045}},color={0,0,127}));
  connect(y_equalto_kx_plus_b1.y[1],measurementBusA.capacity)
    annotation (Line(points={{79.77,3.7},{79.77,23.85},{84.04,23.85},{84.04,45.045}},color={0,0,127}));
  connect(Tsdtmax_comp.y,limitsBusB.T_sdt_max_limit_comp)
    annotation (Line(points={{-60.5,73},{-60.5,20.5},{-19.97,20.5},{-19.97,-31.97}},color={0,0,127}));
  connect(Tsdtmax_comp.y,limitsBusA.T_sdt_max_limit_comp)
    annotation (Line(points={{-60.5,73},{-60.5,48.5},{-19.97,48.5},{-19.97,24.03}},color={0,0,127}));
  connect(Tdgtmax_comp.y,limitsBusA.T_dgt_max_limit_comp)
    annotation (Line(points={{-60.5,63},{-40.25,63},{-40.25,24.03},{-19.97,24.03}},color={0,0,127}));
  connect(cap_comp.y,limitsBusA.capacity_setpoint)
    annotation (Line(points={{-60.5,92},{-40,92},{-40,24.03},{-19.97,24.03}},color={0,0,127}));
  connect(Tsstmin_comp.y,limitsBusB.T_sst_min_limit_comp)
    annotation (Line(points={{-60.5,83},{-60.5,25.5},{-19.97,25.5},{-19.97,-31.97}},color={0,0,127}));
  connect(Tsstmin_comp.y,limitsBusA.T_sst_min_limit_comp)
    annotation (Line(points={{-60.5,83},{-60.5,53.5},{-19.97,53.5},{-19.97,24.03}},color={0,0,127}));
  connect(Tscstpt_exv.y,limitsBusA.dT_sbc_setpoint)
    annotation (Line(points={{-60.5,48},{-40,48},{-40,24.03},{-19.97,24.03}},color={0,0,127}));
  connect(Tscstpt_exv.y,limitsBusB.dT_sbc_setpoint)
    annotation (Line(points={{-60.5,48},{-40,48},{-40,-31.97},{-19.97,-31.97}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false)),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)));
end Test_controllerblock;
