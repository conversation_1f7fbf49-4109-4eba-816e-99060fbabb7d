within KAV_initiation.System30KAV.Controller30KAV;
model Controller
  extends.Workspace.Controller.SubSystems.BaseClasses.ControllerBase(
    redeclare.Workspace.Controller.SubSystems.FanControl fanControl_crkA(
      isOffSDTmin=isOffSDTmin_fan,
      isOffSDTmax=isOffSDTmax_fan,
      isOffDGTmax=isOffDGTmax_fan,
      setpointController(
        manualOff=manualOff_fan_crkA,
        AV_value_off=frq_fan_sp_manual_crkA),
      MaxFrequency=MaxFrequency),
    redeclare.Workspace.Controller.SubSystems.EXVControl eXVControl_crkA(
      isOffSSTmin=isOffSSTmin_EXV,
      isOffSSTmax=isOffSSTmax_EXV,
      isOffDSHmin=isOffDSHmin_EXV,
      isOffDGTmax=isOffDGTmax_EXV,
      comp_max_speed=crkA_max_speed,
      MaxFanFrequency=MaxFrequency),
    redeclare.Workspace.Controller.SubSystems.CompleteCompressorControl completeCompressorControl_base(
      compressorControl_crkA(
        isOffSSTmin=isOffSSTmin_comp,
        isOffSDTmax=isOffSDTmax_comp,
        isOffDGTmax=isOffDGTmax_comp,
        setpointController(
          manualOff=manualOff_compressor_crkA,
          AV_value_off=frq_comp_sp_manual_crkA)),
      compressorControl_crkB(
        isOffSSTmin=isOffSSTmin_comp,
        isOffSDTmax=isOffSDTmax_comp,
        isOffDGTmax=isOffDGTmax_comp,
        setpointController(
          manualOff=manualOff_compressor_crkB,
          AV_value_off=frq_comp_sp_manual_crkB)),gain_capacity = gain_capacity),
    redeclare.Workspace.Controller.SubSystems.FanControl fanControl_crkB(
      isOffSDTmin=isOffSDTmin_fan,
      isOffSDTmax=isOffSDTmax_fan,
      isOffDGTmax=isOffDGTmax_fan,
      setpointController(
        manualOff=manualOff_fan_crkB,
        AV_value_off=frq_fan_sp_manual_crkB),
      MaxFrequency=MaxFrequency),
    redeclare.Workspace.Controller.SubSystems.EXVControl eXVControl_crkB(
      isOffSSTmin=isOffSSTmin_EXV,
      isOffSSTmax=isOffSSTmax_EXV,
      isOffDSHmin=isOffDSHmin_EXV,
      isOffDGTmax=isOffDGTmax_EXV,
      comp_max_speed=crkB_max_speed,
      MaxFanFrequency=MaxFrequency),
    redeclare.Workspace.Controller.SubSystems.EcoEXVControl ecoEXVControl_crkA(
      setpointController(
        AV_min=0.05)),
    redeclare.Workspace.Controller.SubSystems.EcoEXVControl ecoEXVControl_crkB(
      setpointController(
        AV_min=0.05)),
    crkA_isOff=false);
  parameter Real MaxFrequency;
  parameter Real gain_capacity;
  parameter Boolean isPumpUserPresent=false
    annotation (Dialog(tab="General",group="User Pump"));
  parameter Boolean isEWTControl=false
    "True when EWT is controlled"
    annotation (Dialog(tab="General",group="User Pump"));
  parameter Real min_speed_pumpUser=20.0
    annotation (Dialog(tab="General",group="User Pump"));
  parameter Real max_speed_pumpUser=2900.0
    annotation (Dialog(tab="General",group="User Pump"));
  //     parameter Real AV_min_setpoint;
  parameter Boolean isOffSDTmin_fan
    annotation (Dialog(group="fanControl ",tab="isOff controller"));
  parameter Boolean isOffSDTmax_fan
    annotation (Dialog(group="fanControl ",tab="isOff controller"));
  parameter Boolean isOffDGTmax_fan
    annotation (Dialog(group="fanControl ",tab="isOff controller"));
  parameter Boolean isOffSSTmin_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffSSTmax_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffDSHmin_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffDGTmax_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffSSTmin_comp
    annotation (Dialog(group="Compressor ",tab="isOff controller"));
  parameter Boolean isOffSDTmax_comp
    annotation (Dialog(group="Compressor ",tab="isOff controller"));
  parameter Boolean isOffDGTmax_comp
    annotation (Dialog(group="Compressor ",tab="isOff controller"));
  parameter Boolean manualOff_fan_crkA=false
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Real frq_fan_sp_manual_crkA=1
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Boolean manualOff_fan_crkB=false
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Real frq_fan_sp_manual_crkB=1
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Boolean manualOff_compressor_crkA=false
    annotation (Dialog(group="Compressor ",tab="manualOff"));
  parameter Real frq_comp_sp_manual_crkA=95
    annotation (Dialog(group="Compressor ",tab="manualOff"));
  parameter Boolean manualOff_compressor_crkB=false
    annotation (Dialog(group="Compressor ",tab="manualOff"));
  parameter Real frq_comp_sp_manual_crkB=95
    annotation (Dialog(group="Compressor ",tab="manualOff"));
  .Workspace.Controller.SubSystems.PumpUserControl pumpUserControl(
    gain_extPressure=0.0001,
    isEWTControl=isEWTControl,
    UserPumpPresent=isPumpUserPresent,
    Ka_min=0)
    annotation (Placement(transformation(extent={{-11.011197947271775,-140.22990785020986},{8.988802052728225,-120.22990785020986}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput KaInput_crk
    annotation (Placement(transformation(extent={{100.0,-126.0},{120.0,-106.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput pumpUser_crk
    annotation (Placement(transformation(extent={{100,-148},{120,-128}},origin={0,0},rotation=0)));
equation
  connect(pumpUserControl.measurementBus,measurementBus_crkA)
    annotation (Line(points={{-13.611197947271776,-119.82990785020985},{-55.391691279468034,-119.82990785020985},{-55.391691279468034,70},{-100,70}},color={255,204,51}));
  connect(pumpUserControl.limitsBus,limitsBus_crkA)
    annotation (Line(points={{-13.211197947271776,-137.22990785020986},{-55.00269478400226,-137.22990785020986},{-55.00269478400226,30},{-100,30}},color={255,204,51}));
  connect(pumpUserControl.ActuatorSignal_Ka,KaInput_crk)
    annotation (Line(points={{17.788802052728226,-125.42990785020986},{62.4,-125.42990785020986},{62.4,-116},{110,-116}},color={0,0,127}));
  connect(pumpUserControl.ActuatorSignal,pumpUser_crk)
    annotation (Line(points={{17.788802052728226,-129.22990785020986},{62.4,-129.22990785020986},{62.4,-138},{110,-138}},color={0,0,127}));
end Controller;
