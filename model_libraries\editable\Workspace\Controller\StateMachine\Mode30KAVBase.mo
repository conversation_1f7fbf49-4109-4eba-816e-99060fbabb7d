within KAV_initiation.System30KAV.Controller30KAV.StateMachine;
record Mode30KAVBase
  extends BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeBase(
    redeclare type ModeID=ModeID30KAV,
    allowStart=true);
  parameter Boolean CrkA=false;
  parameter Boolean CrkB=false;
  parameter Boolean EcoA=false;
  parameter Boolean EcoB=false;
  parameter Boolean ViA=false;
  parameter Boolean ViB=false;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false)),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)));
end Mode30KAVBase;
