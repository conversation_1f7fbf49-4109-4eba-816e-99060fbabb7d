within KAV_initiation.System30KAV.Controller30KAV.StateMachine;
partial model StateMachine30KAV
  inner BOLT.Control.SteadyState.Utilities.CalculateExtraOutputs calculateExtraOutputs;
  Boolean transitTo2,transitTo3,transitTo4,transitTo5,transitTo6,transitTo7,transitTo8,transitTo9,transitTo10,transitTo11,transitTo12,transitTo13,transitTo14,transitTo15,transitTo16,transitTo17,transitTo18;
  CrkAB_1 mode_CrkAB_1(
    transitionCondition=false);
  CrkABEcoABVIAB_2 mode_CrkABEcoABVIAB_2(
    transitionCondition=transitTo2);
  CrkABEcoABVIA_3 mode_CrkABEcoABVIA_3(
    transitionCondition=transitTo3);
  CrkABEcoABVIB_4 mode_CrkABEcoABVIB_4(
    transitionCondition=transitTo4);
  CrkABEcoAB_5 mode_CrkABEcoAB_5(
    transitionCondition=transitTo5);
  CrkABEcoAVIA_6 mode_CrkABEcoAVIA_6(
    transitionCondition=transitTo6);
  CrkABEcoA_7 mode_CrkABEcoA_7(
    transitionCondition=transitTo7);
  CrkABEcoBVIB_8 mode_CrkABEcoBVIB_8(
    transitionCondition=transitTo8);
  CrkABEcoB_9 mode_CrkABEcoB_9(
    transitionCondition=transitTo9);
  CrkA_10 mode_CrkA_10(
    transitionCondition=transitTo10);
  CrkAEcoA_11 mode_CrkAEcoA_11(
    transitionCondition=transitTo11);
  CrkAEcoAVIA_12 mode_CrkAEcoAVIA_12(
    transitionCondition=transitTo12);
  CrkAVIA_13 mode_CrkAVIA_13(
    transitionCondition=transitTo13);
  CrkABVIAB_14 mode_CrkABVIAB_14(
    transitionCondition=transitTo14);
  CrkABVIA_15 mode_CrkABVIA_15(
    transitionCondition=transitTo15);
  CrkABVIB_16 mode_CrkABVIB_16(
    transitionCondition=transitTo16);
  CrkABEcoAVIAB_17 mode_CrkABEcoAVIAB_17(
    transitionCondition=transitTo17);
  CrkABEcoBVIAB_18 mode_CrkABEcoBVIAB_18(
    transitionCondition=transitTo18);
  Boolean actuators_OnOff[6];
  Boolean CAP_LOWsat;
  Boolean ECO_ON_A;
  Boolean ECO_ON_B;
  Boolean VI_ON_A;
  Boolean VI_ON_B;
  parameter Boolean UseStateMachine
    "If true then StateMachine works normally if false, StateMachine stop and initiale current mode ID is never change"
    annotation (Dialog(group="Use Parameters"));
  BOLT.Control.SteadyState.StateMachine.StateMachine StateMachine(
    redeclare type ModeID=ModeID30KAV,
    redeclare record Mode=Mode30KAVBase,
    currentModeID=ModeID30KAV.CrkAB_1,
    modes={mode_CrkAB_1,mode_CrkABVIAB_14,mode_CrkABVIA_15,mode_CrkABVIB_16,mode_CrkABEcoAVIAB_17,mode_CrkABEcoBVIAB_18,mode_CrkABEcoABVIAB_2,mode_CrkABEcoABVIA_3,mode_CrkABEcoABVIB_4,mode_CrkABEcoAB_5,mode_CrkABEcoAVIA_6,mode_CrkABEcoA_7,mode_CrkABEcoBVIB_8,mode_CrkABEcoB_9,mode_CrkA_10,mode_CrkAVIA_13,mode_CrkAEcoAVIA_12,mode_CrkAEcoA_11})
    annotation (Placement(transformation(extent={{-109.94834955889496,74.05165044110504},{-90.05165044110504,93.94834955889496}},rotation=0.0,origin={0.0,0.0})));
equation
  when calculateExtraOutputs then
    transitTo2=
      if UseStateMachine then
        (ECO_ON_A and ECO_ON_B) and(VI_ON_A and VI_ON_B) and not CAP_LOWsat
      else
        false;
    transitTo3=
      if UseStateMachine then
        (ECO_ON_A and ECO_ON_B) and(VI_ON_A and not VI_ON_B) and not CAP_LOWsat
      else
        false;
    transitTo4=
      if UseStateMachine then
        (ECO_ON_A and ECO_ON_B) and(not VI_ON_A and VI_ON_B) and not CAP_LOWsat
      else
        false;
    transitTo5=
      if UseStateMachine then
        (ECO_ON_A and ECO_ON_B) and(not VI_ON_A and not VI_ON_B) and not CAP_LOWsat
      else
        false;
    transitTo6=
      if UseStateMachine then
        (ECO_ON_A and not ECO_ON_B) and VI_ON_A and not CAP_LOWsat
      else
        false;
    transitTo7=
      if UseStateMachine then
        (ECO_ON_A and not ECO_ON_B) and not VI_ON_A and not CAP_LOWsat
      else
        false;
    transitTo8=
      if UseStateMachine then
        (not ECO_ON_A and ECO_ON_B) and VI_ON_B and not CAP_LOWsat
      else
        false;
    transitTo9=
      if UseStateMachine then
        (not ECO_ON_A and ECO_ON_B) and not VI_ON_B and not CAP_LOWsat
      else
        false;
    transitTo10=
      if UseStateMachine then
        not ECO_ON_A and not VI_ON_A and CAP_LOWsat
      else
        false;
    transitTo11=(StateMachine.currentModeID == ModeID30KAV.CrkA_10) and(
      if UseStateMachine then
        ECO_ON_A and not VI_ON_A
      else
        false);
    transitTo12=(StateMachine.currentModeID == ModeID30KAV.CrkA_10) and(
      if UseStateMachine then
        ECO_ON_A and VI_ON_A
      else
        false);
    transitTo13=(StateMachine.currentModeID == ModeID30KAV.CrkA_10) and(
      if UseStateMachine then
        not ECO_ON_A and VI_ON_A
      else
        false);
    transitTo14=
      if UseStateMachine then
        not ECO_ON_A and not ECO_ON_B and VI_ON_A and VI_ON_B and not CAP_LOWsat
      else
        false;
    transitTo15=
      if UseStateMachine then
        not ECO_ON_A and not ECO_ON_B and VI_ON_A and not VI_ON_B and not CAP_LOWsat
      else
        false;
    transitTo16=
      if UseStateMachine then
        not ECO_ON_A and not ECO_ON_B and not VI_ON_A and VI_ON_B and not CAP_LOWsat
      else
        false;
    transitTo17=
      if UseStateMachine then
        ECO_ON_A and not ECO_ON_B and VI_ON_A and VI_ON_B and not CAP_LOWsat
      else
        false;
    transitTo18=
      if UseStateMachine then
        not ECO_ON_A and ECO_ON_B and VI_ON_A and VI_ON_B and not CAP_LOWsat
      else
        false;
  end when;
  actuators_OnOff={
    if StateMachine.nextModeID == ModeID30KAV.CrkAB_1 then StateMachine.modes[1].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIAB_14 then StateMachine.modes[2].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIA_15 then StateMachine.modes[3].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIB_16 then StateMachine.modes[4].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAVIAB_17 then StateMachine.modes[5].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoBVIAB_18 then StateMachine.modes[6].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIAB_2 then StateMachine.modes[7].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIA_3 then StateMachine.modes[8].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIB_4 then StateMachine.modes[9].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAB_5 then StateMachine.modes[10].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAVIA_6 then StateMachine.modes[11].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoA_7 then StateMachine.modes[12].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoBVIB_8 then StateMachine.modes[13].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoB_9 then StateMachine.modes[14].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkA_10 then StateMachine.modes[15].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkAVIA_13 then StateMachine.modes[16].CrkA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkAEcoAVIA_12 then StateMachine.modes[17].CrkA
    else StateMachine.modes[18].CrkA,
    if StateMachine.nextModeID == ModeID30KAV.CrkAB_1 then StateMachine.modes[1].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIAB_14 then StateMachine.modes[2].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIA_15 then StateMachine.modes[3].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIB_16 then StateMachine.modes[4].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAVIAB_17 then StateMachine.modes[5].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoBVIAB_18 then StateMachine.modes[6].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIAB_2 then StateMachine.modes[7].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIA_3 then StateMachine.modes[8].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIB_4 then StateMachine.modes[9].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAB_5 then StateMachine.modes[10].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAVIA_6 then StateMachine.modes[11].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoA_7 then StateMachine.modes[12].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoBVIB_8 then StateMachine.modes[13].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoB_9 then StateMachine.modes[14].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkA_10 then StateMachine.modes[15].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkAVIA_13 then StateMachine.modes[16].CrkB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkAEcoAVIA_12 then StateMachine.modes[17].CrkB
    else StateMachine.modes[18].CrkB,
    if StateMachine.nextModeID == ModeID30KAV.CrkAB_1 then StateMachine.modes[1].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIAB_14 then StateMachine.modes[2].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIA_15 then StateMachine.modes[3].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIB_16 then StateMachine.modes[4].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAVIAB_17 then StateMachine.modes[5].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoBVIAB_18 then StateMachine.modes[6].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIAB_2 then StateMachine.modes[7].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIA_3 then StateMachine.modes[8].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIB_4 then StateMachine.modes[9].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAB_5 then StateMachine.modes[10].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAVIA_6 then StateMachine.modes[11].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoA_7 then StateMachine.modes[12].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoBVIB_8 then StateMachine.modes[13].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoB_9 then StateMachine.modes[14].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkA_10 then StateMachine.modes[15].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkAVIA_13 then StateMachine.modes[16].EcoA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkAEcoAVIA_12 then StateMachine.modes[17].EcoA
    else StateMachine.modes[18].EcoA,
    if StateMachine.nextModeID == ModeID30KAV.CrkAB_1 then StateMachine.modes[1].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIAB_14 then StateMachine.modes[2].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIA_15 then StateMachine.modes[3].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIB_16 then StateMachine.modes[4].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAVIAB_17 then StateMachine.modes[5].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoBVIAB_18 then StateMachine.modes[6].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIAB_2 then StateMachine.modes[7].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIA_3 then StateMachine.modes[8].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIB_4 then StateMachine.modes[9].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAB_5 then StateMachine.modes[10].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAVIA_6 then StateMachine.modes[11].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoA_7 then StateMachine.modes[12].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoBVIB_8 then StateMachine.modes[13].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoB_9 then StateMachine.modes[14].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkA_10 then StateMachine.modes[15].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkAVIA_13 then StateMachine.modes[16].EcoB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkAEcoAVIA_12 then StateMachine.modes[17].EcoB
    else StateMachine.modes[18].EcoB,
    if StateMachine.nextModeID == ModeID30KAV.CrkAB_1 then StateMachine.modes[1].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIAB_14 then StateMachine.modes[2].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIA_15 then StateMachine.modes[3].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIB_16 then StateMachine.modes[4].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAVIAB_17 then StateMachine.modes[5].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoBVIAB_18 then StateMachine.modes[6].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIAB_2 then StateMachine.modes[7].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIA_3 then StateMachine.modes[8].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIB_4 then StateMachine.modes[9].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAB_5 then StateMachine.modes[10].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAVIA_6 then StateMachine.modes[11].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoA_7 then StateMachine.modes[12].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoBVIB_8 then StateMachine.modes[13].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoB_9 then StateMachine.modes[14].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkA_10 then StateMachine.modes[15].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkAVIA_13 then StateMachine.modes[16].ViA
    elseif StateMachine.nextModeID == ModeID30KAV.CrkAEcoAVIA_12 then StateMachine.modes[17].ViA
    else StateMachine.modes[18].ViA,
    if StateMachine.nextModeID == ModeID30KAV.CrkAB_1 then StateMachine.modes[1].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIAB_14 then StateMachine.modes[2].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIA_15 then StateMachine.modes[3].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABVIB_16 then StateMachine.modes[4].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAVIAB_17 then StateMachine.modes[5].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoBVIAB_18 then StateMachine.modes[6].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIAB_2 then StateMachine.modes[7].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIA_3 then StateMachine.modes[8].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoABVIB_4 then StateMachine.modes[9].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAB_5 then StateMachine.modes[10].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoAVIA_6 then StateMachine.modes[11].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoA_7 then StateMachine.modes[12].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoBVIB_8 then StateMachine.modes[13].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkABEcoB_9 then StateMachine.modes[14].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkA_10 then StateMachine.modes[15].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkAVIA_13 then StateMachine.modes[16].ViB
    elseif StateMachine.nextModeID == ModeID30KAV.CrkAEcoAVIA_12 then StateMachine.modes[17].ViB
    else StateMachine.modes[18].ViB};
  annotation (
    Placement(
      transformation(
        extent={{-78,8},{-58,28}})),
    Placement(
      transformation(
        extent={{-68,12},{-48,32}})));
end StateMachine30KAV;
