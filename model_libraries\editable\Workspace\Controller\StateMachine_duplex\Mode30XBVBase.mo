within StateMachine;
record Mode30XBVBase
  extends BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeBase(
    redeclare type ModeID=ModeID30XBV);
  parameter Boolean CrkA;
  parameter Boolean CrkB;
  parameter Boolean CrkC;
  parameter Boolean CrkD;
  parameter Boolean EcoA;
  parameter Boolean EcoB;
  parameter Boolean EcoC;
  parameter Boolean EcoD;
  parameter Boolean ViA;
  parameter Boolean ViB;
  parameter Boolean ViC;
  parameter Boolean ViD;
end Mode30XBVBase;
