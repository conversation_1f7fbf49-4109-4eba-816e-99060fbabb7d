within KAV_initiation.System30KAV.Auxiliary30KAV.ECAT30KAV;
model ECAT30XBVBase
  extends BOLT.ECATBlock.ChillerBase;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={37,31,23},
          fillColor={132,132,2},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          textString="ECAT",
          origin={-2,-2},
          extent={{-104,44},{104,-44}})}));
end ECAT30XBVBase;
