within Workspace.Auxiliary.OptionBlock.RecordBase;
record UnitBase_2C
  extends.Modelica.Icons.Record;
  parameter Bo<PERSON><PERSON> longEvap
    "Evaporator length for Z_U_ev application";
  parameter.Modelica.SIunits.HeatFlowRate Capacity_design
    "Design cooling capacity";
  parameter.Workspace.Controller.Components.Types.CompressorSelector CompType_CKA
    "Compressor Type";
  parameter.Workspace.Controller.Components.Types.CompressorSelector CompType_CKB
    "Compressor Type";
  parameter.Public_database.Compressor.PD_3Port.ScrewMap.Polynomial10.VI.Selector selector_Comp_CKA
    "None";
  parameter.Public_database.Compressor.PD_3Port.ScrewMap.Polynomial10.VI.Selector selector_Comp_CKB
    "None";
  parameter.Public_database.Compressor.PD_3Port.RefIndMap.Polynomial4.VI.Selector_VS_VI selector_Comp_CKA_refInd
    "None";
  parameter.Public_database.Compressor.PD_3Port.RefIndMap.Polynomial4.VI.Selector_VS_VI selector_Comp_CKB_refInd
    "None";
  parameter.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector selector_VFDA_CKA
    "Drive compressor type";
  parameter.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector selector_VFDA_CKB
    "Drive compressor type";
  parameter.BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selector_pump_type
    "Pump type";
  parameter Real CompVoltage_CKA
    "None";
  parameter Real CompVoltage_CKB
    "None";
  parameter Real NcompMax_CKA
    "max frequency ";
  parameter Real NcompMax_CKB
    "max frequency ";
  parameter Real NcompMax_coating_CKA
    "max frequency for opt263";
  parameter Real NcompMax_coating_CKB
    "max frequency for opt263";

  parameter Real NcompMax_MEPS_ME_CKA
    "max frequency for MEPS Saudi and UAE";
  parameter Real NcompMax_MEPS_ME_CKB
    "max frequency for MEPS Saudi and UAE";

  parameter Real NcompMax_MEPS_ME_coating_CKA
    "max frequency for MEPS Saudi and UAE + opt 263";
  parameter Real NcompMax_MEPS_ME_coating_CKB
    "max frequency for MEPS Saudi and UAE + opt 263";


  parameter Real NcompMin_CKA
    "min frequency ";
  parameter Real NcompMin_CKB
    "min frequency ";
  parameter.BlackBoxLibrary.GeoSelector.GeoSelector_RefRef Eco_Geo_CKA
    "Geometry of eco";
  parameter.BlackBoxLibrary.GeoSelector.GeoSelector_RefRef Eco_Geo_CKB
    "Geometry of eco";
  parameter.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector EXV_main_A
    "main EXV flow data of circuit A";
  parameter.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector EXV_eco_A
    "eco EXV flow data of circuit A";
  parameter.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector EXV_main_B
    "main EXV flow data of circuit B";
  parameter.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector EXV_eco_B
    "eco EXV flow data of circuit B";
  parameter Integer eco_nPlate_CKA
    "Plate number of eco";
  parameter Integer eco_nPlate_CKB
    "Plate number of eco";
  parameter Integer evap_n_passes
    "pass number of evaporator";
  parameter.Public_database.Evaporator.Tube.Selector evap_selector_tube
    "evaporator tube";
  parameter Integer evap_n_tubes_pass1
    "tube number of pass 1 of evaporator ";
  parameter Integer evap_n_tubes_pass2
    "tube number of pass 2 of evaporator ";
  parameter.Modelica.SIunits.Length evap_diameter_in_shell
    "shell internal diameter of evaporator (m)";
  parameter.Modelica.SIunits.Length evap_diameter_nozzle
    "outlet water connections (m)";
  parameter.Modelica.SIunits.Length evap_length_tube_CKA
    "tube length of evaporator";
  parameter.Modelica.SIunits.Length evap_length_tube_CKB
    "tube length of evaporator";
  parameter Integer nCoil_CKA
    "None";
  parameter Integer nCoil_CKB
    "None";
  parameter.Modelica.SIunits.Length OilSepDiameter_CKA
    "Oil sep diameter (m)";
  parameter.Modelica.SIunits.Length OilSepDiameter_CKB
    "Oil sep diameter (m)";
  parameter.Modelica.SIunits.Length OilSepLength_CKA
    "Oil sep length (m)";
  parameter.Modelica.SIunits.Length OilSepLength_CKB
    "Oil sep length (m)";
  parameter.Modelica.SIunits.Pressure Suc_lineA_DP
    "pressure drop at eurovent Pa";
  parameter Real Suc_lineA_MF
    "mass flow reference pressure drop kg/s";
  parameter.Modelica.SIunits.Pressure Oil_sepA_DP
    "pressure drop at eurovent Pa";
  parameter Real Oil_sepA_MF
    "mass flow reference pressure drop kg/s";
  parameter.Modelica.SIunits.Pressure Dis_lineA_DP
    "pressure drop at eurovent Pa";
  parameter Real Dis_lineA_MF
    "mass flow reference pressure drop kg/s";
  parameter.Modelica.SIunits.Pressure Suc_lineB_DP
    "pressure drop at eurovent Pa";
  parameter Real Suc_lineB_MF
    "mass flow reference pressure drop kg/s";
  parameter.Modelica.SIunits.Pressure Oil_sepB_DP
    "pressure drop at eurovent Pa";
  parameter Real Oil_sepB_MF
    "mass flow reference pressure drop kg/s";
  parameter.Modelica.SIunits.Pressure Dis_lineB_DP
    "pressure drop at eurovent Pa";
  parameter Real Dis_lineB_MF
    "mass flow reference pressure drop kg/s";
  parameter.Modelica.SIunits.Length eco_DportL_b_A
    "outlet diameter of low pressure side of eco of circuit A";
  parameter.Modelica.SIunits.Length eco_DportH_a_A
    "inlet diameter of high pressure side of eco of circuit A";
  parameter.Modelica.SIunits.Length eco_DportL_a_A
    "inlet diameter of low pressure side of eco of circuit A";
  parameter.Modelica.SIunits.Length eco_DportH_b_A
    "outlet diameter of high pressure side of eco of circuit A";
  parameter.Modelica.SIunits.Length eco_DportL_b_B
    "outlet diameter of low pressure side of eco of circuit B";
  parameter.Modelica.SIunits.Length eco_DportH_a_B
    "inlet diameter of high pressure side of eco of circuit B";
  parameter.Modelica.SIunits.Length eco_DportL_a_B
    "inlet diameter of low pressure side of eco of circuit B";
  parameter.Modelica.SIunits.Length eco_DportH_b_B
    "outlet diameter of high pressure side of eco of circuit B";
end UnitBase_2C;
