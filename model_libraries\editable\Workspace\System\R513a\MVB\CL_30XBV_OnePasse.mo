within Workspace.System.R513a.MVB;
model CL_30XBV_OnePasse
  extends.Workspace.System.BaseCycle.XBV_2C.MVB.System_30XBV(
    ModuleA(
      shaftPumpUser(
        streamID=1),
      c_load2_bf_pow_opt17A=0.16,
      c_load_bf_pow_opt17A=-0.16,
      bf_pow_min_opt17A=0.96,BPHEECOA(RefMedium_H = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A,RefMedium_L = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeEXVmaininA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),split(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),EXVMainA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),EXVECOA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeBPHEECOinA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeevapinA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeEXVECOoutA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),liquidlineA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),evaporator(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeBPHEECOoutA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodecondAiroutA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeevapoutA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeevapoutB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeevapinB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),ECOlineA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),condAirA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),valve_92A(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),valve_92B(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),EXVMainB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeCpECOA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodecondAirinA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),suctionlineA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),suctionlineB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeEXVmaininB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),CompressorA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),dischargelineA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeCpinA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeCpinB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),
    BPHEECOB(RefMedium_H = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A,
             RefMedium_L = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),
    nodeCpoutA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeOilsepoutA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),CompressorB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),split2(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),oilseparatorA(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeCpoutB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeCpECOB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),EXVECOB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeBPHEECOinB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),oilseparatorB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),ECOlineB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeEXVECOoutB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),liquidlineB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeOilsepoutB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodeBPHEECOoutB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodecondAiroutB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),dischargelineB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),condAirB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A),nodecondAirinB(RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R513A)),
    ECAT(
      AmbientAirDBTemp_K(
        setPoint=273.15 + 40),
      EvapBrineLWT_K(
        setPoint=7+273.15),
      EvapBrineEWT_K(
        setPoint=12+273.15),
      ExternalSystemPressureDrop_Pa(
        setPoint=15000),RefrigerantType_nd = BOLT.InternalLibrary.ECATBlock.Types.RefrigerantTypes.R513A),choiceBlock(Selector_ModuleA = Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Selector.Unit_30XBV_1300,HighAmbiant_selector = Workspace.Auxiliary.OptionBlock.HighAmbiantTemperature.HighAmbiant_selector.HighAmbiant,SixtyHz_selector = Workspace.Auxiliary.OptionBlock.SixtyHzOption.SixtyHz_selector.SixtyHz,MEPS_ME_selector = Workspace.Auxiliary.OptionBlock.MEPS_ME.MEPS_ME_selector.MEPS_ME,Coating_selector = Workspace.Auxiliary.OptionBlock.CoatingOption.Coating_selector.STANDARD,FAN_SPEED_selector = Workspace.Auxiliary.OptionBlock.FanSpeed.Selector.FAN_FS),controllerSettings_crkA(SDT_max = 273.15 + (if controllerSettings_crkA.isHighAmbientOption then (if controllerSettings_crkA.is60HZ and controllerSettings_crkA.OAT_sdt_offset > (52 + 273.15) then 70.5 else 70) else 67)),controllerSettings_crkB(SDT_max = 273.15 + (if controllerSettings_crkB.isHighAmbientOption then (if controllerSettings_crkB.is60HZ and controllerSettings_crkB.OAT_sdt_offset > (52 + 273.15) then 70.5 else 70) else 67)),controller(completeCompressorControl_base(capacity_controller(AV_min = if StateMachine.currentModeID == Workspace.Controller.StateMachine.ModeID30KAV.CrkAB_1 then 0.1 else 0))),ECO_ON_inter = 0.3096,ECO_ON_slope = 1.4073)
    annotation (Icon(graphics={Text(textString="CL",origin={2,-12},extent={{-100,75},{100,-75}})}));
  annotation (
    Icon(
      graphics={
        Text(
          textString="CL",
          origin={-2,-10},
          extent={{-96.01685393258424,70.66502830561285},{97,-70}})}));
end CL_30XBV_OnePasse;
