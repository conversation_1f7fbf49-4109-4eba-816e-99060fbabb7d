within Workspace.Auxiliary.BusinessFactors_30XBV;
model BusinessFactors_cooling
  "Final BF implementation for cooling mode"
  parameter Boolean use_business_factor=true;
  parameter Real c_load_bf_cap=0.1
    "capacity business factor coefficient";
  parameter Real c_const_bf_cap=0.92
    "capacity business factor coefficient";
  parameter Real c_load2_bf_cap=0.92
    "capacity business factor coefficient";
  parameter Real c_load2_bf_pow=-0.114
    "power business factor coefficient";
  parameter Real c_load_bf_pow=0.239
    "power business factor coefficient";
  parameter Real c_const_bf_pow=0.876
    "power business factor coefficient";
  parameter Boolean is_fixedSpeed;
  parameter Real c_load2_bf_pow_opt17A=-0.114
    "power business factor coefficient when opt17A is selected";
  parameter Real c_load_bf_pow_opt17A=0.239
    "power business factor coefficient when opt17A is selected";
  parameter Real c_const_bf_pow_opt17A=0.876
    "power business factor coefficient when opt17A is selected";
  parameter Real bf_cap_max=1
    "capacity business factor max value";
  parameter Real bf_cap_min=1
    "capacity business factor min value";
  parameter Real bf_pow_max=1
    "power business factor max value";
  parameter Real bf_pow_min=1
    "power business factor min value";
  parameter Real bf_pow_max_opt17A=1
    "power business factor max value when opt17A is selected";
  parameter Real bf_pow_min_opt17A=1
    "power business factor min value when opt17A is selected";
  Real bf_cap_interp(
    start=1);
  Real bf_pow_interp(
    start=1);
  Real bf_pow_interp_opt17A(
    start=1);
public
  .Modelica.Blocks.Interfaces.RealInput net_pow
    annotation (Placement(transformation(extent={{-100,30},{-60,70}})));
  .Modelica.Blocks.Interfaces.RealInput gross_pow
    annotation (Placement(transformation(extent={{-100,-30},{-60,10}})));
  .Modelica.Blocks.Interfaces.RealInput gross_cool_cap
    annotation (Placement(transformation(extent={{-100,-60},{-60,-20}})));
  .Modelica.Blocks.Interfaces.RealInput load
    annotation (Placement(transformation(extent={{-100.0,-122.0},{-60.0,-82.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealInput net_cool_cap
    annotation (Placement(transformation(extent={{-100,60},{-60,100}})));
  .Modelica.Blocks.Interfaces.RealOutput pub_net_cool_cap
    annotation (Placement(transformation(extent={{80.0,50.0},{100.0,70.0}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealOutput pub_net_pow
    annotation (Placement(transformation(extent={{80,10},{100,30}})));
  .Modelica.Blocks.Interfaces.RealOutput pub_gross_cool_cap
    annotation (Placement(transformation(extent={{80,-30},{100,-10}})));
  .Modelica.Blocks.Interfaces.RealOutput pub_gross_pow
    annotation (Placement(transformation(extent={{80,-60},{100,-40}})));
  .Modelica.Blocks.Interfaces.RealInput OAT_C
    annotation (Placement(transformation(extent={{-100.0,-158.0},{-60.0,-118.0}},rotation=0.0,origin={0.0,0.0})));
equation
  bf_cap_interp=
    if OAT_C < 48 then
      if use_business_factor then
        min(
          bf_cap_max,
          max(
            bf_cap_min,
            c_load2_bf_cap*load*load+c_load_bf_cap*load+c_const_bf_cap))
      else
        1
    else
      1;
  bf_pow_interp_opt17A=
    if OAT_C < 48 then
      if use_business_factor then
        if load > 0.4 then
          min(
            bf_pow_max_opt17A,
            max(
              bf_pow_min_opt17A,
              c_load2_bf_pow_opt17A*load*load+c_load_bf_pow_opt17A*load+c_const_bf_pow_opt17A))
        else
          1
      else
        1
    else
      1;
  bf_pow_interp=
    if OAT_C < 48 then
      if use_business_factor then
        min(
          bf_pow_max,
          max(
            bf_pow_min,
            c_load2_bf_pow*load*load+c_load_bf_pow*load+c_const_bf_pow))
      //    then 
      //  if load >= 0.85 then 0.98 else 1
      else
        1
    else
      1;
  pub_net_cool_cap=bf_cap_interp*net_cool_cap;
  pub_gross_cool_cap=bf_cap_interp*gross_cool_cap;
  pub_net_pow=
    if is_fixedSpeed then
      bf_pow_interp*net_pow
    else
      bf_pow_interp*bf_pow_interp_opt17A*net_pow;
  pub_gross_pow=
    if is_fixedSpeed then
      bf_pow_interp*gross_pow
    else
      bf_pow_interp*bf_pow_interp_opt17A*gross_pow
    annotation (Icon(graphics={Rectangle(origin={10,-12},extent={{-70,106},{70,-106}},fillPattern=FillPattern.Solid,fillColor={80,227,194}),Text(textString="BusinessFactors_cooling",origin={11,-15},extent={{-67,-47},{67,47}})}));
    annotation(Icon(graphics = {Rectangle(origin={10,-24},extent={{-69,122},{69,-122}},fillPattern=FillPattern.Solid,fillColor={131,163,194}),Text(textString="BF",origin={7,-20},extent={{-63,34},{63,-34}})}));
end BusinessFactors_cooling;
