within Workspace.Surrogate.MCHX_Cond;
function getSelector
  input Selector selector
    "Selected parameterisation";
  output Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.Configuration configuration;
protected
  constant Workspace.Surrogate.MCHX_Cond.MCHX_Cond_R134a MCHX_Cond_R134a;
  constant Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.Configuration[:] values={MCHX_Cond_R134a};
algorithm
  configuration := values[Integer(
    selector)];
end getSelector;
