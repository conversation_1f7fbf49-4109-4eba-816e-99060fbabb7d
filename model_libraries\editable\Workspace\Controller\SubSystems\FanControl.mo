within KAV_initiation.System30KAV.Controller30KAV.SubSystems;
model FanControl
  extends.Workspace.Controller.SubSystems.BaseClasses.FanControlBase;
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController(
    AV_min=0.1,
    AV_max=1,
    AV_start=0.9,
    isOff=crkIsOff)
    annotation (Placement(transformation(extent={{64.0,-10.0},{84.0,10.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation fanSpeed_error(
    measurement=actuatorSignal,
    ID=1,
    gain=1,
    setPoint=fanSpeed_setpoint/((((MaxFrequency)))))
    annotation (Placement(transformation(extent={{-105.14623271598376,17.784890797495848},{-54.85376728401624,38.21510920250415}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_sdt_min_error(
    ID=2,
    gain=-1/301.7,
    setPoint=T_sdt_min_limit,
    measurement=T_sdt,
    isOff=isOffSDTmin)
    annotation (Placement(transformation(extent={{-105.14623271598376,-0.021510920250410237},{-54.85376728401624,20.4087074847579}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_sdt_max_error(
    ID=3,
    gain=-1/(338.15),
    setPoint=T_sdt_max_limit_fan,
    measurement=T_sdt,
    isOff=isOffSDTmax)
    annotation (Placement(transformation(extent={{-105.14623271598376,-18.215109202504156},{-54.85376728401624,2.2151092025041557}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_dgt_max_error(
    ID=4,
    gain=-1/365.15,
    setPoint=T_dgt_max_limit_fan,
    measurement=T_dgt,
    isOff=isOffDGTmax)
    annotation (Placement(transformation(extent={{-105.14623271598376,-36.795904049265374},{-54.85376728401624,-16.36568564425707}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min
    annotation (Placement(transformation(extent={{-39.311992958271766,12.688007041728234},{-20.688007041728234,31.311992958271766}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Max max
    annotation (Placement(transformation(extent={{-6.76767259947931,5.232327400520688},{14.76767259947931,26.76767259947931}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Max max2
    annotation (Placement(transformation(extent={{29.297600636593984,-0.7023993634060197},{50.702399363406016,20.70239936340602}},origin={0.0,0.0},rotation=0.0)));
  parameter Real MaxFrequency;
  parameter Boolean isOffSDTmin;
  parameter Boolean isOffSDTmax;
  parameter Boolean isOffDGTmax;
protected
  .Modelica.Blocks.Interfaces.RealOutput T_sdt
    "Discharge gas temperature"
    annotation (Placement(transformation(extent={{-68.49941158124145,65.50058841875855},{-43.500588418758554,90.49941158124145}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_dgt
    "Discharge gas temperature"
    annotation (Placement(transformation(extent={{-68.49941158124145,47.500588418758554},{-43.500588418758554,72.49941158124145}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_sdt_min_limit
    "Discharge gas temperature"
    annotation (Placement(transformation(extent={{-68.49941158124145,-90.49941158124145},{-43.500588418758554,-65.50058841875855}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_sdt_max_limit_fan
    "Discharge gas temperature"
    annotation (Placement(transformation(extent={{-68.49941158124145,-108.49941158124145},{-43.500588418758554,-83.50058841875855}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_dgt_max_limit_fan
    "Discharge gas temperature"
    annotation (Placement(transformation(extent={{-68.49941158124145,-126.49941158124145},{-43.500588418758554,-101.50058841875855}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput fanSpeed_setpoint
    "Discharge gas temperature"
    annotation (Placement(transformation(extent={{-68.49941158124145,-72.49941158124145},{-43.500588418758554,-47.500588418758554}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(setpointController.actuatorSignal,actuatorSignal)
    annotation (Line(points={{84.6,0},{100,0}},color={0,0,127}));
  connect(T_dgt,measurementBus.T_dgt)
    annotation (Line(points={{-56,60},{-78,60},{-78,60.1},{-99.9,60.1}},color={0,0,127}));
  connect(T_sdt,measurementBus.T_sdt)
    annotation (Line(points={{-56,78},{-99.9,78},{-99.9,60.1}},color={0,0,127}));
  connect(T_sdt_min_limit,limitsBus.T_sdt_min_limit)
    annotation (Line(points={{-56,-78},{-99.95,-78},{-99.95,-59.95}},color={0,0,127}));
  connect(fanSpeed_setpoint,limitsBus.fanSpeed_setpoint)
    annotation (Line(points={{-56,-60},{-78,-60},{-78,-59.95},{-99.95,-59.95}},color={0,0,127}));
  connect(fanSpeed_error.sensor,min.u1)
    annotation (Line(points={{-55.35669193833592,27.18279126379967},{-47.67834596916796,27.18279126379967},{-47.67834596916796,27.214716056632188},{-39.311992958271766,27.214716056632188}},color={28,108,200}));
  connect(T_sdt_min_error.sensor,min.u2)
    annotation (Line(points={{-55.35669193833591,9.376389546053414},{-46,9.376389546053414},{-46,16.41280422503694},{-39.311992958271766,16.41280422503694}},color={28,108,200}));
  connect(min.y,max.u1)
    annotation (Line(points={{-20.688007041728234,22},{-14.344003520864117,22},{-14.344003520864117,22.029896655708413},{-6.767672599479306,22.029896655708413}},color={28,108,200}));
  connect(max.u2,T_sdt_max_error.sensor)
    annotation (Line(points={{-6.767672599479306,9.539396440312414},{-14,9.539396440312414},{-14,-8.817208736200332},{-55.35669193833592,-8.817208736200332}},color={28,108,200}));
  connect(max.y,max2.u1)
    annotation (Line(points={{14.767672599479306,16},{19,16},{19,15.993343643507373},{29.297600636593984,15.993343643507373}},color={28,108,200}));
  connect(max2.u2,T_dgt_max_error.sensor)
    annotation (Line(points={{29.297600636593984,3.578560381956388},{22,3.578560381956388},{22,-27.398003582961554},{-55.35669193833591,-27.398003582961554}},color={28,108,200}));
  connect(max2.y,setpointController.errorSignal)
    annotation (Line(points={{50.702399363406016,10},{58,10},{58,0},{64,0}},color={28,108,200}));
  connect(T_dgt_max_limit_fan,limitsBus.T_dgt_max_limit_fan)
    annotation (Line(points={{-56,-114},{-99.95,-114},{-99.95,-59.95}},color={0,0,127}));
  connect(T_sdt_max_limit_fan,limitsBus.T_sdt_max_limit_fan)
    annotation (Line(points={{-56,-96},{-99.95,-96},{-99.95,-59.95}},color={0,0,127}));
end FanControl;
