within Workspace.Surrogate.Tests_MCHX;
model test_surr
  .Workspace.HXComponents.RefAir_MCHX.CondAirM_30XBV condAirM_30XBV(
    tubeOrientation=.BOLT.InternalLibrary.BuildingBlocks.Types.TubeOrientation.Vertical_inletTop,
    n_slabs=1,
    n_tubes={{90,30}},
    userDefined_geo(
      length_tube=1.937,
      diameter_in_header=0.02395,
      length_tube_inlet=0.02286,
      diameter_in_inletTube=0.0157,
      length_outlet=0.02286,
      diameter_in_outlet=0.0157),
    selector_curve_num=106,
    nCoils=3)
    annotation (Placement(transformation(extent={{-28.49652390608573,10.031948205516727},{8.49652390608573,45.96805179448327}},rotation=0.0,origin={0.0,0.0})));
  .BOLT.BoundaryNode.Refrigerant.Source source(
    m_flow_set=0.5,
    x_set=1.13,
    streamID=1)
    annotation (Placement(transformation(extent={{-62.0,24.125},{-54.0,32.125}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Air.Sink sink(
    pg_set=0)
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-10.0,64.625},rotation=-90.0)));
  .BOLT.BoundaryNode.Refrigerant.Sink sink2(
    p_fixed=false,
    x_set=-0.05,
    x_fixed=true,
    streamID=1)
    annotation (Placement(transformation(extent={{34.0,24.0},{26.0,32.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Air.Source source2(
    Vds_flow_fixed=false,
    Vd_flow_fixed=true,
    Vd_flow_set=0.8,
    Tdb_set=308.15,
    pg_set=0,
    pg_fixed=false)
    annotation (Placement(transformation(extent={{4.0,4.0},{-4.0,-4.0}},origin={-10.0,-9.375},rotation=-90.0)));
  Real T_sat;
  Real x_out_crit=-0.2;
  Real delta_x_out_interp=0.05;
  constant Workspace.Surrogate.MCHX_Cond.MCHX_Cond_R134a MCHX_Cond_R134a;
equation
  connect(source.port,condAirM_30XBV.port_a_ref)
    annotation (Line(points={{-54,28.125},{-44.39267101707744,28.125},{-44.39267101707744,28},{-34.78534203415488,28}},color={255,0,0}));
  connect(condAirM_30XBV.port_b_ref,sink2.port)
    annotation (Line(points={{8.496523906085727,28},{26,28}},color={255,0,0}));
  connect(condAirM_30XBV.port_b_air,sink.port)
    annotation (Line(points={{-10,43.81188557914528},{-10,60.625}},color={0,0,255}));
  connect(source2.port,condAirM_30XBV.port_a_air)
    annotation (Line(points={{-10,-5.375},{-10,12.547475456744387}},color={0,0,255}));
  T_sat=Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.NNFunction(
    MCHX_Cond_R134a.layer_Tsat_in,
    source.summary.m_flow,
    source.summary.x,
    sink2.summary.x,
    sink.summary.Vd_flow,
    source2.summary.Tdb,
    1,
    1)
    annotation (Icon(coordinateSystem(preserveAspectRatio=false,extent={{-100.0,-100.0},{100.0,100.0}}),graphics={Rectangle(lineColor={0,0,0},fillColor={230,230,230},fillPattern=FillPattern.Solid,extent={{-100.0,-100.0},{100.0,100.0}}),Text(lineColor={0,0,255},extent={{-150,150},{150,110}},textString="%name")}));
end test_surr;
