within Workspace.System.BaseCycle.Optimization;
model System_XBV_opti
  extends.Workspace.Controller.CL_XBV_System(
    redeclare replaceable.Workspace.Controller.Controller_opti controller(
      crkA_isOff=equipment.isOffA,
      crkB_isOff=equipment.isOffB,
      ecoA_isOff=equipment.isOffECOA,
      ecoB_isOff=equipment.isOffECOB,
      crkA_min_speed=equipment.NcompAMin,
      crkB_min_speed=equipment.NcompBMin,
      crkA_max_speed=equipment.NcompAMax,
      crkB_max_speed=equipment.NcompBMax,
      isOffSDTmin_fan=true,
      isOffSDTmax_fan=true,
      isOffDGTmax_fan=true,
      isOffSSTmin_EXV=false,
      isOffSSTmax_EXV=false,
      isOffDSHmin_EXV=true,
      isOffDGTmax_EXV=true,
      isOffSSTmin_comp=true,
      isOffSDTmax_comp=true,
      isOffDGTmax_comp=true,
      MaxFrequency=
        if equipment.is60HZ then
          60
        else
          50),
    UseStateMachine=true,
    controllerSettings_crkA(
      capacity_setpoint=targetCapacity,
      compressorFrequency_min=equipment.NcompAMin,
      compressorFrequency_max=equipment.NcompAMax,
      T_sst_min_limit_comp=273.15,
      compressor=choiceBlock.Unit.CompType_CKA,
      dT_esh_setpoint=equipment.eco_ssh_A,
      machineError_Temperature=0.1,
      nbrCoils=choiceBlock.Unit.nCoil_CKA,
      dT_sbc_max=10,
      FanFrequency_max=
        if equipment.is60HZ then
          60
        else
          50),
    controllerSettings_crkB(
      capacity_setpoint=targetCapacity,
      compressorFrequency_min=equipment.NcompBMin,
      compressorFrequency_max=equipment.NcompBMax,
      compressor=choiceBlock.Unit.CompType_CKB,
      dT_esh_setpoint=equipment.eco_ssh_B,
      machineError_Temperature=0.1,
      nbrCoils=choiceBlock.Unit.nCoil_CKB,
      dT_sbc_max=10,
      FanFrequency_max=
        if equipment.is60HZ then
          60
        else
          50));
  .Workspace.System.BaseCycle.Optimization.Equipment_opti equipment(
    isOffA=isOffA,
    isOffB=isOffB,
    isOffECOA=isEcoAOff,
    isOffECOB=isEcoBOff,
    Vi_A=Vi_A,
    Vi_B=Vi_B,
    n_coilsB=choiceBlock.Unit.nCoil_CKB,
    n_coilsA=choiceBlock.Unit.nCoil_CKA,
    NcompAMax=choiceBlock.Unit.NcompMax_CKA,
    NcompAMin=choiceBlock.Unit.NcompMin_CKA,
    NcompBMax=choiceBlock.Unit.NcompMax_CKB,
    NcompBMin=choiceBlock.Unit.NcompMin_CKB,
    redeclare replaceable model EvapFL_2C=.BOLT.Evaporator.RefCoolant.EvapFL_2C(
      selector_tube=.Public_database.Evaporator.Tube.Selector.userDefined,
      userDefined_geo1(
        length_tube=118*0.0254,
        diameter_in_shell=0.5715)),
    diameter_in_shell_evap=choiceBlock.Unit.evap_diameter_in_shell,
    n_tubes_passe1_evap=choiceBlock.Unit.evap_n_tubes_pass1,
    n_tubes_passe2_evap=choiceBlock.Unit.evap_n_tubes_pass2,
    n_plate_crkA=choiceBlock.Unit.eco_nPlate_CKA,
    n_plate_crkB=choiceBlock.Unit.eco_nPlate_CKB,
    voltage_crkA=choiceBlock.Unit.CompVoltage_CKA,
    voltage_crkB=choiceBlock.Unit.CompVoltage_CKB,
    n_coil_crkA=choiceBlock.Unit.nCoil_CKA,
    n_coil_crkB=choiceBlock.Unit.nCoil_CKB,
    Di_oil_crkA=choiceBlock.Unit.OilSepDiameter_CKA,
    Di_oil_crkB=choiceBlock.Unit.OilSepDiameter_CKB,
    length_oil_crkA=choiceBlock.Unit.OilSepLength_CKA,
    length_oil_crkB=choiceBlock.Unit.OilSepLength_CKB,
    dp_ref_oil_crkA=choiceBlock.Unit.Oil_sepA_DP,
    dp_ref_oil_crkB=choiceBlock.Unit.Oil_sepB_DP,
    m_flow_ref_oil_crkA=choiceBlock.Unit.Oil_sepA_MF,
    m_flow_ref_oil_crkB=choiceBlock.Unit.Oil_sepB_MF,
    dp_ref_SL_crkA=choiceBlock.Unit.Suc_lineA_DP,
    dp_ref_SL_crkB=choiceBlock.Unit.Suc_lineB_DP,
    m_flow_ref_SL_crkA=choiceBlock.Unit.Suc_lineA_MF,
    m_flow_ref_SL_crkB=choiceBlock.Unit.Suc_lineB_MF,
    dp_ref_DL_crkA=choiceBlock.Unit.Dis_lineA_DP,
    dp_ref_DL_crkB=choiceBlock.Unit.Dis_lineB_DP,
    m_flow_ref_DL_crkA=choiceBlock.Unit.Dis_lineA_MF,
    m_flow_ref_DL_crkB=choiceBlock.Unit.Dis_lineB_MF,
    Selector_comp_crkA=choiceBlock.Unit.selector_Comp_CKA,
    Selector_comp_crkB=choiceBlock.Unit.selector_Comp_CKB,
    selector_VFDA_CKA=choiceBlock.Unit.selector_VFDA_CKA,
    selector_VFDA_CKB=choiceBlock.Unit.selector_VFDA_CKB,
    evap_selector_tube=choiceBlock.Unit.evap_selector_tube,
    Eco_Geo_CKA=choiceBlock.Unit.Eco_Geo_CKA,
    Eco_Geo_CKB=choiceBlock.Unit.Eco_Geo_CKB,
    n_passes_evap=2,
    capacity_design=choiceBlock.Unit.Capacity_design,
    is_fixedSpeed=choiceBlock.FAN_SPEED,
    motorA(
      use_shaftSpeed_in=false),
    length_tube_evap_crkA=choiceBlock.Unit.evap_length_tube_CKA,
    length_tube_evap_crkB=choiceBlock.Unit.evap_length_tube_CKB,
    EWT=ECAT.EvapBrineEWT_K.setPoint-273.15,
    LWT=ECAT.EvapBrineLWT_K.setPoint-273.15,
    FanA(
      nStage_fixed=true),
    sourcebrine(
      Vd_fixed=ECAT.EvapBrineFlowRate_m3s.fixed,
      Vd_set=ECAT.EvapBrineFlowRate_m3s.setPoint,
      T_fixed=ECAT.EvapBrineEWT_K.fixed and not equipment.isEWTUnfixed),
    sinkbrine(
      T_fixed=ECAT.EvapBrineLWT_K.fixed and not equipment.isLWTUnfixed),
    coolantConcentration=ECAT.EvapBrineConcentration_nd.setPoint,
    OAT=ECAT.AmbientAirDBTemp_K.setPoint-273.15,
    sourceAirA(
      RH_set=ECAT.AmbientAirRH_nd.setPoint),
    sourceAirB(
      RH_set=equipment.sourceAirA.RH_set),
    evaporator(
      R_foul=ECAT.EvapFoulingFactor_m2KW.setPoint),
    BPHEECOA(
      Dport_ref_H_a=choiceBlock.Unit.eco_DportH_a_A,
      Dport_ref_H_b=choiceBlock.Unit.eco_DportH_b_A,
      Dport_ref_L_a=choiceBlock.Unit.eco_DportL_a_A,
      Dport_ref_L_b=choiceBlock.Unit.eco_DportL_b_A),
    BPHEECOB(
      Dport_ref_H_a=choiceBlock.Unit.eco_DportH_a_B,
      Dport_ref_H_b=choiceBlock.Unit.eco_DportH_b_B,
      Dport_ref_L_a=choiceBlock.Unit.eco_DportL_a_B,
      Dport_ref_L_b=choiceBlock.Unit.eco_DportL_b_B),
    is60HZ=choiceBlock.is60HZ,
    max_fan_rpm=
      if choiceBlock.is60HZ then
        1140
      else
        950,
    max_motor_frequency=
      if choiceBlock.is60HZ then
        60
      else
        50,
    limit_max_fan_rpm=
      if choiceBlock.is60HZ then
        1140
      else
        950)
    annotation (Placement(transformation(extent={{22.0,-12.0},{62.0,28.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.OptionBlock.HighAmbiantTemperature.ChoiceBlock choiceBlock(
    Selector=.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Selector.Unit_500)
    annotation (Placement(transformation(extent={{-81.57546614020706,106.42453385979289},{-50.424533859792945,137.5754661402071}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean isOffA=not StateMachine.currentMode.CrkA
    "set true to turn off circuit A";
  parameter Boolean isOffB=not StateMachine.currentMode.CrkB
    "set true to turn off circuit B";
  parameter Boolean isEcoAOff=not StateMachine.currentMode.EcoA
    "set true to turn off eco line in circuit A";
  parameter Boolean isEcoBOff=not StateMachine.currentMode.EcoB
    "set true to turn off eco line in circuit B";
  parameter Boolean Vi_A=StateMachine.currentMode.ViA
    "set true to turn on circuit A VI";
  parameter Boolean Vi_B=StateMachine.currentMode.ViB
    "set true to turn off circuit B VI";
  parameter Real ECO_ON_inter=139.5
    "Value of the OAT max to turn on the ECO at 273.15 K for LWT";
  parameter Real ECO_ON_slope=0.523
    "Slope for the boundary to turn off the ECO according to the OAT and the LWT";
  //output Real VI_A;
  //output Real VI_B;
  parameter Real targetCapacity=ECAT.TargetCoolingCapacity_W.setPoint
    annotation (Dialog(tab="Capacity Control"));
  .Workspace.Auxiliary.ECAT_Block.ECAT30XBVBase ECAT(
    RefrigerantCharge_kg(
      fixed={false,false},
      value=equipment.systemVariables.mRef),
    CondCoilAirPressDrop_Pa(
      value={equipment.condAirA.summary.dp_air,equipment.condAirB.summary.dp_air}),
    RefrigerantSST_K(
      value={equipment.nodeCpinA.Tsat,equipment.nodeCpinB.Tsat}),
    RefrigerantSDT_K(
      value={equipment.nodeCpoutA.Tsat,equipment.nodeCpoutB.Tsat}),
    RefrigerantSET_K(
      value={equipment.nodeevapoutA.Tsat,equipment.nodeevapoutB.Tsat}),
    RefrigerantSCT_K(
      value={equipment.nodecondAirinA.Tsat,equipment.nodecondAirinB.Tsat}),
    RefrigerantDGT_K(
      value={equipment.nodeCpoutA.T,equipment.nodeCpoutB.T}),
    SuctionSuperheat_K(
      value={equipment.nodeCpinA.dTsh,equipment.nodeCpinB.dTsh}),
    CondSubcooling_K(
      value={equipment.nodecondAiroutA.dTsh,equipment.nodecondAiroutB.dTsh}),
    DischargeSuperheat_K(
      value={equipment.nodeCpoutA.dTsh,equipment.nodeCpoutB.dTsh}),
    CompressorFrequency_Hz(
      setPoint={1.,1}),
    CondFanAirflowRate_m3s(
      value={equipment.sourceAirA.Vd_flow*(equipment.n_coilsA+equipment.n_coils_sup),equipment.sourceAirB.Vd_flow*(equipment.n_coilsB+equipment.n_coils_sup)}),
    CompressorPower_W(
      value={equipment.CompressorA.summary.P_compression,equipment.CompressorB.summary.P_compression}),
    FanPower_W(
      value={equipment.motorA.computeStreamsAggregates.pow,equipment.motorB.computeStreamsAggregates.pow}),
    Altitude_m(
      setPoint=0),
    AmbientAirDBTemp_K(
      setPoint=35+273.15),
    AmbientAirRH_nd(
      setPoint=0.6),
    CondBrineConcentration_nd(
      setPoint=1),
    CondBrineLWT_K(
      value=1),
    CondBrineEWT_K(
      value=1.),
    CondBrineFlowRate_m3s(
      value=0),
    CondFoulingFactor_m2KW(
      setPoint=0),
    EvapBrineConcentration_nd(
      setPoint=0),
    EvapFoulingFactor_m2KW(
      setPoint=0),
    EvapBrineLWT_K(
      value=equipment.sinkbrine.T,
      setPoint=7+273.15),
    EvapBrineEWT_K(
      value=equipment.sourcebrine.T,
      setPoint=12+273.15),
    EvapBrineFlowRate_m3s(
      value=equipment.sourcebrine.Vd*equipment.businessFactors.bf_cap_interp),
    TargetCoolingCapacity_W(
      setPoint=2000000),
    TargetHeatingCapacity_W(
      setPoint=1),
    TotalRefrigerantCharge_kg(
      value=equipment.systemVariables.summary.mRef[1]+equipment.systemVariables.summary.mRef[2]),
    TotalOilCharge_kg(
      value=-1),
    EvapPumpSpeed_rpm(
      value=-1),
    PubUnitPower_W(
      value=equipment.controlledPower),
    PubCoolingCapacity_W(
      value=equipment.controlledCapacity),
    PubHeatingCapacity_W(
      value=equipment.controlledHeating),
    TotalCompressorPower_W(
      value=equipment.CompressorA.summary.P_compression+equipment.CompressorB.summary.P_compression),
    TotalFanPower_W(
      value=sum(
        ECAT.FanPower_W.value)),
    EvapPumpPower_W(
      value=-1),
    CondPumpPower_W(
      value=-1),
    CondPumpSpeed_rpm(
      value=-1),
    EvapBrineIntPressDrop_Pa(
      value=equipment.evaporator.summary.dp_coolant),
    EvapPumpTotalHead_m(
      value=-1),
    EvapBrineDensity_kgm3(
      value=1/equipment.sinkbrine.v),
    CondBrineIntPressDrop_Pa(
      value=-1),
    CondPumpTotalHead_m(
      value=-1),
    CondBrineDensity_kgm3(
      value=-1),
    EvapBrineVelocity_mps(
      value=-1),
    CondBrineVelocity_mps(
      value=-1),
    StageNum_nd(
      value=1.1),
    redeclare replaceable.BOLT.InternalLibrary.ECATBlock.TableGenOutputs extraSettings,
    HeatingAmbientAirDBTemp_K(
      setPoint=1.05),
    EN14511PumpPowerCorrectionWithPump_W(
      value=equipment.en14511.M_pump),
    CondEN14511PumpPowerCorrectionWithPump_W(
      value=1),
    EN14511PumpPowerCorrectionWithoutPump_W(
      value=equipment.en14511.M),
    CondEN14511PumpPowerCorrectionWithoutPump_W(
      value=1),
    ExternalSystemKa_kPas2L2(
      value=-1),
    CondExternalSystemKa_kPas2L2(
      value=1),
    CondExternalSystemPressureDrop_Pa(
      value=1),
    IsMinimalCapacity(
      value=
        if controller.completeCompressorControl_base.capacity_controller.summary.ID ==-3 then
          1
        else
          0),
    IsCoolingMode(
      value=1),
    PubHeatingCapacityInstantaneous_W(
      value=1),
    HeatingAmbientAirWBTemp_K(
      value=-1),
    CondHighStaticFanExtPressDrop_Pa(
      value=1),
    EvapHighStaticFanExtPressDrop_Pa(
      value=1.1),
    HighStaticFanExternalKa_kPas2L2(
      value={1.1,1}),
    CondCoilHeatingCapacity_W(
      value={equipment.condAirA.summary.Q_flow_air,equipment.condAirB.summary.Q_flow_air}),
    CoolantFreezingTemp_K(
      value=1.05))
    annotation (Placement(transformation(extent={{-32.864038283143586,108.37526813901718},{-3.1359617168564142,138.10334470530438}},origin={0.0,0.0},rotation=0.0)));
  // Optimization
  .Workspace.Auxiliary.Records.Optimization_summary Optimization_Outputs
    annotation (Placement(transformation(extent={{-114.88333285655656,111.79803617397809},{-96.82715731138288,129.85421171915178}},origin={0.0,0.0},rotation=0.0)));
  output.Modelica.SIunits.Temperature LWT;
  output.Modelica.SIunits.Temperature OAT;
  output Real EER;
  output Real EER_crkA;
  output Real EER_crkB;
  output.Modelica.SIunits.Temperature EWT;
  output.Modelica.SIunits.Power P_flow_total;
  output.Modelica.SIunits.Power Q_flow_total;
  output.Modelica.SIunits.Power Q_flow_crkA;
  output.Modelica.SIunits.Power Q_flow_crkB;
  output.Modelica.SIunits.Power P_flow_crkA;
  output.Modelica.SIunits.Power P_flow_crkB;
  .Modelica.Blocks.Logical.Switch manual_FanA
    annotation (Placement(transformation(extent={{1.5567967105368927,26.62864212165605},{9.38415519801282,34.45600060913198}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.BooleanExpression FanA_manual_off
    annotation (Placement(transformation(extent={{-19.187894850511245,27.87923883287275},{-7.035605838903745,40.03152784448026}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator FanA_manual_value(
    maxBound=60,
    minBound=15,
    setPoint=60,
    isOff=not FanA_manual_off.y)
    annotation (Placement(transformation(extent={{-4.249437669134956,33.43611104223891},{-24.249437669134956,53.43611104223891}},origin={0.0,0.0},rotation=0.0)));
equation
  //ECO_ON_A_std = if   equipment.sourceAirA.summary.Tdb < ECO_ON_slope * equipment.sinkbrine.summary.T +  ECO_ON_inter then
  //equipment.CompressorA.summary.PR > 2.7 and equipment.CompressorA.summary.Ncomp / equipment.NcompAMax>0.3
  //else equipment.CompressorA.summary.PR > 2.3 and equipment.CompressorA.summary.Ncomp / equipment.NcompAMax>0.3;
  //ECO_ON_A_std = if   equipment.sourceAirB.summary.Tdb < ECO_ON_slope * equipment.sinkbrine.summary.T +  ECO_ON_inter then
  //equipment.CompressorB.summary.PR > 2.7 and equipment.CompressorB.summary.Ncomp / equipment.NcompBMax>0.3
  //else equipment.CompressorB.summary.PR > 2.3 and equipment.CompressorB.summary.Ncomp / equipment.NcompBMax>0.3;
  ECO_ON_A=equipment.CompressorA.summary.PR > 2.3 and equipment.CompressorA.summary.Ncomp/equipment.NcompAMax > 0.3;
  ECO_ON_B=equipment.CompressorB.summary.PR > 2.3 and equipment.CompressorB.summary.Ncomp/equipment.NcompBMax > 0.3;
  CAP_LOWsat=equipment.systemVariables.CoolCap_total < 0.23*equipment.globalParameters.capacity_design or controller.completeCompressorControl_base.capacity_controller.summary.ID ==-3;
  //VI_A    = .Workspace.Controller.StateMachine.isVIOn( equipment.Speed_crkA,
  //                                                    equipment.NcompAMax,
  //                                                    equipment.NcompAMin,
  //                                                    equipment.nodeCpoutA.summary.Tsat,
  //                                                    equipment.nodeCpinA.summary.Tsat)  ;
  VI_ON_A=.Workspace.Controller.StateMachine.isVIOn(
    equipment.Speed_crkA,
    equipment.NcompAMax,
    equipment.NcompAMin,
    equipment.nodeCpoutA.summary.Tsat,
    equipment.nodeCpinA.summary.Tsat) > 0.5;
  // VI_B    = .Workspace.Controller.StateMachine.isVIOn( equipment.Speed_crkB,
  //                                                     equipment.NcompBMax,
  //                                                     equipment.NcompBMin,
  //                                                     equipment.nodeCpoutB.summary.Tsat,
  //                                                     equipment.nodeCpinB.summary.Tsat);
  VI_ON_B=.Workspace.Controller.StateMachine.isVIOn(
    equipment.Speed_crkB,
    equipment.NcompBMax,
    equipment.NcompBMin,
    equipment.nodeCpoutB.summary.Tsat,
    equipment.nodeCpinB.summary.Tsat) > 0.5;
  // Optimization
  LWT=equipment.sinkbrine.summary.T;
  OAT=equipment.sourceAirA.summary.Tdb;
  EWT=equipment.sourcebrine.summary.T;
  Q_flow_total=equipment.systemVariables.summary.CoolCap_total;
  Q_flow_crkA=equipment.systemVariables.summary.CoolCap[1];
  Q_flow_crkB=equipment.systemVariables.summary.CoolCap[2];
  P_flow_total=equipment.systemVariables.summary.pow_total;
  P_flow_crkA=equipment.systemVariables.summary.pow[1];
  P_flow_crkB=equipment.systemVariables.summary.pow[2];
  EER=Q_flow_total/P_flow_total;
  EER_crkA=Q_flow_crkA/P_flow_crkA;
  EER_crkB=Q_flow_crkB/P_flow_crkB;
  Optimization_Outputs.EER=EER;
  Optimization_Outputs.EER_crkA=EER_crkA;
  Optimization_Outputs.EER_crkB=EER_crkB;
  Optimization_Outputs.P_flow_total=P_flow_total;
  Optimization_Outputs.P_flow_crkA=P_flow_crkA;
  Optimization_Outputs.P_flow_crkB=P_flow_crkB;
  Optimization_Outputs.Q_flow_total=Q_flow_total;
  Optimization_Outputs.Q_flow_crkA=Q_flow_crkA;
  Optimization_Outputs.Q_flow_crkB=Q_flow_total;
  connect(equipment.measurementBusA,controllerSettings_crkA.measurementBus)
    annotation (Line(points={{34.8174,30.156599999999997},{34.8174,62},{3.4000000000000004,62}},color={255,204,51}));
  connect(equipment.measurementBusA,controller.measurementBus_crkA)
    annotation (Line(points={{34.8174,30.156599999999997},{34.8174,38},{-84,38},{-84,21.750529153390108},{-75.64361307627159,21.750529153390108}},color={255,204,51}));
  connect(equipment.measurementBusB,controllerSettings_crkB.measurementBus)
    annotation (Line(points={{49.6174,30.156599999999997},{49.6174,62},{70,62},{70,-50},{5.4,-50}},color={255,204,51}));
  connect(equipment.measurementBusB,controller.measurementBus_crkB)
    annotation (Line(points={{49.6174,30.156599999999997},{49.6174,-26},{-100,-26},{-100,2.106916077118526},{-75.64361307627159,2.106916077118526}},color={255,204,51}));
  connect(controller.exv_crkA,equipment.EXV_Main_crkA)
    annotation (Line(points={{-34.10964503340015,25.013226002878355},{-7.265207962628839,25.013226002878355},{-7.265207962628839,21.68},{19.199999999999996,21.68}},color={0,0,127}));
  connect(controller.sstmax_crkA,equipment.actuatorSSTmaxA)
    annotation (Line(points={{-34.10964503340015,19.818404304699264},{-7.265207962628839,19.818404304699264},{-7.265207962628839,17.68},{19.199999999999996,17.68}},color={0,0,127}));
  connect(controller.ecoExv_crkA,equipment.EXV_Eco_crkA)
    annotation (Line(points={{-34.025238427871834,15.185268079102245},{-7.2230046598646815,15.185268079102245},{-7.2230046598646815,13.68},{19.199999999999996,13.68}},color={0,0,127}));
  connect(controller.compressor_crkA,equipment.Speed_crkA)
    annotation (Line(points={{-33.79933425433203,9.94453227928926},{-7.11005257309478,9.94453227928926},{-7.11005257309478,9.68},{19.199999999999996,9.68}},color={0,0,127}));
  connect(controller.compressor_crkB,equipment.Speed_crkB)
    annotation (Line(points={{-34.11249778972111,5.741612919953182},{-7.26663434078932,5.741612919953182},{-7.26663434078932,5.68},{19.199999999999996,5.68}},color={0,0,127}));
  connect(controller.fan_crkB,equipment.Fan_crkB)
    annotation (Line(points={{-34.1124977897211,0.6288021719152539},{-7.266634340789313,0.6288021719152539},{-7.266634340789313,1.6799999999999997},{19.199999999999996,1.6799999999999997}},color={0,0,127}));
  connect(controller.exv_crkB,equipment.EXV_Main_crkB)
    annotation (Line(points={{-34.531678061041724,-4.767051392961218},{-7.476224476449627,-4.767051392961218},{-7.476224476449627,-2.3200000000000003},{19.199999999999996,-2.3200000000000003}},color={0,0,127}));
  connect(controller.sstmax_crkB,equipment.actuatorSSTmaxB)
    annotation (Line(points={{-34.32208792538141,-7.5693362718790205},{-7.37142940861947,-7.5693362718790205},{-7.37142940861947,-6.32},{19.199999999999996,-6.32}},color={0,0,127}));
  connect(controller.ecoExv_crkB,equipment.EXV_Eco_crkB)
    annotation (Line(points={{-34.084032222305225,-11.314776761268217},{-34.084032222305225,-17.314776761268217},{19.199999999999996,-17.314776761268217},{19.199999999999996,-10.32}},color={0,0,127}));
  connect(controller.fan_crkA,manual_FanA.u3)
    annotation (Line(points={{-34.10964503340015,29.253489966949946},{-4.846710030068224,29.253489966949946},{-4.846710030068224,27.41137797040364},{0.7740608617893008,27.41137797040364}},color={0,0,127}));
  connect(manual_FanA.y,equipment.Fan_crkA)
    annotation (Line(points={{9.775523122386616,30.542321365394017},{19.199999999999996,30.542321365394017},{19.199999999999996,25.68}},color={0,0,127}));
  connect(FanA_manual_off.y,manual_FanA.u2)
    annotation (Line(points={{-6.42799138832337,33.955383338676505},{-2.826965263267035,33.955383338676505},{-2.826965263267035,30.542321365394017},{0.7740608617893008,30.542321365394017}},color={255,0,255}));
  connect(manual_FanA.u1,FanA_manual_value.value)
    annotation (Line(points={{0.7740608617893008,33.67326476038439},{-2.7207719711028457,33.67326476038439},{-2.7207719711028457,43.43611104223891},{-9.249437669134956,43.43611104223891}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={245,166,35},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name"),
        Text(
          textString="Optimization",
          origin={0,-78},
          extent={{-88.86398550912584,22.753034688856772},{89,-23}})}));
end System_XBV_opti;
