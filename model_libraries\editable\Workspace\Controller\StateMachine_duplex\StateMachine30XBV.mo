within StateMachine;
partial model StateMachine30XBV
  inner BOLT.Control.SteadyState.Utilities.CalculateExtraOutputs calculateExtraOutputs;
  BOLT.Control.SteadyState.StateMachine.StateMachine StateMachine(
    redeclare type ModeID=ModeID30XBV,
    redeclare record Mode=Mode30XBVBase,
    currentModeID=ModeID30XBV.CrkABCD_1,
    modes={mode_CrkABCD_1,mode_CrkABCDVIABCDEcoABCD_2,mode_CrkABCDVIABEcoABCD_3,mode_CrkABCDVICDEcoABCD_4,mode_CrkABCDVIABCDEcoAB_5,mode_CrkABCDVIABCDEcoCD_6,mode_CrkABCDVIABEcoAB_7,mode_CrkABCDVIABEcoCD_8,mode_CrkABCDVICDEcoAB_9,mode_CrkABCDVICDEcoCD_10,mode_CrkABCDVIAB_11,mode_CrkABCDVICD_12,mode_CrkABCDVIABCD_13,mode_CrkABCDEcoAB_14,mode_CrkABCDEcoCD_15,mode_CrkABCDEcoABCD_16,mode_CrkAB_17,mode_CrkABVIAB_18,mode_CrkABEcoAB_19,mode_CrkABVIABEcoAB_20})
    annotation (Placement(transformation(extent={{-109.94834955889496,74.05165044110504},{-90.05165044110504,93.94834955889496}},rotation=0.0,origin={0.0,0.0})));
  // Boolean declarations
  Boolean transitTo2;
  Boolean transitTo3;
  Boolean transitTo4;
  Boolean transitTo5;
  Boolean transitTo6;
  Boolean transitTo7;
  Boolean transitTo8;
  Boolean transitTo9;
  Boolean transitTo10;
  Boolean transitTo11;
  Boolean transitTo12;
  Boolean transitTo13;
  Boolean transitTo14;
  Boolean transitTo15;
  Boolean transitTo16;
  Boolean transitTo17;
  Boolean transitTo18;
  Boolean transitTo19;
  Boolean transitTo20;
  // Modes declarations
  CrkABCD_1 mode_CrkABCD_1(
    transitionCondition=false);
  CrkABCDVIABCDEcoABCD_2 mode_CrkABCDVIABCDEcoABCD_2(
    transitionCondition=transitTo2);
  CrkABCDVIABEcoABCD_3 mode_CrkABCDVIABEcoABCD_3(
    transitionCondition=transitTo3);
  CrkABCDVICDEcoABCD_4 mode_CrkABCDVICDEcoABCD_4(
    transitionCondition=transitTo4);
  CrkABCDVIABCDEcoAB_5 mode_CrkABCDVIABCDEcoAB_5(
    transitionCondition=transitTo5);
  CrkABCDVIABCDEcoCD_6 mode_CrkABCDVIABCDEcoCD_6(
    transitionCondition=transitTo6);
  CrkABCDVIABEcoAB_7 mode_CrkABCDVIABEcoAB_7(
    transitionCondition=transitTo7);
  CrkABCDVIABEcoCD_8 mode_CrkABCDVIABEcoCD_8(
    transitionCondition=transitTo8);
  CrkABCDVICDEcoAB_9 mode_CrkABCDVICDEcoAB_9(
    transitionCondition=transitTo9);
  CrkABCDVICDEcoCD_10 mode_CrkABCDVICDEcoCD_10(
    transitionCondition=transitTo10);
  CrkABCDVIAB_11 mode_CrkABCDVIAB_11(
    transitionCondition=transitTo11);
  CrkABCDVICD_12 mode_CrkABCDVICD_12(
    transitionCondition=transitTo12);
  CrkABCDVIABCD_13 mode_CrkABCDVIABCD_13(
    transitionCondition=transitTo13);
  CrkABCDEcoAB_14 mode_CrkABCDEcoAB_14(
    transitionCondition=transitTo14);
  CrkABCDEcoCD_15 mode_CrkABCDEcoCD_15(
    transitionCondition=transitTo15);
  CrkABCDEcoABCD_16 mode_CrkABCDEcoABCD_16(
    transitionCondition=transitTo16);
  CrkAB_17 mode_CrkAB_17(
    transitionCondition=transitTo17);
  CrkABVIAB_18 mode_CrkABVIAB_18(
    transitionCondition=transitTo18);
  CrkABEcoAB_19 mode_CrkABEcoAB_19(
    transitionCondition=transitTo19);
  CrkABVIABEcoAB_20 mode_CrkABVIABEcoAB_20(
    transitionCondition=transitTo20);
  parameter Boolean UseStateMachine
    "If true then StateMachine works normally if false, StateMachine stop and initiale current mode ID is never change"
    annotation (Dialog(group="Use Parameters"));
  // Transition condition declarations
  Boolean actuators_OnOff[12];
  Boolean CAP_LOWsat;
  Boolean ECO_ON_A;
  Boolean ECO_ON_B;
  Boolean ECO_ON_C;
  Boolean ECO_ON_D;
  Boolean VI_ON_A;
  Boolean VI_ON_B;
  Boolean VI_ON_C;
  Boolean VI_ON_D;
equation
  // Transition condition equations
  when calculateExtraOutputs then
    transitTo2=
      if UseStateMachine then
        (ECO_ON_A and ECO_ON_B and ECO_ON_C and ECO_ON_D) and(VI_ON_A and VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat
      else
        false;
    transitTo3=
      if UseStateMachine then
        (ECO_ON_A and ECO_ON_B and ECO_ON_C and ECO_ON_D) and(VI_ON_A and VI_ON_B and not VI_ON_C and not VI_ON_D) and not CAP_LOWsat
      else
        false;
    transitTo4=
      if UseStateMachine then
        (ECO_ON_A and ECO_ON_B and ECO_ON_C and ECO_ON_D) and(not VI_ON_A and not VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat
      else
        false;
    transitTo5=
      if UseStateMachine then
        (ECO_ON_A and ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(VI_ON_A and VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat
      else
        false;
    transitTo6=
      if UseStateMachine then
        (not ECO_ON_A and not ECO_ON_B and ECO_ON_C and ECO_ON_D) and(VI_ON_A and VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat
      else
        false;
    transitTo7=
      if UseStateMachine then
        ((ECO_ON_A and ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(VI_ON_A and VI_ON_B and not VI_ON_C and not VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo8=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and ECO_ON_C and ECO_ON_D) and(VI_ON_A and VI_ON_B and not VI_ON_C and not VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo9=
      if UseStateMachine then
        ((ECO_ON_A and ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(not VI_ON_A and not VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo10=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and ECO_ON_C and ECO_ON_D) and(not VI_ON_A and not VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo11=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(VI_ON_A and VI_ON_B and not VI_ON_C and not VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo12=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(not VI_ON_A and not VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo13=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(VI_ON_A and VI_ON_B and VI_ON_C and VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo14=
      if UseStateMachine then
        ((ECO_ON_A and ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(not VI_ON_A and not VI_ON_B and not VI_ON_C and not VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo15=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and ECO_ON_C and ECO_ON_D) and(not VI_ON_A and not VI_ON_B and not VI_ON_C and not VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo16=
      if UseStateMachine then
        ((ECO_ON_A and ECO_ON_B and ECO_ON_C and ECO_ON_D) and(not VI_ON_A and not VI_ON_B and not VI_ON_C and not VI_ON_D) and not CAP_LOWsat)
      else
        false;
    transitTo17=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(not VI_ON_A and not VI_ON_B and not VI_ON_C and not VI_ON_D) and CAP_LOWsat)
      else
        false;
    transitTo18=
      if UseStateMachine then
        ((not ECO_ON_A and not ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(VI_ON_A and VI_ON_B and not VI_ON_C and not VI_ON_D))
      else
        false;
    transitTo19=
      if UseStateMachine then
        ((ECO_ON_A and ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(not VI_ON_A and not VI_ON_B and not VI_ON_C and not VI_ON_D))
      else
        false;
    transitTo20=
      if UseStateMachine then
        (ECO_ON_A and ECO_ON_B and not ECO_ON_C and not ECO_ON_D) and(VI_ON_A and VI_ON_B and not VI_ON_C and not VI_ON_D)
      else
        false;
  end when;
  actuators_OnOff={StateMachine.modes[Integer(StateMachine.nextModeID)].CrkA,StateMachine.modes[Integer(StateMachine.nextModeID)].CrkB,StateMachine.modes[Integer(StateMachine.nextModeID)].CrkC,StateMachine.modes[Integer(StateMachine.nextModeID)].CrkD,StateMachine.modes[Integer(StateMachine.nextModeID)].EcoA,StateMachine.modes[Integer(StateMachine.nextModeID)].EcoB,StateMachine.modes[Integer(StateMachine.nextModeID)].EcoC,StateMachine.modes[Integer(StateMachine.nextModeID)].EcoD,StateMachine.modes[Integer(StateMachine.nextModeID)].ViA,StateMachine.modes[Integer(StateMachine.nextModeID)].ViB,StateMachine.modes[Integer(StateMachine.nextModeID)].ViC,StateMachine.modes[Integer(StateMachine.nextModeID)].ViD};
  annotation (
    Placement(
      transformation(
        extent={{-78,8},{-58,28}})),
    Placement(
      transformation(
        extent={{-68,12},{-48,32}})));
end StateMachine30XBV;
