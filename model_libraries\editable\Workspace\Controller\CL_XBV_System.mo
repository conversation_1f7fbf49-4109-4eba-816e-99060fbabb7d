within Workspace.Controller;
model CL_XBV_System
  extends.Workspace.Controller.StateMachine.StateMachine30KAV;
  .Workspace.Controller.ControllerSettings controllerSettings_crkA
    annotation (Placement(transformation(extent={{2.0,52.0},{-18.0,72.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings controllerSettings_crkB
    annotation (Placement(transformation(extent={{4.0,-60.0},{-16.0,-40.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.Controller controller
    annotation (Placement(transformation(extent={{-75.64361307627159,-11.643613076271581},{-36.35638692372841,27.64361307627158}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(controllerSettings_crkA.limitsBus,controller.limitsBus_crkA)
    annotation (Line(points={{-19.8,60},{-100,60},{-100,13.893083922881475},{-75.64361307627159,13.893083922881475}},color={255,204,51}));
  connect(controllerSettings_crkB.limitsBus,controller.limitsBus_crkB)
    annotation (Line(points={{-17.8,-52},{-100,-52},{-100,-5.946965284152824},{-75.64361307627159,-5.946965284152824}},color={255,204,51}));
end CL_XBV_System;
