within Workspace.System.R134a.Physical;
model CL_30XBV_TwoPasse
  extends.Workspace.System.BaseCycle.XBV_2C.Physical.System_30XBV(
    ModuleA(
      n_passes_evap=2),
    choiceBlock(
      Coating_selector=Workspace.Auxiliary.OptionBlock.CoatingOption.Coating_selector.STANDARD,
      FAN_SPEED_selector=Workspace.Auxiliary.OptionBlock.FanSpeed.Selector.FAN_FS,
      SixtyHz_selector=Workspace.Auxiliary.OptionBlock.SixtyHzOption.SixtyHz_selector.SixtyHz,
      Pump_selector=Workspace.Auxiliary.OptionBlock.PumpOption.Pump_selector.SINGLE_PUMP_HIGH_PRESSURE),
    ECO_ON_inter=-0.625,
    ECO_ON_slope=0.944)
    annotation (Icon(graphics={Text(textString="CL",origin={2,-12},extent={{-100,75},{100,-75}})}));
  annotation (
    Icon(
      graphics={
        Text(
          textString="CL",
          origin={-2,-10},
          extent={{-96.01685393258424,70.66502830561285},{97,-70}})}));
end CL_30XBV_TwoPasse;
