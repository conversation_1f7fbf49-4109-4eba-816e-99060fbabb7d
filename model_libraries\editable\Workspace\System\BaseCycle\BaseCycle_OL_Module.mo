within Workspace.System.BaseCycle;
model BaseCycle_OL_Module
  extends.Workspace.System.BaseCycle.OpenLoop_Duplex(
    BPHEECOA(
      nPlate=n_plate_crkA,
      selectGeo=Eco_Geo_CKA,
      Dport_ref_H_a=eco_DportH_a_A,
      Dport_ref_H_b=eco_DportH_b_A,
      Dport_ref_L_a=eco_DportL_a_A,
      Dport_ref_L_b=eco_DportL_b_A),
    EXVECOA(
      selector_flowData=EXV_eco_A),
    EXVMainA(
      selector_flowData=EXV_main_A),
    BPHEECOB(
      selectGeo=Eco_Geo_CKB,
      nPlate=n_plate_crkB,
      Dport_ref_H_a=eco_DportH_a_B,
      Dport_ref_H_b=eco_DportH_b_B,
      Dport_ref_L_a=eco_DportL_a_B,
      Dport_ref_L_b=eco_DportL_b_B),
    EXVECOB(
      selector_flowData=EXV_eco_B),
    EXVMainB(
      selector_flowData=EXV_main_B),
    evaporator(
      selector_tube=evap_selector_tube,
      n_passes=n_passes_evap,
      Z_dpc_expression=1,
      T_a_start_coolant=sinkBrine_init+5,
      T_b_start_coolant=sinkBrine_init,
      userDefined_geo1(
        length_tube=length_tube_evap_crkA,
        diameter_in_shell=diameter_in_shell_evap,
        n_tubes_pass1=n_tubes_passe1_evap,
        n_tubes_pass2=n_tubes_passe2_evap,
        diameter_nozzle=diameter_nozzle),
      userDefined_geo2(
        length_tube=length_tube_evap_crkB,
        diameter_in_shell=diameter_in_shell_evap,
        n_tubes_pass1=n_tubes_passe1_evap,
        n_tubes_pass2=n_tubes_passe2_evap),
      R_foul=EvapFoulingFactor,
      cooler1(
        Vap_dTwi_start=
          if LWT > 283.15 then
            0.5
          else
            2.2),
      cooler2(
        Vap_dTwi_start=
          if LWT > 283.15 then
            0.5
          else
            2.2)),
    CompressorA(
      selector_comp_RefDep=Selector_comp_crkA,
      voltage=voltage_crkA),
    CompressorB(
      selector_comp_RefDep=Selector_comp_crkB,
      voltage=voltage_crkB),
    oilseparatorA(
      crossArea=.Modelica.Constants.pi*Di_oil_crkA*Di_oil_crkA/((4)),
      length=length_oil_crkA,
      Di=Di_oil_crkA,
      dp_reference=dp_ref_oil_crkA,
      m_flow_reference=m_flow_ref_oil_crkA),
    oilseparatorB(
      Di=Di_oil_crkB,
      crossArea=.Modelica.Constants.pi*Di_oil_crkB*Di_oil_crkB/((4)),
      perimeter=.Modelica.Constants.pi*Di_oil_crkB,
      length=length_oil_crkB,
      dp_reference=dp_ref_oil_crkB,
      m_flow_reference=m_flow_ref_oil_crkB),
    globalParameters(
      varLevel=.BOLT.InternalLibrary.BuildingBlocks.Types.Var_level.Advanced,
      Tsat_evap_start=sinkBrine_init-0.5,
      T_ambient=sourceAirA.Tdb_set),
    VFDA(
      selector_GenScrew=selector_VFDA_CKA),
    VFDB(
      selector_GenScrew=selector_VFDA_CKB),
    FanA(
      use_nStage_in=true,
      use_scalingFactor_in=false),
    FanB(
      use_nStage_in=true,
      use_scalingFactor_in=false),
    condAirA(
      nCoils=n_coilsA),
    condAirB(
      nCoils=n_coilsB),
    dischargelineA(
      dp_reference=dp_ref_DL_crkA,
      m_flow_reference=m_flow_ref_DL_crkA),
    dischargelineB(
      dp_reference=dp_ref_DL_crkB,
      m_flow_reference=m_flow_ref_DL_crkB),
    suctionlineA(
      m_flow_reference=m_flow_ref_SL_crkA,
      dp_reference=dp_ref_SL_crkA),
    suctionlineB(
      dp_reference=dp_ref_SL_crkB,
      m_flow_reference=m_flow_ref_SL_crkB),
    valve_92A(
      dp_reference=0),
    valve_92B(
      dp_reference=0),
    split(
      Fraction_b_start=fractionB_start),
    split2(
      Fraction_b_start=fractionB_start),
    nodecondAirinA(
      m_flow_set=5.1,
      p_set=1664934,
      x_set=1.1,
      Tsat_set=332,
      T_set=349),
    nodecondAiroutA(
      m_flow_set=5.1,
      T_set=313,
      Tsat_set=321,
      x_set=-0.03,
      p_set=1262616),
    nodeBPHEECOinA(
      m_flow_set=5.1,
      p_set=1262616,
      Tsat_set=320,
      T_set=310),
    nodeEXVECOoutA(
      T_set=297,
      Tsat_set=297,
      p_set=656831,
      m_flow_set=0.56,
      x_set=0.12),
    nodeEXVmaininA(
      T_set=299,
      Tsat_set=320,
      x_set=-0.1,
      p_set=1230342,
      m_flow_set=4.6),
    nodeBPHEECOoutA(
      T_set=305,
      Tsat_set=295,
      x_set=1.05,
      p_set=611935,
      m_flow_set=0.56),
    nodeCpECOA(
      T_set=304,
      Tsat_set=293,
      m_flow_set=0.56,
      x_set=1.06),
    nodeevapinA(
      T_set=280,
      Tsat_set=280,
      x_set=0.15,
      p_set=3.7e5,
      m_flow_set=4.6),
    nodeevapoutA(
      Tsat_set=280,
      T_set=280,
      p_set=3.7e5,
      m_flow_set=4.6),
    nodeCpinA(
      T_set=279,
      m_flow_set=4.6),
    nodeCpoutA(
      T_set=350,
      Tsat_set=333,
      x_set=1.1,
      p_set=1701299,
      m_flow_set=5.1),
    nodeOilsepoutA(
      m_flow_set=5.1,
      p_set=1701299,
      x_set=1.1,
      Tsat_set=333,
      T_set=350),
    nodeCpoutB(
      m_flow_set=5.1,
      p_set=1701299,
      x_set=1.1,
      Tsat_set=333,
      T_set=350),
    nodeOilsepoutB(
      Tsat_set=333,
      T_set=350,
      x_set=1.1,
      p_set=1701299,
      m_flow_set=5.1),
    nodecondAirinB(
      p_set=1664934,
      m_flow_set=5.1,
      x_set=1.1,
      Tsat_set=332,
      T_set=349),
    nodecondAiroutB(
      p_set=1262616,
      m_flow_set=5.1,
      x_set=-0.03,
      Tsat_set=321,
      T_set=313),
    nodeBPHEECOinB(
      m_flow_set=5.1,
      p_set=1262616,
      Tsat_set=321,
      T_set=313),
    nodeCpECOB(
      p_set=567000,
      m_flow_set=0.56,
      x_set=1.06,
      Tsat_set=293,
      T_set=304),
    nodeBPHEECOoutB(
      p_set=611935,
      m_flow_set=0.56,
      x_set=1.06,
      Tsat_set=293,
      T_set=304),
    nodeEXVECOoutB(
      p_set=656831,
      m_flow_set=0.56,
      x_set=0.12,
      Tsat_set=297,
      T_set=297),
    nodeEXVmaininB(
      p_set=1230342,
      m_flow_set=4.6,
      x_set=-0.1,
      Tsat_set=320,
      T_set=299),
    nodeevapinB(
      p_set=3.7e5,
      m_flow_set=4.6,
      x_set=0.15,
      Tsat_set=280,
      T_set=280),
    nodeevapoutB(
      p_set=3.7e5,
      m_flow_set=4.6,
      Tsat_set=280,
      T_set=280),
    nodeCpinB(
      m_flow_set=4.6,
      T_set=279));
  .BOLT.BoundaryNode.Coolant.Node NodeSinkEvap
    annotation (Placement(transformation(extent={{-4.0,-4.0},{4.0,4.0}},origin={-92.0,-26.0},rotation=-180.0)));
  .BOLT.BoundaryNode.Coolant.Node NodeSourceEvap
    annotation (Placement(transformation(extent={{-102.0,-86.0},{-94.0,-78.0}},origin={0.0,0.0},rotation=0.0)));
  import Selector_Comp=Public_database.Compressor.PD_3Port.ScrewMap.Polynomial10.VI.Selector;
  import Selector_VFD=BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector;
  import Selector_evap=Public_database.Evaporator.Tube.Selector;
  import Selector_eco_geo=BlackBoxLibrary.GeoSelector.GeoSelector_RefRef;
  import Selector_EXV=BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector;
  parameter Real OAT
    annotation (Dialog(tab="Boundary Conditions"));
  parameter Real LWT
    annotation (Dialog(tab="Boundary Conditions"));
  parameter Real relative_humidity
    annotation (Dialog(tab="Boundary Conditions"));
  parameter Real capacity_design
    annotation (Dialog(tab="Boundary Conditions"));
  parameter Real sinkBrine_init
    annotation (Dialog(tab="Boundary Conditions"));
  parameter Real sourceBrine_init
    annotation (Dialog(tab="Boundary Conditions"));
  parameter Real fractionB_start=max(
    0.02,
    min(
      0.1,
      6.25e-3*(OAT-LWT)-0.075))
    annotation (Dialog(group="Split ECO BPHE ",tab="Initialization"));
  parameter Real EWT_init
    annotation (Dialog(group="Pump ",tab="Initialization"));
  parameter Real mdot_start
    annotation (Dialog(group="Pump ",tab="Initialization"));
  parameter Boolean Vi_A
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Boolean Vi_B
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.Public_database.Compressor.PD_3Port.ScrewMap.Polynomial10.VI.Selector Selector_comp_crkA
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.Public_database.Compressor.PD_3Port.ScrewMap.Polynomial10.VI.Selector Selector_comp_crkB
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.Public_database.Compressor.PD_3Port.RefIndMap.Polynomial4.VI.Selector_VS_VI Selector_comp_crkA_refInd
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.Public_database.Compressor.PD_3Port.RefIndMap.Polynomial4.VI.Selector_VS_VI Selector_comp_crkB_refInd
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.Workspace.Controller.Components.Types.CompressorSelector CompType_CKA
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.Workspace.Controller.Components.Types.CompressorSelector CompType_CKB
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector selector_VFDA_CKA
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector selector_VFDA_CKB
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.Public_database.Evaporator.Tube.Selector evap_selector_tube
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter Boolean longEvap
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter Real EvapFoulingFactor
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter.BlackBoxLibrary.GeoSelector.GeoSelector_RefRef Eco_Geo_CKA
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter.BlackBoxLibrary.GeoSelector.GeoSelector_RefRef Eco_Geo_CKB
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter Real eco_DportH_a_A
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter Real eco_DportH_b_A
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter Real eco_DportL_a_A
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter Real eco_DportL_b_A
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter Real eco_DportH_a_B
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter Real eco_DportH_b_B
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter Real eco_DportL_a_B
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter Real eco_DportL_b_B
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter Real length_tube_evap_crkA
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter Real length_tube_evap_crkB
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter Real diameter_in_shell_evap
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter Integer n_tubes_passe1_evap
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter Integer n_tubes_passe2_evap
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter Real diameter_nozzle
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter Integer n_plate_crkA
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter Integer n_plate_crkB
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter Real voltage_crkA
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real voltage_crkB
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));    
  parameter Boolean is_fixedSpeed
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector EXV_main_A
    annotation (Dialog(group="EXV",tab="Unit Characteristics"));
  parameter.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector EXV_main_B
    annotation (Dialog(group="EXV",tab="Unit Characteristics"));
  parameter.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector EXV_eco_A
    annotation (Dialog(group="EXV",tab="Unit Characteristics"));
  parameter.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector EXV_eco_B
    annotation (Dialog(group="EXV",tab="Unit Characteristics"));
  parameter Boolean isUserPumpPresent=false
    annotation (Dialog(tab="Unit Characteristics",group="Hidronic kit"));
  parameter Real efficieny_userPumpFictitious
    "Efficiency of a fictitious user pump, used only when isUserPumpPresent is False"
    annotation (Dialog(tab="Unit Characteristics",group="Hidronic kit"));
  parameter Integer n_coilsA
    annotation (Dialog(group="CondAir",tab="Unit Characteristics"));
  parameter Integer n_coilsB
    annotation (Dialog(group="CondAir",tab="Unit Characteristics"));
  parameter Boolean isCoating
    annotation (Dialog(group="CondAir",tab="Unit Characteristics"));
  parameter Real Di_oil_crkA
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real Di_oil_crkB
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real length_oil_crkA
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real length_oil_crkB
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real dp_ref_oil_crkA
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real dp_ref_oil_crkB
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real m_flow_ref_oil_crkA
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real m_flow_ref_oil_crkB
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real dp_ref_SL_crkA
    annotation (Dialog(group="SuctionLine",tab="Unit Characteristics"));
  parameter Real dp_ref_SL_crkB
    annotation (Dialog(group="SuctionLine",tab="Unit Characteristics"));
  parameter Real m_flow_ref_SL_crkA
    annotation (Dialog(group="SuctionLine",tab="Unit Characteristics"));
  parameter Real m_flow_ref_SL_crkB
    annotation (Dialog(group="SuctionLine",tab="Unit Characteristics"));
  parameter Real dp_ref_DL_crkA
    annotation (Dialog(group="DischargeLine",tab="Unit Characteristics"));
  parameter Real dp_ref_DL_crkB
    annotation (Dialog(group="DischargeLine",tab="Unit Characteristics"));
  parameter Real m_flow_ref_DL_crkA
    annotation (Dialog(group="DischargeLine",tab="Unit Characteristics"));
  parameter Real m_flow_ref_DL_crkB
    annotation (Dialog(group="DischargeLine",tab="Unit Characteristics"));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_b port_b
    annotation (Placement(transformation(extent={{-218.0,-20.0},{-198.0,0.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={180,-40})));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.Ports.FluidPort_a port_a
    annotation (Placement(transformation(extent={{-558.5,-158.25},{-538.5,-138.25}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-50,-40})));
  .BOLT.CoolantMisc.ReducedPipe pipeDuplex(
    Kb=1.84005,
    Ka_set=0.0013)
    annotation (Placement(transformation(extent={{-102.0,-36.0},{-122.0,-16.0}},origin={0.0,0.0},rotation=0.0)));
  parameter.BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector selector_PumpUser
    "User pump Model"
    annotation (Dialog(tab="Unit Characteristics",group="Hidronic kit"));
  .BOLT.CoolantMisc.ReducedPipe External_System(
    Ka_set=0.0013,
    Kb=2,
    use_Ka_in=true,
    isOff=not isUserPumpPresent)
    annotation (Placement(transformation(extent={{-142.0,-36.0},{-162.0,-16.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.PumpPoly pumpUser(
    selector=selector_PumpUser,
    isOff=not isUserPumpPresent,
    T_start=EWT_init,
    k_pow=1)
    annotation (Placement(transformation(extent={{-492.0,-108.0},{-452.0,-68.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.InternalLibrary.Mechanicals.Shaft.Source shaftPumpUser(
    use_speed_in=true,
    isOff=not isUserPumpPresent)
    annotation (Placement(transformation(extent={{-524.0,-50.0},{-516.0,-42.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.CoolantMisc.Mixer mixerPumpUser(
    isOff_a=not isUserPumpPresent,
    isOff_b=isUserPumpPresent,
    T_start=EWT_init,
    fa_set=
      if isUserPumpPresent then
        1.0
      else
        0.0)
    annotation (Placement(transformation(extent={{-186,-98},{-166,-78}},origin={0,0},rotation=0)));
  .BOLT.InternalLibrary.BuildingBlocks.Coolant.FlowAdapters.Split splitUserPump(
    isOff_a=not isUserPumpPresent,
    isOff_b=isUserPumpPresent,
    T_start=EWT_init,
    mDot_b_start=
      if not isUserPumpPresent then
        mdot_start
      else
        0,
    mDot_a_start=
      if isUserPumpPresent then
        mdot_start
      else
        0,
    fa_set=
      if isUserPumpPresent then
        1.0
      else
        0.0)
    annotation (Placement(transformation(extent={{-478,-158},{-458,-138}},origin={0,0},rotation=0)));
equation
  connect(NodeSourceEvap.port_b,evaporator.port_a_coolant)
    annotation (Line(points={{-94,-82},{-56.319028686354926,-82},{-56.319028686354926,-50.210367706409286}},color={0,127,0}));
  connect(NodeSinkEvap.port_a,evaporator.port_b_coolant)
    annotation (Line(points={{-88,-26},{-55.90826110524182,-26},{-55.90826110524182,-37.065805110790045}},color={0,127,0}));
  connect(pipeDuplex.port_a,NodeSinkEvap.port_b)
    annotation (Line(points={{-102,-26},{-96,-26}},color={0,127,0}));
  connect(pipeDuplex.port_b,External_System.port_a)
    annotation (Line(points={{-122,-26},{-142,-26}},color={0,127,0}));
  connect(External_System.port_b,port_b)
    annotation (Line(points={{-162,-26},{-185,-26},{-185,-10},{-208,-10}},color={0,127,0}));
  connect(shaftPumpUser.flange,pumpUser.flange)
    annotation (Line(points={{-516,-46},{-472,-46},{-472,-68}},color={127,0,0}));
  connect(pumpUser.port_b,mixerPumpUser.port_a)
    annotation (Line(points={{-452,-88},{-319,-88},{-319,-83.2},{-186,-83.2}},color={0,127,0}));
  connect(splitUserPump.port_c,port_a)
    annotation (Line(points={{-478.2,-148.2},{-513.35,-148.2},{-513.35,-148.25},{-548.5,-148.25}},color={0,127,0}));
  connect(splitUserPump.port_a,pumpUser.port_a)
    annotation (Line(points={{-458,-142.8},{-452,-142.8},{-452,-115.4},{-498,-115.4},{-498,-88},{-492,-88}},color={0,127,0}));
  connect(splitUserPump.port_b,mixerPumpUser.port_b)
    annotation (Line(points={{-458,-153.2},{-322,-153.2},{-322,-93.2},{-186,-93.2}},color={0,127,0}));
  connect(NodeSourceEvap.port_a,mixerPumpUser.port_c)
    annotation (Line(points={{-102,-82},{-133.8,-82},{-133.8,-88},{-165.6,-88}},color={0,127,0}));
  annotation (
    Icon(
      graphics={
        Rectangle(
          origin={1,-2},
          extent={{-107,102},{107,-102}},
          fillPattern=FillPattern.Solid,
          fillColor={150,46,59})}));
end BaseCycle_OL_Module;
