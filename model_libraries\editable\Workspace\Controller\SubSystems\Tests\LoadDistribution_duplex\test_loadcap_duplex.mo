within Workspace.Controller.SubSystems.Tests.LoadDistribution_duplex;
model test_loadcap_duplex
  .Modelica.Blocks.Sources.RealExpression moduleA_min(
    y=min_compressor_speed_moduleA_crkA)
    annotation (Placement(transformation(extent={{143.17933406980026,133.2266176990513},{158.49196482540938,148.53924845466042}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Gain gainA(
    k=max_compressor_speed_moduleA_crkA-min_compressor_speed_moduleA_crkA)
    annotation (Placement(transformation(extent={{141.05353259088008,115.05353259088011},{158.94646740911992,132.9464674091199}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Add add_1
    annotation (Placement(transformation(extent={{180.36715161913665,128.688370938837},{195.67978237474577,144.00100169444613}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput compressorFrequency_moduleA_crkA
    annotation (Placement(transformation(extent={{218.4299766402221,128.6875164393529},{233.74260739583121,144.000147194962}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Gain gainB(
    k=max_compressor_speed_moduleA_crkB-min_compressor_speed_moduleA_crkB)
    annotation (Placement(transformation(extent={{142.49074967531237,50.40354288999055},{160.3836844935522,68.29647770823034}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression moduleB_min(
    y=min_compressor_speed_moduleA_crkB)
    annotation (Placement(transformation(extent={{145.09341291425142,28.33509702312895},{160.40604366986054,43.647727778738044}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Add add_2
    annotation (Placement(transformation(extent={{183.37498980327416,32.92888624981167},{198.68762055888328,48.241517005420775}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal_moduleA_crkA
    annotation (Placement(transformation(extent={{398.34368462219544,130.34368462219544},{413.65631537780456,145.65631537780456}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{300.25507452432606,89.32493398569181},{331.8956088571186,120.96546831848423}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression totalCapacity_moduleA_crkA(
    y=300000)
    annotation (Placement(transformation(extent={{220.73317657792023,90.73317657792022},{251.26682342207977,121.26682342207978}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController(
    AV_min=min_compressor_speed_moduleA_crkA,
    AV_max=max_compressor_speed_moduleA_crkA,
    AV_start=1)
    annotation (Placement(transformation(extent={{328.8996699485449,131.7508970899588},{344.212300704154,147.06352784556793}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation compressorFrequency_error(
    ID=1,
    measurement=actuatorSignal_moduleA_crkA,
    gain=1/((max_compressor_speed_moduleA_crkA)),
    setPoint=compressorFrequency_moduleA_crkA)
    annotation (Placement(transformation(extent={{251.03482362670206,130.34368462219544},{292.96517637329794,145.65631537780456}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController2(
    AV_start=0,
    AV_max=1,
    AV_min=0)
    annotation (Placement(transformation(extent={{102.34368462219545,94.34368462219545},{117.65631537780455,109.65631537780455}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation compressorFrequency_error2(
    setPoint=loadDistribution_capa.SetPoint_capacity_moduleA,
    gain=1/(500000),
    measurement=totalCapacity_moduleA_crkA.y+totalCapacity_moduleA_crkB.y,
    ID=1)
    annotation (Placement(transformation(extent={{25.034823626702092,68.34368462219545},{66.96517637329791,83.65631537780455}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput compressorFrequency_moduleA_crkB
    annotation (Placement(transformation(extent={{220.1799915837203,32.218797178499685},{235.49262233932942,47.53142793410879}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController3(
    AV_min=min_compressor_speed_moduleA_crkB,
    AV_max=max_compressor_speed_moduleA_crkB,
    AV_start=1)
    annotation (Placement(transformation(extent={{339.31498110782684,35.85027263236327},{354.62761186343596,51.16290338797238}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal_moduleA_crkB
    annotation (Placement(transformation(extent={{398.72390507138863,34.99684867825064},{414.03653582699775,50.30947943385975}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation compressorFrequency_error3(
    ID=1,
    measurement=actuatorSignal_moduleA_crkB,
    gain=1/((max_compressor_speed_moduleA_crkB)),
    setPoint=compressorFrequency_moduleA_crkB)
    annotation (Placement(transformation(extent={{258.3202088043397,34.99684867825064},{300.2505615509356,50.30947943385975}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBusB
    annotation (Placement(transformation(extent={{301.0207060621065,62.5278301633759},{332.66124039489904,94.1683644961683}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression totalCapacity_moduleA_crkB(
    y=300000)
    annotation (Placement(transformation(extent={{220.22579891724965,61.35860294768629},{250.75944576140918,91.89224979184586}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.SubSystems.BaseClasses.LoadDistribution_DesignCapa_duplex loadDistribution_capa(
    capacityDesign_moduleA_max=1000000,
    capacityDesign_moduleA_min=200000,
    capacityDesign_moduleB_max=900000,
    capacityDesign_moduleB_min=200000)
    annotation (Placement(transformation(extent={{-73.69104038579161,-27.69104038579161},{-10.30895961420839,35.69104038579161}},origin={0.0,0.0},rotation=0.0)));
  parameter Real max_compressor_speed_moduleA_crkA=90
    "Maximum speed of compressor module A (Hz)"
    annotation (Dialog(group="compressor speed module A"));
  parameter Real min_compressor_speed_moduleA_crkA=20
    "Minimum speed of compressor module A (Hz)"
    annotation (Dialog(group="compressor speed module A"));
  parameter Real max_compressor_speed_moduleA_crkB=90
    "Minimum speed of compressor module B (Hz)"
    annotation (Dialog(group="compressor speed module A"));
  parameter Real min_compressor_speed_moduleA_crkB=20
    "Maximum speed of compressor module B (Hz)"
    annotation (Dialog(group="compressor speed module A"));
  parameter Real max_compressor_speed_moduleB_crkA=90
    "Maximum speed of compressor module A (Hz)"
    annotation (Dialog(group="compressor speed module B"));
  parameter Real min_compressor_speed_moduleB_crkA=20
    "Minimum speed of compressor module A (Hz)"
    annotation (Dialog(group="compressor speed module B"));
  parameter Real max_compressor_speed_moduleB_crkB=90
    "Minimum speed of compressor module B (Hz)"
    annotation (Dialog(group="compressor speed module B"));
  parameter Real min_compressor_speed_moduleB_crkB=20
    "Maximum speed of compressor module B (Hz)"
    annotation (Dialog(group="compressor speed module B"));
  .Modelica.Blocks.Interfaces.RealOutput compressorFrequency_moduleB_crkA
    annotation (Placement(transformation(extent={{225.14988710627057,-49.39808563746146},{240.4625178618797,-34.08545488185234}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Add add_
    annotation (Placement(transformation(extent={{190.09490026932264,-145.1567158270027},{205.40753102493176,-129.84408507139358}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus2
    annotation (Placement(transformation(extent={{306.97498499037454,-88.76066809112254},{338.61551932316706,-57.12013375833013}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput compressorFrequency_moduleB_crkB
    annotation (Placement(transformation(extent={{226.89990204976877,-145.86680489831468},{242.2125328053779,-130.55417414270556}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal_moduleB_crkB
    annotation (Placement(transformation(extent={{405.4438155374371,-143.0887533985637},{420.7564462930462,-127.77612264295459}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBusB2
    annotation (Placement(transformation(extent={{307.740616528155,-115.55777191343844},{339.3811508609475,-83.91723758064606}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal_moduleB_crkA
    annotation (Placement(transformation(extent={{405.0635950882439,-47.741917454618914},{420.37622584385304,-32.42928669900979}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation compressorFrequency_error4(
    ID=1,
    measurement=totalCapacity_moduleB_crkA.y+totalCapacity_moduleB_crkB.y,
    gain=1/(500000),
    setPoint=loadDistribution_capa.SetPoint_capacity_moduleB)
    annotation (Placement(transformation(extent={{31.754734092750567,-109.7419174546189},{73.68508683934638,-94.42928669900981}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression moduleB_min2(
    y=min_compressor_speed_moduleB_crkB)
    annotation (Placement(transformation(extent={{151.8133233802999,-149.7505050536854},{167.125954135909,-134.43787429807628}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Gain gainB2(
    k=max_compressor_speed_moduleA_crkB-min_compressor_speed_moduleA_crkB)
    annotation (Placement(transformation(extent={{149.21066014136085,-127.6820591868238},{167.10359495960068,-109.78912436858403}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation compressorFrequency_error5(
    setPoint=compressorFrequency_moduleB_crkB,
    gain=1/(max_compressor_speed_moduleB_crkB),
    measurement=actuatorSignal_moduleB_crkB,
    ID=1)
    annotation (Placement(transformation(extent={{265.0401192703882,-143.0887533985637},{306.97047201698405,-127.77612264295459}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController4(
    AV_start=1,
    AV_max=max_compressor_speed_moduleB_crkB,
    AV_min=min_compressor_speed_moduleB_crkB)
    annotation (Placement(transformation(extent={{346.0348915738753,-142.23532944445108},{361.34752232948443,-126.92269868884196}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression totalCapacity_moduleB_crkB(
    y=300000)
    annotation (Placement(transformation(extent={{226.73317657792023,-117.26682342207978},{257.26682342207977,-86.73317657792022}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression totalCapacity_moduleB_crkA(
    y=300000)
    annotation (Placement(transformation(extent={{226.73317657792023,-87.26682342207977},{257.26682342207977,-56.73317657792023}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController5(
    AV_start=1,
    AV_max=max_compressor_speed_moduleB_crkA,
    AV_min=min_compressor_speed_moduleB_crkA)
    annotation (Placement(transformation(extent={{335.6195804145934,-46.33470498685554},{350.9322111702025,-31.02207423124642}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation compressorFrequency_error6(
    setPoint=compressorFrequency_moduleB_crkA,
    gain=1/(max_compressor_speed_moduleB_crkA),
    measurement=actuatorSignal_moduleB_crkA,
    ID=1)
    annotation (Placement(transformation(extent={{257.75473409275054,-47.741917454618914},{299.6850868393464,-32.42928669900979}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Add add_3
    annotation (Placement(transformation(extent={{187.08706208518512,-49.39723113797734},{202.39969284079424,-34.08460038236822}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Gain gainA2(
    k=max_compressor_speed_moduleB_crkA-min_compressor_speed_moduleB_crkA)
    annotation (Placement(transformation(extent={{147.77344305692856,-63.032069485934244},{165.6663778751684,-45.139134667694464}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression moduleA_min2(
    y=min_compressor_speed_moduleB_crkA)
    annotation (Placement(transformation(extent={{149.89924453584874,-44.85898437776305},{165.21187529145786,-29.54635362215393}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController6(
    AV_min=0,
    AV_max=1,
    AV_start=0)
    annotation (Placement(transformation(extent={{109.06359508824391,-83.7419174546189},{124.37622584385304,-68.42928669900981}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(compressorFrequency_error.sensor,setpointController.errorSignal)
    annotation (Line(points={{292.545872845832,137.38749476977563},{328.8996699485449,137.38749476977563},{328.8996699485449,139.40721246776343}},color={28,108,200}));
  connect(compressorFrequency_error2.sensor,setpointController2.errorSignal)
    annotation (Line(points={{66.54587284583195,75.38749476977564},{94.93614581949576,75.38749476977564},{94.93614581949576,102},{102.34368462219545,102}},color={28,108,200}));
  connect(compressorFrequency_error3.sensor,setpointController3.errorSignal)
    annotation (Line(points={{299.8312580234697,42.04065882583083},{339.31498110782684,42.04065882583083},{339.31498110782684,43.506588010167846}},color={28,108,200}));
  connect(actuatorSignal_moduleA_crkA,measurementBus.compressorFrequency)
    annotation (Line(points={{406,138},{416.1816652555168,138},{416.1816652555168,105.14520115208803},{316.0753416907223,105.14520115208803}},color={0,0,127}));
  connect(setpointController3.actuatorSignal,actuatorSignal_moduleA_crkB)
    annotation (Line(points={{355.08699078610425,43.506588010167846},{406.3802204491932,43.506588010167846},{406.3802204491932,42.65316405605522}},color={0,0,127}));
  connect(actuatorSignal_moduleA_crkB,measurementBusB.compressorFrequency)
    annotation (Line(points={{406.3802204491932,42.65316405605522},{420.7754544821995,42.65316405605522},{420.7754544821995,78.34809732977213},{316.8409732285028,78.34809732977213}},color={0,0,127}));
  connect(gainA.y,add_1.u2)
    annotation (Line(points={{159.8411141500319,124},{171.06883727822986,124},{171.06883727822986,131.75089708995887},{178.83588854357575,131.75089708995887}},color={0,0,127}));
  connect(moduleA_min.y,add_1.u1)
    annotation (Line(points={{159.25759636318983,140.88293307685586},{178.83588854357575,140.88293307685586},{178.83588854357575,140.93847554332433}},color={0,0,127}));
  connect(add_1.y,compressorFrequency_moduleA_crkA)
    annotation (Line(points={{196.44541391252622,136.34468631664157},{226.08629201802665,136.34468631664157},{226.08629201802665,136.34383181715748}},color={0,0,127}));
  connect(gainB.y,add_2.u1)
    annotation (Line(points={{161.27833123446422,59.35001029911045},{172.21728458490054,59.35001029911045},{172.21728458490054,45.178990854299016},{181.84372672771326,45.178990854299016}},color={0,0,127}));
  connect(moduleB_min.y,add_2.u2)
    annotation (Line(points={{161.171675207641,35.991412400933555},{181.84372672771326,35.991412400933555}},color={0,0,127}));
  connect(add_2.y,compressorFrequency_moduleA_crkB)
    annotation (Line(points={{199.45325209666373,40.585201627616286},{227.83630696152488,40.585201627616286},{227.83630696152488,39.87511255630426}},color={0,0,127}));
  connect(setpointController.actuatorSignal,actuatorSignal_moduleA_crkA)
    annotation (Line(points={{344.6716796268223,139.40721246776337},{406,139.40721246776337},{406,138}},color={0,0,127}));
  connect(setpointController2.actuatorSignal,gainA.u)
    annotation (Line(points={{118.11569430047282,102},{128.68996670476446,102},{128.68996670476446,124},{139.2642391090561,124}},color={0,0,127}));
  connect(setpointController2.actuatorSignal,gainB.u)
    annotation (Line(points={{118.11569430047282,102},{129.4085752469806,102},{129.4085752469806,59.35001029911044},{140.70145619348838,59.35001029911044}},color={0,0,127}));
  connect(totalCapacity_moduleA_crkA.y,measurementBus.capacity)
    annotation (Line(points={{252.79350576428774,106},{316.0753416907223,106},{316.0753416907223,105.14520115208803}},color={0,0,127}));
  connect(totalCapacity_moduleA_crkB.y,measurementBusB.capacity)
    annotation (Line(points={{252.79350576428774,76},{316.8409732285028,76},{316.8409732285028,78.34809732977213}},color={0,0,127}));
  connect(gainA2.y,add_3.u2)
    annotation (Line(points={{166.56102461608037,-54.085602076814354},{177.78874774427834,-54.085602076814354},{177.78874774427834,-46.334704986855485},{185.55579900962422,-46.334704986855485}},color={0,0,127}));
  connect(moduleA_min2.y,add_3.u1)
    annotation (Line(points={{165.9775068292383,-37.20266899995849},{185.55579900962422,-37.20266899995849},{185.55579900962422,-37.147126533490024}},color={0,0,127}));
  connect(gainB2.y,add_.u1)
    annotation (Line(points={{167.9982417005127,-118.7355917777039},{178.93719505094901,-118.7355917777039},{178.93719505094901,-132.90661122251532},{188.56363719376174,-132.90661122251532}},color={0,0,127}));
  connect(moduleB_min2.y,add_.u2)
    annotation (Line(points={{167.89158567368946,-142.09418967588078},{188.56363719376174,-142.09418967588078}},color={0,0,127}));
  connect(compressorFrequency_error6.sensor,setpointController5.errorSignal)
    annotation (Line(points={{299.26578331188045,-40.698107307038725},{335.6195804145934,-40.698107307038725},{335.6195804145934,-38.678389609050924}},color={28,108,200}));
  connect(compressorFrequency_error4.sensor,setpointController6.errorSignal)
    annotation (Line(points={{73.26578331188043,-102.69810730703871},{101.65605628554424,-102.69810730703871},{101.65605628554424,-76.08560207681435},{109.06359508824393,-76.08560207681435}},color={28,108,200}));
  connect(compressorFrequency_error5.sensor,setpointController4.errorSignal)
    annotation (Line(points={{306.55116848951815,-136.04494325098352},{346.0348915738753,-136.04494325098352},{346.0348915738753,-134.5790140666465}},color={28,108,200}));
  connect(setpointController6.actuatorSignal,gainA2.u)
    annotation (Line(points={{124.8356047665213,-76.08560207681435},{135.40987717081293,-76.08560207681435},{135.40987717081293,-54.085602076814354},{145.98414957510457,-54.085602076814354}},color={0,0,127}));
  connect(setpointController6.actuatorSignal,gainB2.u)
    annotation (Line(points={{124.8356047665213,-76.08560207681435},{136.12848571302908,-76.08560207681435},{136.12848571302908,-118.73559177770392},{147.42136665953686,-118.73559177770392}},color={0,0,127}));
  connect(totalCapacity_moduleB_crkA.y,measurementBus2.capacity)
    annotation (Line(points={{258.7935057642877,-72},{322.7952521567708,-72},{322.7952521567708,-72.94040092472632}},color={0,0,127}));
  connect(totalCapacity_moduleB_crkB.y,measurementBusB2.capacity)
    annotation (Line(points={{258.7935057642877,-102},{323.56088369455125,-102},{323.56088369455125,-99.73750474704222}},color={0,0,127}));
  connect(add_3.y,compressorFrequency_moduleB_crkA)
    annotation (Line(points={{203.1653243785747,-41.74091576017278},{232.80620248407513,-41.74091576017278},{232.80620248407513,-41.741770259656874}},color={0,0,127}));
  connect(add_.y,compressorFrequency_moduleB_crkB)
    annotation (Line(points={{206.1731625627122,-137.50040044919808},{234.55621742757336,-137.50040044919808},{234.55621742757336,-138.2104895205101}},color={0,0,127}));
  connect(actuatorSignal_moduleB_crkA,measurementBus2.compressorFrequency)
    annotation (Line(points={{412.7199104660485,-40.085602076814354},{422.9015757215653,-40.085602076814354},{422.9015757215653,-72.94040092472632},{322.7952521567708,-72.94040092472632}},color={0,0,127}));
  connect(setpointController5.actuatorSignal,actuatorSignal_moduleB_crkA)
    annotation (Line(points={{351.3915900928708,-38.67838960905098},{412.7199104660485,-38.67838960905098},{412.7199104660485,-40.085602076814354}},color={0,0,127}));
  connect(setpointController4.actuatorSignal,actuatorSignal_moduleB_crkB)
    annotation (Line(points={{361.8069012521527,-134.5790140666465},{413.10013091524166,-134.5790140666465},{413.10013091524166,-135.43243802075915}},color={0,0,127}));
  connect(actuatorSignal_moduleB_crkB,measurementBusB2.compressorFrequency)
    annotation (Line(points={{413.10013091524166,-135.43243802075915},{427.495364948248,-135.43243802075915},{427.495364948248,-99.73750474704222},{323.56088369455125,-99.73750474704222}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end test_loadcap_duplex;
