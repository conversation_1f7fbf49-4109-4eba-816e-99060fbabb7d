within KAV_initiation.System30KAV.Controller30KAV.StateMachine;
function isVIOn
  input Modelica.SIunits.Frequency cmpr_spd;
  input Modelica.SIunits.Frequency cmpr_spd_max;
  input Modelica.SIunits.Frequency cmpr_spd_min;
  input Modelica.SIunits.Temperature Tsat_dis;
  input Modelica.SIunits.Temperature Tsat_suc;
  output Real cmd_Vi;
protected
  Modelica.SIunits.Conversions.NonSIunits.Temperature_degF sdt_50;
  Modelica.SIunits.Conversions.NonSIunits.Temperature_degF sdtMaxfreq;
  Modelica.SIunits.Conversions.NonSIunits.Temperature_degF sdtMinFreq;
  Modelica.SIunits.Conversions.NonSIunits.Temperature_degF csdt;
  Modelica.SIunits.Conversions.NonSIunits.Temperature_degF sst_degF;
  Modelica.SIunits.Conversions.NonSIunits.Temperature_degF sdt_degF;
algorithm
  // switch cmprRef
  //     case CmprRefs.NG0
  //         %To be updated
  //         viGuidanceMaxFreq =  95;
  //         viGuidanceMinFreq =  20;
  //     case {CmprRefs.NG1, CmprRefs.NG1_460V}
  //         viGuidanceMaxFreq = 105;
  //         viGuidanceMinFreq =  19;
  //     case CmprRefs.NG2
  //         viGuidanceMaxFreq =  98;
  //         viGuidanceMinFreq =  17;
  //     case CmprRefs.NG3_460V
  //         %To be updated
  //         viGuidanceMaxFreq =  95;
  //         viGuidanceMinFreq =  20;
  //     case CmprRefs.NG3
  //         %To be updated
  //         viGuidanceMaxFreq =  95;
  //         viGuidanceMinFreq =  20;
  //     otherwise
  //         viGuidanceMaxFreq =  95;
  //         viGuidanceMinFreq =  20;
  // end
  // % VI Guide Curve Equations
  // % ----------------------------------------------
  // %|    Max Hz     |      50 Hz   |      Min Hz   |
  // %-----------------------------------------------
  // %| SSF Â°F |SDTÂ°F |SSF Â°F |SDTÂ°F | SSF Â°F |SDTÂ°F |
  // %|  -10     59.8   -10     52.5    -10     42.9   |
  // %|  62.6    134   62.6     123     62.6    113  |
  // %------------------------------------------------
  sst_degF := Modelica.SIunits.Conversions.to_degF(
    Tsat_suc);
  sdt_degF := Modelica.SIunits.Conversions.to_degF(
    Tsat_dis);
  sdtMaxfreq := 0.8709*sst_degF+68.509;
  sdt_50 := 0.8275*sst_degF+60.775;
  sdtMinFreq := 0.8228*sst_degF+51.128;
  //% Interpolation
  if cmpr_spd == cmpr_spd_max then
    csdt := sdtMaxfreq;
  elseif cmpr_spd == 50 then
    csdt := sdt_50;
  elseif cmpr_spd == cmpr_spd_min then
    csdt := sdtMinFreq;
  elseif cmpr_spd > cmpr_spd_min and cmpr_spd <= 50 then
    csdt := sdtMinFreq+(sdt_50-sdtMinFreq)*(cmpr_spd-cmpr_spd_min)/(50-cmpr_spd_min);
  elseif cmpr_spd > 50 and cmpr_spd <= cmpr_spd_max then
    csdt := sdt_50+(sdtMaxfreq-sdt_50)*(cmpr_spd-50)/(cmpr_spd_max-50);
  elseif cmpr_spd < cmpr_spd_min then
    csdt := sdt_50+(sdtMinFreq-sdt_50)*(cmpr_spd-50)/(cmpr_spd_min-50);
  elseif cmpr_spd > cmpr_spd_max then
    csdt := sdt_50+(sdtMaxfreq-sdt_50)*(cmpr_spd-50)/(cmpr_spd_max-50);
  else
    csdt := 0;
  end if;
  //%Apply offest (lab test only)
  //%sst is read on the evaporator so we need to add an offset of 1K to set
  //%correct position for the VI
  //%be more accurate.
  //csdt :=csdt + 1.8;
  //% Inside deadband, no change
  //if abs(sdt-csdt)<VI_deadbandF then
  //    cmd_Vi:=cur_cmd_Vi;
  //% Above deadband, High VI
  //if sdt>(csdt+VI_deadbandF) then
  if sdt_degF > csdt then
    cmd_Vi := 1;
  //% Below deadband, Low VI
  //elseif sdt<(csdt-VI_deadbandF) then
  elseif sdt_degF < csdt then
    cmd_Vi := 0;
  else
    cmd_Vi := 0;
  end if;
end isVIOn;
