within Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM;
record LayerBase
  extends Modelica.Icons.Record;
  parameter Integer LayerNumber=4
    "LayerNumber";
  parameter Real[150,7] W0=zeros(
    size(
      W0,
      1),
    size(
      W0,
      2));
  parameter Real[150] B0=zeros(
    size(
      B0,
      1));
  parameter Real[70,150] W1=zeros(
    size(
      W1,
      1),
    size(
      W1,
      2));
  parameter Real[70] B1=zeros(
    size(
      B1,
      1));
  parameter Real[10,70] W2=zeros(
    size(
      W2,
      1),
    size(
      W2,
      2));
  parameter Real[10] B2=zeros(
    size(
      B2,
      1));
  parameter Real[1,10] W3=zeros(
    size(
      W3,
      1),
    size(
      W3,
      2));
  parameter Real[1] B3=zeros(
    size(
      B3,
      1));
  parameter Integer[4,1] RowNumber=[
    size(
      W0,
      1);
    size(
      W1,
      1);
    size(
      W2,
      1);
    size(
      W3,
      1)];
  parameter Integer[4,1] ColumnNumber=[
    [
      size(
        W0,
        2);
      size(
        W1,
        2);
      size(
        W2,
        2);
      size(
        W3,
        2)]];
  parameter Integer MaxRow=max(
    RowNumber)
    "MaxRowNumber of W";
  parameter Integer MaxColumn=max(
    ColumnNumber)
    "MaxColumnNumber of W";
  parameter SurrogateInput Mean;
  parameter SurrogateInput Std;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false)),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)));
end LayerBase;
