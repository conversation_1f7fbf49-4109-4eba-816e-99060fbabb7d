within StateMachine;
record CrkABCDVICDEcoAB_9
  import BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod;
  extends Mode30XBVBase(
    final modeID=ModeID30XBV.CrkABCDVICDEcoAB_9,
    final CrkA=true,
    final CrkB=true,
    final CrkC=true,
    final CrkD=true,
    final EcoA=true,
    final EcoB=true,
    final EcoC=false,
    final EcoD=false,
    final ViA=false,
    final ViB=false,
    final ViC=true,
    final ViD=true,
    initMethod=ModeInitMethod.External);
end CrkABCDVICDEcoAB_9;
