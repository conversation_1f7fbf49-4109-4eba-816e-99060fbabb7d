within Workspace.Controller;
model CL_XBV_System_duplex
  extends.Workspace.Controller.StateMachine_duplex.StateMachine30XBV;
  .Workspace.Controller.ControllerSettings controllerSettings_moduleA_crkA
    annotation (Placement(transformation(extent={{-79.0,39.0},{-59.0,59.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings controllerSettings_moduleA_crkB
    annotation (Placement(transformation(extent={{-78.0,-64.0},{-58.0,-44.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings controllerSettings_moduleB_crkA
    annotation (Placement(transformation(extent={{19.0,37.0},{39.0,57.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.ControllerSettings controllerSettings_moduleB_crkB
    annotation (Placement(transformation(extent={{22.0,-64.0},{42.0,-44.0}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.Controller_Duplex Controller_Duplex
    constrainedby.Workspace.Controller.SubSystems.BaseClasses.ControllerBase_Duplex
    annotation (Placement(transformation(extent={{-34.77989622182537,1.2201037781746322},{-5.220103778174632,30.779896221825368}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(Controller_Duplex.limitsBus_moduleA_crkA,controllerSettings_moduleA_crkA.limitsBus)
    annotation (Line(points={{-24.877365753202373,33.14467961731743},{-30,33.14467961731743},{-30,47},{-57.2,47}},color={255,204,51}));
  connect(Controller_Duplex.limitsBus_moduleA_crkB,controllerSettings_moduleA_crkB.limitsBus)
    annotation (Line(points={{-12.314453964650808,33.14467961731743},{-12.314453964650808,46},{72,46},{72,-56},{-56.2,-56}},color={255,204,51}));
  connect(Controller_Duplex.limitsBus_moduleB_crkA,controllerSettings_moduleB_crkA.limitsBus)
    annotation (Line(points={{-24.877365753202373,-0.11008688178965187},{72,-0.11008688178965187},{72,45},{40.8,45}},color={255,204,51}));
  connect(controllerSettings_moduleB_crkB.limitsBus,Controller_Duplex.limitsBus_moduleB_crkB)
    annotation (Line(points={{43.8,-56},{43.8,-0.11008688178965187},{-12.314453964650808,-0.11008688178965187}},color={255,204,51}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end CL_XBV_System_duplex;
