within Workspace.Auxiliary;
model Switch_param
    parameter <PERSON><PERSON><PERSON> = true;
    parameter Real Gain_1 = 0.1;
    parameter Real Gain_2 = -0.001;
    
    
    final parameter Real Output_parameter = if <PERSON><PERSON> then Gain_1 else Gain_2;
    
    annotation(Icon(coordinateSystem(preserveAspectRatio = false,extent = {{-100.0,-100.0},{100.0,100.0}}),graphics = {Rectangle(fillColor={189,16,224},fillPattern=FillPattern.Solid,extent={{-100.0,-100.0},{100.0,100.0}}),Text(lineColor={0,0,255},extent={{-150,150},{150,110}},textString="%name"),Line(origin={36,18},points={{64.12557509119492,-17.830428984766495},{-6,-18},{-64,18}}),Line(origin={-62,41},points={{-38.32492828861143,2.6200399783231916},{38,3},{38,-3}}),Line(origin={-62,-22},points={{-38.08123207215286,-3.9139980172855218},{38,-4.062454007861618},{38.08123207215286,4.062454007861618}})}));
end Switch_param;