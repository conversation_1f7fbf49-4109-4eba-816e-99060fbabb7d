within StateMachine;
record CrkABCDVIABCD_13
  import BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod;
  extends Mode30XBVBase(
    final modeID=ModeID30XBV.CrkABCDVIABCD_13,
    final CrkA=true,
    final CrkB=true,
    final CrkC=true,
    final CrkD=true,
    final EcoA=false,
    final EcoB=false,
    final EcoC=false,
    final EcoD=false,
    final ViA=true,
    final ViB=true,
    final ViC=true,
    final ViD=true,
    initMethod=ModeInitMethod.External);
end CrkABCDVIABCD_13;
