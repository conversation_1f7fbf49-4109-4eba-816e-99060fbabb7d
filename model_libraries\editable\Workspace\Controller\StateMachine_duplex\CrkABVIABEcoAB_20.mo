within StateMachine;
record CrkABVIABEcoAB_20
  import BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod;
  extends Mode30XBVBase(
    final modeID=ModeID30XBV.CrkABVIABEcoAB_20,
    final CrkA=true,
    final CrkB=true,
    final CrkC=false,
    final CrkD=false,
    final EcoA=true,
    final EcoB=true,
    final EcoC=false,
    final EcoD=false,
    final ViA=true,
    final ViB=true,
    final ViC=false,
    final ViD=false,
    initMethod=ModeInitMethod.External);
end CrkABVIABEcoAB_20;
