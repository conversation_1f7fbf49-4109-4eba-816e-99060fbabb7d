within Workspace.System.BaseCycle;

model OpenLoop_Duplex_1

  parameter Real coolantConcentration = 0.2;
  parameter .Modelica.SIunits.TemperatureDifference eco_ssh_A=10 "ECO circuit A suction superheat";
  parameter .Modelica.SIunits.TemperatureDifference eco_ssh_B=10 "ECO circuit B suction superheat";
  parameter .Modelica.SIunits.TemperatureDifference SubcoolingA=-10;
  parameter .Modelica.SIunits.TemperatureDifference SubcoolingB=-10;
  parameter .Modelica.SIunits.Temp_C OAT=35;
  parameter .Modelica.SIunits.Temp_C EWT=12;
  parameter .Modelica.SIunits.Temp_C LWT=7;
  parameter Integer n_coilsB=5 "Number of coils and fans for the circuit B";
  parameter Integer n_coilsA=7 "Number of coils and fans for the circuit A";
  parameter Integer n_passes_evap = 2 "number of passes in the evaporator";
  parameter .Modelica.SIunits.Frequency NcompAMax=95;
  parameter .Modelica.SIunits.Frequency NcompAMin=23;
  parameter .Modelica.SIunits.Frequency NcompBMax=105;
  parameter .Modelica.SIunits.Frequency NcompBMin=27;
  parameter Boolean isOffA=false;
  parameter Boolean isOffB=false;
  parameter Boolean isOffECOA=true;
  parameter Boolean isOffECOB=true;
  parameter Boolean Vi_A=false;
  parameter Boolean Vi_B=false;
  parameter Boolean is_fixedSpeed=false
    "if false then Variable speed, true then fixed speed";
  parameter .Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm
    max_fan_rpm=940 "maximum fan speed";
  parameter .Modelica.SIunits.Frequency max_motor_frequency=50
    "maximum motor frequency";
  parameter .Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm
    limit_max_fan_rpm=940 "limit maximum fan speed";
  parameter Boolean usePump=false "if true, pumps will be integrated into system";
  parameter Boolean isHighAmbientOption=false;
  parameter Boolean isHighEfficiencyOption=false;

    
  // Declaration of Coolant Medium Package
  package CoolantCommonMedium = .BOLT.InternalLibrary.Media.Coolant.CoolantCommon "Coolant Medium Package" annotation (); 
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter .BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
    "Coolant Medium Selection" annotation (Dialog(group="Medium"));  
  //parameter .BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMediumHR=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
    //"Coolant Medium Selection" annotation (Dialog(group="Medium"));
  final parameter .BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(CoolantMedium) "Coolant Medium" annotation ();

  CoolantCommonMedium.Temperature FreezTemp = CoolantCommonMedium.freezeTemp(coolantInf,coolantConcentration,1);

        final parameter .Modelica.SIunits.VolumeFlowRate Vd_flow_min=0.2;
  final parameter .Modelica.SIunits.VolumeFlowRate Vd_flow_max= 6.2;
  Integer coolerMinFlowRateFlag=
   if evaporator.selector_tube == .Public_database.Evaporator.Tube.Selector.B4HSL_075_025_R134a
     and evaporator.summary.Re_a_coolant < 4000 then -1
   else 1 "If cooler side coolant Reynolds number less than 5000 for R-1234ze, flag = -1";

  replaceable model MCHX = .BOLT.Condenser.RefAir.CondAirM constrainedby
    .BOLT.InternalLibrary.BuildingBlocks.Configuration.Base annotation (choices(
        choice(redeclare model MCHX = .BOLT.Condenser.RefAir.CondAirM
          "Physical model"), choice(redeclare model MCHX =
            .BOLT.InternalLibrary.HeatExchangersSurrogate.RefAir.MCHX.CondAirM
          "Surrogate model")));
        


  replaceable model EvapFL_2C = .BOLT.Evaporator.RefCoolant.EvapFL_2C
    constrainedby .BOLT.InternalLibrary.BuildingBlocks.Configuration.Base
    annotation (choices(choice(redeclare model EvapFL_2C =
            .BOLT.Evaporator.RefCoolant.EvapFL_2C "Physical model"), choice(
          redeclare model EvapFL_2C = .BOLT.Evaporator.RefCoolant.EvapFL_2C
          "Surrogate model")));

  replaceable model EcoBPHX = .BOLT.HeatExchanger.RefRef.RefRefBPHE
    constrainedby .BOLT.InternalLibrary.BuildingBlocks.Configuration.Base
    annotation (choices(choice(redeclare model EcoBPHX =
            .BOLT.HeatExchanger.RefRef.RefRefBPHE "Physical model"), choice(
          redeclare model EcoBPHX = .BOLT.HeatExchanger.RefRef.RefRefBPHE
          "Surrogate model")));


  replaceable package RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.R134a
    constrainedby
    .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.GenericPureRefrigerant
    "Refrigerant medium" annotation (choicesAllMatching=true);
    parameter Integer n_coils_sup = 0 "Number of additional coils given by option 119";
    parameter Real Frq_Comp_A = 95;
    parameter Real Frq_Comp_B = 95;
    .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation capacity_error(measurement = evaporator.summary.capacity1 + evaporator.summary.capacity2,ID = 1,setPoint = targetCapacity,gain = 1 / 500000) annotation(Placement(transformation(extent = {{-389.10689082649,-54.04170253106034},{-350.89310917351,-33.95829746893966}},origin = {0,0},rotation = 0)));
    .BOLT.Control.SteadyState.SetpointControl.SetpointController capacity_controller(AV_min = 0,AV_max = 1) annotation(Placement(transformation(extent = {{-323.4196502809689,-57.398627704572945},{-300.62258470424183,-34.601372295427055}},origin = {0,0},rotation = 0)));
    .Workspace.Controller.SubSystems.BaseClasses.LoadDistribution loadDistribution(crkB_max_speed = compressorFrequencyB_max,crkA_max_speed = compressorFrequencyA_max,crkB_min_speed = compressorFrequencyB_min,crkA_min_speed = compressorFrequencyA_min) annotation(Placement(transformation(extent = {{-241.47700891120428,-59.47700891120425},{-214.52299108879572,-32.52299108879575}},origin = {0,0},rotation = 0)));
    .BOLT.Control.SteadyState.SetpointControl.Actuator SSTmaxA(setPoint = 1,maxBound = 1,minBound = 0) annotation(Placement(transformation(extent = {{-34,402},{-54,422}},origin = {0,0},rotation = 0)));
    .BOLT.Control.SteadyState.SetpointControl.Actuator SSTmaxB(minBound = 0,maxBound = 1,setPoint = 1) annotation(Placement(transformation(extent = {{-34,386},{-54,406}},origin = {0,0},rotation = 0)));
    .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculationA(measurement = nodeBPHEECOoutA.summary.dTsh,setPoint = eco_ssh_A) annotation(Placement(transformation(extent = {{-383,56},{-363,76}},origin = {0,0},rotation = 0)));
    .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointControllerA(AV_min = 0.05,AV_max = 1,AV_start = 0.5) annotation(Placement(transformation(extent = {{-321,56},{-301,76}},origin = {0,0},rotation = 0)));
    .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculation_MainA(setPoint = SubcoolingA,gain = -0.1,measurement = nodeEXVmaininA.summary.dTsh) annotation(Placement(transformation(extent = {{-382,0},{-362,20}},origin = {0,0},rotation = 0)));
    .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController_MainA(AV_start = 0.5,AV_max = 1,AV_min = 0.05) annotation(Placement(transformation(extent = {{-322,0},{-302,20}},origin = {0,0},rotation = 0)));
    .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculationB(measurement = nodeBPHEECOoutB.summary.dTsh,setPoint = eco_ssh_B) annotation(Placement(transformation(extent = {{378,61},{358,81}},origin = {0,0},rotation = 0)));
    .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointControllerB(AV_min = 0.05,AV_max = 1,AV_start = 0.5) annotation(Placement(transformation(extent = {{318,59},{298,79}},origin = {0,0},rotation = 0)));
    .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation errorCalculation_MainB(setPoint = SubcoolingB,gain = -0.1,measurement = nodeEXVmaininA.summary.dTsh) annotation(Placement(transformation(extent = {{381,-6},{361,14}},origin = {0,0},rotation = 0)));
    .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController_MainB(AV_start = 0.5,AV_max = 1,AV_min = 0.05) annotation(Placement(transformation(extent = {{319,-6},{299,14}},origin = {0,0},rotation = 0)));
    .BOLT.Control.SteadyState.SetpointControl.Actuator pctFanA(minBound = 0,maxBound = 1,setPoint = 1) annotation(Placement(transformation(extent = {{-302,350},{-322,370}},origin = {0,0},rotation = 0)));
    .Workspace.Auxiliary.FanSignal.FanSignalProcessor fanSignalProcessorA annotation(Placement(transformation(extent = {{-288.28725628581213,337.71274371418787},{-255.71274371418787,370.28725628581213}},origin = {0,0},rotation = 0)));
    .Workspace.Auxiliary.FanSignal.FanSignalProcessor fanSignalProcessorB annotation(Placement(transformation(extent = {{291.32207172919914,344.67792827080086},{260.67792827080086,375.32207172919914}},origin = {0,0},rotation = 0)));
    .BOLT.Control.SteadyState.SetpointControl.Actuator pctFanB(setPoint = 1,maxBound = 1,minBound = 0) annotation(Placement(transformation(extent = {{308,356},{328,376}},origin = {0,0},rotation = 0)));
    inner .BOLT.GlobalParameters globalParameters(varLevel = .BOLT.InternalLibrary.BuildingBlocks.Types.Var_level.Advanced,capacity_start_2 = globalParameters.capacity_design / 2,capacity_start = globalParameters.capacity_design / 2,Tsat_econ_start = 1 / 3 * globalParameters.Tsat_cond_start + 2 / 3 * globalParameters.Tsat_evap_start,Tsat_evap_start = sinkbrine.T_set - 0.5,Tsat_cond_start = max(globalParameters.T_ambient + 10,globalParameters.Tsat_evap_start + 3),T_ambient = sourceAirA.Tdb_set,p_ambient = 101325,m_flow_reference = globalParameters.capacity_design / 2 / (globalParameters.h_b_start_ref - globalParameters.h_a_start_ref),capacity_design = 900000) annotation(Placement(transformation(extent = {{2.9489117775000437,244.10217644499994},{30.74455888750022,271.89782355500006}},origin = {0,0},rotation = 0)));
    .BOLT.BoundaryNode.Refrigerant.Node nodeOilsepoutA(redeclare replaceable package Medium = .BOLT.InternalLibrary.Media.Refrigerant.R134a,isOff = isOffA,streamID = 1) annotation(Placement(transformation(extent = {{-4,-4},{4,4}},origin = {-24.5,214.5},rotation = 90)));
    .BOLT.BoundaryNode.Refrigerant.Node nodeBPHEECOinA(redeclare replaceable package Medium = .BOLT.InternalLibrary.Media.Refrigerant.R134a,isOff = isOffA,h_fixed = false,dTsh_set = -3,dTsh_fixed = false,x_set = -0.03,x_fixed = false,streamID = 1) annotation(Placement(transformation(extent = {{-4,-4},{4,4}},origin = {-256,162},rotation = -90)));
    .BOLT.BoundaryNode.Refrigerant.Node nodeevapinA(redeclare replaceable package Medium = .BOLT.InternalLibrary.Media.Refrigerant.R134a,isOff = isOffA,Tsat_set = 278.15,x_set = 0.5,x_fixed = false,streamID = 1) annotation(Placement(transformation(extent = {{-4,-4},{4,4}},origin = {-258,-42},rotation = -90)));
    .BOLT.BoundaryNode.Refrigerant.Node nodeCpinA(redeclare replaceable package Medium = .BOLT.InternalLibrary.Media.Refrigerant.R134a,isOff = isOffA,T_set = 283.15,Tsat_set = 278.15,dTsh_fixed = false,streamID = 1) annotation(Placement(transformation(extent = {{-4,-4},{4,4}},origin = {-22,138},rotation = 90)));
    inner .BOLT.SystemVariable systemVariables(use_EN14511 = false,pow_set = 272000,pow_fixed = false,isOff = {isOffA,isOffB},nStreams = 2) annotation(Placement(transformation(extent = {{-38.74455888750022,244.10217644499994},{-10.948911777500044,271.89782355500006}},origin = {0,0},rotation = 0)));
    .BOLT.BoundaryNode.Coolant.Source sourcebrine(p_fixed = true,p_set = 101325 + 41300,Vd_fixed = false,Vd_set = 0.1,T_set = EWT + 273.15) annotation(Placement(transformation(extent = {{-200.2022034025935,-57.535536735926854},{-185.57557437518426,-42.9089077085176}},origin = {0,0},rotation = 0)));
    .BOLT.BoundaryNode.Coolant.Sink sinkbrine(p_set = 101325 + 41300,p_fixed = true,Vd_set = 0.1,T_set = LWT + 273.15,T_fixed = true) annotation(Placement(transformation(extent = {{-197.88802277839085,-21.888022778390855},{-182.11197722160915,-6.111977221609145}},origin = {0,0},rotation = 0)));
    .BOLT.Condenser.RefAir.CondAirM condAirA(use_Z_fanScaling = true,Z_dpr_expression = calibrationBlock.Z_Cond_Dpr_A,use_Z_dpr_expression = true,Z_U_expression = calibrationBlock.Z_Cond_A,use_Z_U_expression = true,tubeOrientation = .BOLT.InternalLibrary.BuildingBlocks.Types.TubeOrientation.Vertical_inletTop,redeclare replaceable package RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.R134a,fastMode = true,dehumidifying = false,userDefined_geo(length_tube = 1.937,diameter_in_header = 0.027,length_tube_inlet = 0.2286,diameter_in_inletTube = 0.022352,length_outlet = 0.2286,diameter_in_outlet = 0.016002),selector_curve = .BOLT.InternalLibrary.HeatExchangers.RefAir.DataBase.MCHXGeo.SelectorCurve.MCHXCurve90,n_outlets = {1},n_inlets = {2},n_tubes = [101,20],n_passes = {2},Z_U_fixed = true,Z_U = 0.6,nCoils = n_coilsA + n_coils_sup,isOff = isOffA,streamID = 1) annotation(Placement(transformation(extent = {{-165.9482242541834,209.61535156396593},{-202.0517757458166,242.38464843603407}},origin = {0,0},rotation = 0)));
    .BOLT.BoundaryNode.Air.Source sourceAirA(Vd_flow_bounds = {Vd_flow_min,Vd_flow_max},Vd_flow_set = if is_HR then 1 else 5,p_set = 0,p_fixed = true,Tdb_set = OAT + 273.15,Vds_flow_set = 5,Vds_flow_fixed = false,isOff = isOffA) annotation(Placement(transformation(extent = {{-9.731195983750155,-9.731195983750155},{9.731195983750155,9.731195983750155}},origin = {-184,176},rotation = 90)));
    .BOLT.BoundaryNode.Air.Sink sinkAirA(p_set = 0,p_fixed = true,isOff = isOffA) annotation(Placement(transformation(extent = {{-8.958158691073663,-8.958158691073663},{8.958158691073663,8.958158691073663}},origin = {-194,349},rotation = -90)));
    .BOLT.AirMisc.FanCurve FanA(k_pow = 0,Emotor = 0.74,scalingFactor = 1,use_scalingFactor_in = true,selector_curve = .BOLT.InternalLibrary.Air.Fans.DataBase.Selector.FB6_proto_Dr,isOff = isOffA,nStage_fixed = true,scalingFactor_fixed = true) annotation(Placement(transformation(extent = {{-15.630455945386416,-15.630455945386586},{15.630455945386416,15.630455945386586}},origin = {-194,319},rotation = 90)));
    .BOLT.HeatExchanger.RefRef.RefRefBPHE BPHEECOA(Z_UA_expression = calibrationBlock.Z_U_Eco_A,use_Z_UA_expression = true,nPlate = n_plate_crkA,redeclare replaceable package RefMedium_L = .BOLT.InternalLibrary.Media.Refrigerant.R134a,redeclare replaceable package RefMedium_H = .BOLT.InternalLibrary.Media.Refrigerant.R134a,isEcoOff = isOffECOA or isEcoOff,isOff = isOffA,streamID = 1,selectGeo = choiceBlock.Unit.Eco_Geo_CKA) annotation(Placement(transformation(extent = {{-156,52.931714861508155},{-136,31.068285138491845}},origin = {0,0},rotation = 0)));
    .BOLT.RefMisc.Split split(redeclare replaceable package Medium = .BOLT.InternalLibrary.Media.Refrigerant.R134a,isOff = isOffA,m_flow_b_min = 0.0001,isOff_b = isOffECOA or isEcoOff,x_start = -0.02,Tsat_start = globalParameters.Tsat_cond_start - 0.5,Fraction_b_start = 0.2,streamID = 1) annotation(Placement(transformation(extent = {{6.252495172317083,-6.252495172317097},{-6.252495172317083,6.252495172317097}},origin = {-256,120},rotation = 90)));
    .BOLT.BoundaryNode.Refrigerant.Node nodeEXVECOoutA(redeclare replaceable package Medium = .BOLT.InternalLibrary.Media.Refrigerant.R134a,isOff = isOffECOA or isOffA or isEcoOff,streamID = 1) annotation(Placement(transformation(extent = {{-206,40},{-198,48}},origin = {0,0},rotation = 0)));
  annotation (Icon(coordinateSystem(preserveAspectRatio=false, extent={{-100.0,-100.0},
            {100.0,100.0}}), graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          extent={{-66,64},{76,-60}},
          lineColor={28,108,200},
          textString="OL")}));
end OpenLoop_Duplex_1;
