within StateMachine;
record CrkABCDVIABCDEcoABCD_2
  import BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod;
  extends Mode30XBVBase(
    final modeID=ModeID30XBV.CrkABCDVIABCDEcoABCD_2,
    final CrkA=true,
    final CrkB=true,
    final CrkC=true,
    final CrkD=true,
    final EcoA=true,
    final EcoB=true,
    final EcoC=true,
    final EcoD=true,
    final ViA=true,
    final ViB=true,
    final ViC=true,
    final ViD=true,
    initMethod=ModeInitMethod.External);
end CrkABCDVIABCDEcoABCD_2;
