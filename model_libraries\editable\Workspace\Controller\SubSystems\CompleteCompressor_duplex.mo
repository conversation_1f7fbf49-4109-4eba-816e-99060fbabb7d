within Workspace.Controller.SubSystems;
model CompleteCompressor_duplex
  extends.Workspace.Controller.SubSystems.BaseClasses.CompleteCompressorControlBase_duplex(
    redeclare replaceable.Workspace.Controller.SubSystems.CompressorControl compressorControl_moduleA_crkA,
    redeclare replaceable.Workspace.Controller.SubSystems.CompressorControl compressorControl_moduleA_crkB,
    redeclare replaceable.Workspace.Controller.SubSystems.CompressorControl compressorControl_moduleB_crkA,
    redeclare replaceable.Workspace.Controller.SubSystems.CompressorControl compressorControl_moduleB_crkB,
    loadDistribution_DesignCapa_duplex(
      isOffModuleB=isOff_moduleB_crkA),
    capacity_controller_moduleA(
      AV_start=0.6),
    capacity_controller_moduleB(
      AV_start=0.6),capacity_error_moduleA(gain = 1 / ((max(loadDistribution_DesignCapa_duplex.SetPoint_capacity_moduleA,30000)))),capacity_error_moduleB(gain = 1 / ((max(loadDistribution_DesignCapa_duplex.SetPoint_capacity_moduleB,30000)))));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end CompleteCompressor_duplex;
