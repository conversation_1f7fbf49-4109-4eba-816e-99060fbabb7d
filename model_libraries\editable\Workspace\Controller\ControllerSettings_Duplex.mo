within Workspace.Controller;
model ControllerSettings_Duplex
  extends.BOLT.InternalLibrary.BuildingBlocks.Icons.CtrlSettings;
  parameter.Workspace.Controller.Components.Types.RefrigerantSelector refrigerant=.Workspace.Controller.Components.Types.RefrigerantSelector.R134a
    annotation (Dialog(group="Configuration",tab="General"));
  ////////////////////////// crkA //////////////////////////     
  // crkA Configuration parameters 
  //parameter .Workspace.Controller.Components.Types.RefrigerantSelector CrkA_refrigerant = .Workspace.Controller.Components.Types.RefrigerantSelector.R134a annotation(Dialog(group="Configuration",tab = "CrkA"));
  parameter.Workspace.Controller.Components.Types.CompressorSelector CrkA_compressor=.Workspace.Controller.Components.Types.CompressorSelector.NG1
    annotation (Dialog(group="Configuration",tab="CrkA"));
  parameter Boolean CrkA_isHighAmbientOption=false
    annotation (Dialog(group="Configuration",tab="CrkA"));
  parameter Boolean CrkA_isHighEfficiencyOption=false
    annotation (Dialog(group="Configuration",tab="CrkA"));
  // pas sur 30XBV
  parameter.Modelica.SIunits.Frequency CrkA_compressorFrequency_min=23
    annotation (Dialog(group="Configuration",tab="CrkA"));
  parameter.Modelica.SIunits.Frequency CrkA_compressorFrequency_max=95
    annotation (Dialog(group="Configuration",tab="CrkA"));
  // crkA Tuning parameters used in different setpoint calculations
  Real CrkA_circuitLoad=CrkA_compressorFrequency/CrkA_compressorFrequency_max;
  // crkA Compressor Capacity Setpoint, user input
  parameter.Modelica.SIunits.Power CrkA_capacity_setpoint=400e3
    annotation (Dialog(group="Configuration",tab="CrkA"));
  // crkA Compressor Low SST
  parameter.Modelica.SIunits.Temperature CrkA_T_sst_min_limit_comp=273.15
    annotation (Dialog(group="Tuning",tab="CrkA"));
  // crkA Compressor High SDT
  parameter.Modelica.SIunits.Temperature CrkA_SDT_max=273.15+(
    if CrkA_isHighAmbientOption and CrkA_isHighEfficiencyOption then
      68
    else
      if CrkA_isHighAmbientOption then
        70
      else
        if CrkA_isHighEfficiencyOption then
          64
        else
          67)
    annotation (Dialog(group="Tuning",tab="CrkA"));
  .Modelica.SIunits.Temperature CrkA_T_sdt_max_limit_comp;
  // crkA Compressor High DGT
  parameter.Modelica.SIunits.Temperature CrkA_T_dgt_max_limit_comp=367.15
    annotation (Dialog(group="Tuning",tab="CrkA"));
  // crkA EXV SBC
  .Modelica.SIunits.TemperatureDifference CrkA_dT_sbc_setpoint;
  parameter.Modelica.SIunits.TemperatureDifference CrkA_dT_sbc_min=2
    "Minimum subcooling"
    annotation (Dialog(group="Subcooling Tuning",tab="CrkA"));
  parameter.Modelica.SIunits.TemperatureDifference CrkA_dT_sbc_max=5
    "Maximum subcooling"
    annotation (Dialog(group="Subcooling Tuning",tab="CrkA"));
  parameter.Modelica.SIunits.Temperature CrkA_T_oat_min=273.15-10
    annotation (Dialog(group="Subcooling Tuning",tab="CrkA"));
  parameter.Modelica.SIunits.Temperature CrkA_T_oat_max=273.15+20
    annotation (Dialog(group="Subcooling Tuning",tab="CrkA"));
  parameter Real CrkA_subcoolingSmoothing=0.0666
    annotation (Dialog(group="Subcooling Tuning",tab="CrkA"));
  parameter Real CrkA_rel_cooler_level_setpoint=0.97
    annotation (Dialog(group="HR Tuning",tab="CrkA"));
  // crkA EXV Low SST
  parameter.Modelica.SIunits.Temperature CrkA_T_sst_min_limit_exv=CrkA_T_sst_min_limit_comp+0.5
    annotation (Dialog(group="Tuning",tab="CrkA"));
  // crkA EXV High SST
  .Modelica.SIunits.Temperature CrkA_T_sst_max_limit;
  // crkA EXV Low DSH
  .Modelica.SIunits.TemperatureDifference CrkA_dT_dsh_min_limit;
  // crkA EXV High DGT
  parameter.Modelica.SIunits.Temperature CrkA_T_dgt_max_limit_exv=366.15
    annotation (Dialog(group="Tuning",tab="CrkA"));
  // crkA Fan optimal speed
  Real CrkA_fanSpeed_setpoint;
  parameter Real[15] CrkA_fanCoefficients=(
    if CrkA_compressor ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then
      .Workspace.Controller.Components.Records.fanCurveCoefficients.NG1
    elseif CrkA_compressor ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then
      .Workspace.Controller.Components.Records.fanCurveCoefficients.NG2
    else
      .Workspace.Controller.Components.Records.fanCurveCoefficients.NG3)
    annotation (Dialog(group="Fan Optimal Speed Tuning",tab="CrkA"));
  parameter Integer CrkA_nbrCoils=8
    "Number of coils"
    annotation (Dialog(group="Fan Optimal Speed Tuning",tab="CrkA"));
  // crkA Fan Low SDT
  .Modelica.SIunits.Temperature CrkA_T_sdt_min_limit;
  // crkA Parameters for low OAT conditions
  .Modelica.SIunits.Temperature CrkA_SDT_lmap;
  /////////////////
  parameter.Modelica.SIunits.Temperature CrkA_T_oat_low=273.15-20
    annotation (Dialog(group="Fan Low SDT Tuning",tab="CrkA"));
  parameter.Modelica.SIunits.Temperature CrkA_T_oat_low_sdt_act=273.15+(
    if refrigerant ==.Workspace.Controller.Components.Types.RefrigerantSelector.R134a then
      3
    else
      10)
    annotation (Dialog(group="Fan Low SDT Tuning",tab="CrkA"));
  parameter.Modelica.SIunits.TemperatureDifference CrkA_deltaT=8
    annotation (Dialog(group="Fan Low SDT Tuning",tab="CrkA"));
  // crkA Fan High SDT
  .Modelica.SIunits.Temperature CrkA_T_sdt_max_limit_fan=CrkA_T_sdt_max_limit_comp-2;
  // crkA Fan High DGT
  parameter.Modelica.SIunits.Temperature CrkA_T_dgt_max_limit_fan=CrkA_T_dgt_max_limit_comp-2
    annotation (Dialog(group="Tuning",tab="CrkA"));
  // crkA Economizer EXV max opening
  Real CrkA_ecoEXV_max_opening
    "Max opening of Economizer EXV";
  // crkA Economizer EXV discharge superheat
  parameter.Modelica.SIunits.TemperatureDifference CrkA_dT_esh_setpoint=10
    annotation (Dialog(group="Tuning",tab="CrkA"));
  ////////////////////////// crkB ////////////////////////// 
  // crkB Configuration parameters 
  //parameter .Workspace.Controller.Components.Types.RefrigerantSelector CrkB_refrigerant = .Workspace.Controller.Components.Types.RefrigerantSelector.R134a annotation(Dialog(group="Configuration",tab = "CrkB"));
  parameter.Workspace.Controller.Components.Types.CompressorSelector CrkB_compressor=.Workspace.Controller.Components.Types.CompressorSelector.NG1
    annotation (Dialog(group="Configuration",tab="CrkB"));
  parameter Boolean CrkB_isHighAmbientOption=false
    annotation (Dialog(group="Configuration",tab="CrkB"));
  parameter Boolean CrkB_isHighEfficiencyOption=false
    annotation (Dialog(group="Configuration",tab="CrkB"));
  parameter.Modelica.SIunits.Frequency CrkB_compressorFrequency_min=23
    annotation (Dialog(group="Configuration",tab="CrkB"));
  parameter.Modelica.SIunits.Frequency CrkB_compressorFrequency_max=95
    annotation (Dialog(group="Configuration",tab="CrkB"));
  // crkB Tuning parameters used in different setpoint calculations
  Real CrkB_circuitLoad=CrkB_compressorFrequency/CrkB_compressorFrequency_max;
  // crkB Compressor Capacity Setpoint, user input
  parameter.Modelica.SIunits.Power CrkB_capacity_setpoint=400e3
    annotation (Dialog(group="Configuration",tab="CrkB"));
  // crkB Compressor Low SST
  parameter.Modelica.SIunits.Temperature CrkB_T_sst_min_limit_comp=273.15
    annotation (Dialog(group="Tuning",tab="CrkB"));
  // crkB Compressor High SDT
  parameter.Modelica.SIunits.Temperature CrkB_SDT_max=273.15+(
    if CrkB_isHighAmbientOption and CrkB_isHighEfficiencyOption then
      68
    else
      if CrkB_isHighAmbientOption then
        70
      else
        if CrkB_isHighEfficiencyOption then
          64
        else
          67)
    annotation (Dialog(group="Tuning",tab="CrkB"));
  .Modelica.SIunits.Temperature CrkB_T_sdt_max_limit_comp;
  // crkB Compressor High DGT
  parameter.Modelica.SIunits.Temperature CrkB_T_dgt_max_limit_comp=367.15
    annotation (Dialog(group="Tuning",tab="CrkB"));
  // crkB EXV SBC
  .Modelica.SIunits.TemperatureDifference CrkB_dT_sbc_setpoint;
  parameter.Modelica.SIunits.TemperatureDifference CrkB_dT_sbc_min=2
    "Minimum subcooling"
    annotation (Dialog(group="Subcooling Tuning",tab="CrkB"));
  parameter.Modelica.SIunits.TemperatureDifference CrkB_dT_sbc_max=5
    "Maximum subcooling"
    annotation (Dialog(group="Subcooling Tuning",tab="CrkB"));
  parameter.Modelica.SIunits.Temperature CrkB_T_oat_min=273.15-10
    annotation (Dialog(group="Subcooling Tuning",tab="CrkB"));
  parameter.Modelica.SIunits.Temperature CrkB_T_oat_max=273.15+20
    annotation (Dialog(group="Subcooling Tuning",tab="CrkB"));
  parameter Real CrkB_subcoolingSmoothing=0.0666
    annotation (Dialog(group="Subcooling Tuning",tab="CrkB"));
  parameter Real CrkB_rel_cooler_level_setpoint=0.97
    annotation (Dialog(group="HR Tuning",tab="CrkB"));
  // crkB EXV Low SST
  parameter.Modelica.SIunits.Temperature CrkB_T_sst_min_limit_exv=CrkB_T_sst_min_limit_comp+0.5
    annotation (Dialog(group="Tuning",tab="CrkB"));
  // crkB EXV High SST
  .Modelica.SIunits.Temperature CrkB_T_sst_max_limit;
  // crkB EXV Low DSH
  .Modelica.SIunits.TemperatureDifference CrkB_dT_dsh_min_limit;
  // crkB EXV High DGT
  parameter.Modelica.SIunits.Temperature CrkB_T_dgt_max_limit_exv=366.15
    annotation (Dialog(group="Tuning",tab="CrkB"));
  // crkB Fan optimal speed
  Real CrkB_fanSpeed_setpoint;
  parameter Real[15] CrkB_fanCoefficients=(
    if CrkB_compressor ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 then
      .Workspace.Controller.Components.Records.fanCurveCoefficients.NG1
    elseif CrkB_compressor ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then
      .Workspace.Controller.Components.Records.fanCurveCoefficients.NG2
    else
      .Workspace.Controller.Components.Records.fanCurveCoefficients.NG3)
    annotation (Dialog(group="Fan Optimal Speed Tuning",tab="CrkB"));
  parameter Integer CrkB_nbrCoils=8
    "Number of coils"
    annotation (Dialog(group="Fan Optimal Speed Tuning",tab="CrkB"));
  // crkB Fan Low SDT
  .Modelica.SIunits.Temperature CrkB_T_sdt_min_limit;
  // crkB Parameters for low OAT conditions
  .Modelica.SIunits.Temperature CrkB_SDT_lmap;
  parameter.Modelica.SIunits.Temperature CrkB_T_oat_low=273.15-20
    annotation (Dialog(group="Fan Low SDT Tuning",tab="CrkB"));
  parameter.Modelica.SIunits.Temperature CrkB_T_oat_low_sdt_act=273.15+(
    if refrigerant ==.Workspace.Controller.Components.Types.RefrigerantSelector.R134a then
      3
    else
      10)
    annotation (Dialog(group="Fan Low SDT Tuning",tab="CrkB"));
  parameter.Modelica.SIunits.TemperatureDifference CrkB_deltaT=8
    annotation (Dialog(group="Fan Low SDT Tuning",tab="CrkB"));
  // crkB Fan High SDT
  .Modelica.SIunits.Temperature CrkB_T_sdt_max_limit_fan=CrkB_T_sdt_max_limit_comp-2;
  // crkB Fan High DGT
  parameter.Modelica.SIunits.Temperature CrkB_T_dgt_max_limit_fan=CrkB_T_dgt_max_limit_comp-2
    annotation (Dialog(group="Tuning",tab="CrkB"));
  // crkB Economizer EXV max opening
  Real CrkB_ecoEXV_max_opening
    "Max opening of Economizer EXV";
  // crkB Economizer EXV discharge superheat
  parameter.Modelica.SIunits.TemperatureDifference CrkB_dT_esh_setpoint=10
    annotation (Dialog(group="Tuning",tab="CrkB"));
  ////////////////////////// crkA //////////////////////////     
  .Modelica.Blocks.Sources.RealExpression CrkA_T_sst_max_limit_block(
    y=CrkA_T_sst_max_limit)
    annotation (Placement(transformation(extent={{-100.133211193279,52.5759778175958},{-70.05390089176454,72.5759778175958}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkA_T_sst_min_limit_exv_block(
    y=CrkA_T_sst_min_limit_exv)
    annotation (Placement(transformation(extent={{-99.14848172455694,24.181318673508784},{-69.06917142304249,44.181318673508784}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkA_T_dgt_max_limit_comp_block(
    y=CrkA_T_dgt_max_limit_comp)
    annotation (Placement(transformation(extent={{-99.71566089788357,9.897729086845462},{-69.63635059636911,29.897729086845462}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkA_T_sdt_max_limit_comp_block(
    y=CrkA_T_sdt_max_limit_comp)
    annotation (Placement(transformation(extent={{-99.71566089788357,-32.352270913154534},{-69.63635059636911,-12.352270913154534}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkA_T_sdt_min_limit_block(
    y=CrkA_T_sdt_min_limit)
    annotation (Placement(transformation(extent={{-99.71566089788357,-61.352270913154534},{-69.63635059636911,-41.352270913154534}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkA_dT_sbc_setpoint_block(
    y=CrkA_dT_sbc_setpoint)
    annotation (Placement(transformation(extent={{-99.71566089788357,-76.10227091315454},{-69.63635059636911,-56.10227091315454}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkA_dT_esh_setpoint_block(
    y=CrkA_dT_esh_setpoint)
    annotation (Placement(transformation(extent={{-99.71566089788357,-89.35227091315454},{-69.63635059636911,-69.35227091315454}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkA_dT_dsh_min_limit_block(
    y=CrkA_dT_dsh_min_limit)
    annotation (Placement(transformation(extent={{-99.71566089788357,-103.35227091315454},{-69.63635059636911,-83.35227091315454}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkA_capacity_setpoint_block(
    y=CrkA_capacity_setpoint)
    annotation (Placement(transformation(extent={{-99.71566089788357,-116.10227091315454},{-69.63635059636911,-96.10227091315454}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkA_fanSpeed_setpoint_block(
    y=CrkA_fanSpeed_setpoint)
    annotation (Placement(transformation(extent={{-99.71566089788357,-130.10227091315454},{-69.63635059636911,-110.10227091315454}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkA_T_sst_min_limit_comp_block(
    y=CrkA_T_sst_min_limit_comp)
    annotation (Placement(transformation(extent={{-99.71566089788357,37.89772908684546},{-69.63635059636911,57.89772908684546}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkA_T_sdt_max_limit_fan_block(
    y=CrkA_T_sdt_max_limit_fan)
    annotation (Placement(transformation(extent={{-99.71566089788357,-46.352270913154534},{-69.63635059636911,-26.352270913154534}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkA_T_dgt_max_limit_exv_block(
    y=CrkA_T_dgt_max_limit_exv)
    annotation (Placement(transformation(extent={{-99.71566089788357,-4.102270913154538},{-69.63635059636911,15.897729086845462}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkA_T_dgt_max_limit_fan_block(
    y=CrkA_T_dgt_max_limit_fan)
    annotation (Placement(transformation(extent={{-99.71566089788357,-18.102270913154577},{-69.63635059636911,1.8977290868454233}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkA_ecoEXV_max_opening_block(
    y=CrkA_ecoEXV_max_opening)
    annotation (Placement(transformation(extent={{-99.99925048454685,-146.38586049981785},{-69.9199401830324,-126.38586049981785}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1Ds CrkA_dT_dsh_low_lwt_linear_interpolation(
    table=[
      0.49,5;
      0.5,5;
      1,7;
      1.01,7],
    smoothness=.Modelica.Blocks.Types.Smoothness.MonotoneContinuousDerivative1,
    extrapolation=.Modelica.Blocks.Types.Extrapolation.HoldLastPoint)
    "Interpolation table for low DSH setpoint in EXV controller in case of R1234ze and low LWT"
    annotation (Placement(transformation(extent={{-232.67600574712634,-63.52075740762747},{-212.67600574712634,-43.52075740762747}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Tables.CombiTable1Ds CrkA_T_sdt_low_map_interpolation(
    table={{-23.31,3.9},{-23.3,3.9},{-15,7.9},{5,20},{17,28.7},{22,32.3},{22.01,32.3}} .+ 273.15,
    smoothness=.Modelica.Blocks.Types.Smoothness.LinearSegments,
    extrapolation=.Modelica.Blocks.Types.Extrapolation.HoldLastPoint)
    "Interpolation table for low SDT setpoint in Fan controller in case of low OAT"
    annotation (Placement(transformation(extent={{-232.67600574712634,-104.10227091315454},{-212.67600574712634,-84.10227091315454}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Tables.CombiTable1Ds CrkA_ecoEXV_max_opening_interpolation(
    table=[
      0.59,0.3;
      0.61,1.1],
    smoothness=.Modelica.Blocks.Types.Smoothness.MonotoneContinuousDerivative1,
    extrapolation=.Modelica.Blocks.Types.Extrapolation.HoldLastPoint)
    "Interpolation table for Economizer EXV max opening calculation"
    annotation (Placement(transformation(extent={{-232.67600574712634,-140.10227091315454},{-212.67600574712634,-120.10227091315454}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression CrkA_rel_cooler_level_setpoint_block(
    y=CrkA_rel_cooler_level_setpoint)
    annotation (Placement(transformation(extent={{-99.71566089788357,-160.10227091315454},{-69.63635059636911,-140.10227091315454}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput CrkA_rel_cooler_level
    "Compressor speed in Hz"
    annotation (Placement(transformation(extent={{-230.67600574712634,79.89772908684546},{-210.67600574712634,99.89772908684546}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-46.4}},origin={-175,130})));
  //protected  
  .Modelica.Blocks.Interfaces.RealInput CrkA_T_sst
    "Suction saturated temperature"
    annotation (Placement(transformation(extent={{-230.67600574712634,41.89772908684546},{-210.67600574712634,61.89772908684546}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-46.4}},origin={-175,130})));
  .Modelica.Blocks.Interfaces.RealInput CrkA_compressorFrequency
    "Compressor speed in Hz"
    annotation (Placement(transformation(extent={{-230.67600574712634,61.89772908684546},{-210.67600574712634,81.89772908684546}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-46.4}},origin={-175,130})));
  .Modelica.Blocks.Interfaces.RealInput T_oat
    "Outside air temperature"
    annotation (Placement(transformation(extent={{-232.42054626370762,22.47924259237257},{-212.42054626370762,42.47924259237257}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-46.4}},origin={-175,130})));
  /////////////////// 
  .Modelica.Blocks.Interfaces.RealInput CrkA_T_lwt
    "Leaving water temperature"
    annotation (Placement(transformation(extent={{-230.67600574712634,1.8977290868454624},{-210.67600574712634,21.897729086845462}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-46.4}},origin={-175,130})));
  .Modelica.Blocks.Interfaces.RealInput CrkA_T_sdt
    "Suction discharge temperature"
    annotation (Placement(transformation(extent={{-231.8390327581805,-19.265297924208703},{-211.8390327581805,0.7347020757912972}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-46.4}},origin={-175,130})));
  Real deltaT_oat=T_oat-CrkA_T_oat_low;
  ///////////////////
  parameter Real CrkA_machineError_Temperature=0.01
    annotation (Dialog(group="Parameters",tab="CrkA"));
  .Workspace.Interfaces.MeasurementBus measurementBus_CrkA
    annotation (Placement(transformation(extent={{-284.67600574712634,-48.102270913154534},{-244.67600574712634,-8.102270913154534}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-120.0,50.0},{-80.0,90.0}},origin={40,-180})));
  .Workspace.Interfaces.LimitsBus limitsBus_CrkA
    annotation (Placement(transformation(extent={{-52.67600574712634,-68.10227091315454},{-12.67600574712634,-28.102270913154534}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-110.53884282723261,19.461157172767386},{-89.46115717276739,40.538842827232614}},origin={70,-135})));
  ////////////////////////// crkB //////////////////////////    
  .Modelica.Blocks.Sources.RealExpression CrkB_T_sst_max_limit_block(
    y=CrkB_T_sst_max_limit)
    annotation (Placement(transformation(extent={{-101.59434629946361,-207.644533129136},{-71.51503599794916,-187.644533129136}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkB_T_sst_min_limit_exv_block(
    y=CrkB_T_sst_min_limit_exv)
    annotation (Placement(transformation(extent={{-101.59434629946361,-235.22698283374052},{-71.51503599794916,-215.22698283374052}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkB_T_dgt_max_limit_comp_block(
    y=CrkB_T_dgt_max_limit_comp)
    annotation (Placement(transformation(extent={{-102.85312663151987,-250.90331346119223},{-72.77381633000542,-230.90331346119223}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkB_T_sdt_max_limit_comp_block(
    y=CrkB_T_sdt_max_limit_comp)
    annotation (Placement(transformation(extent={{-101.17679600406814,-292.31208342453147},{-71.09748570255368,-272.31208342453147}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkB_T_sdt_min_limit_block(
    y=CrkB_T_sdt_min_limit)
    annotation (Placement(transformation(extent={{-101.59434629946361,-322.15331346119217},{-71.51503599794916,-302.15331346119217}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkB_dT_sbc_setpoint_block(
    y=CrkB_dT_sbc_setpoint)
    annotation (Placement(transformation(extent={{-101.59434629946361,-334.894533129136},{-71.51503599794916,-314.894533129136}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkB_dT_esh_setpoint_block(
    y=CrkB_dT_esh_setpoint)
    annotation (Placement(transformation(extent={{-101.59434629946361,-348.894533129136},{-71.51503599794916,-328.894533129136}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkB_dT_dsh_min_limit_block(
    y=CrkB_dT_dsh_min_limit)
    annotation (Placement(transformation(extent={{-101.59434629946361,-362.894533129136},{-71.51503599794916,-342.894533129136}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkB_capacity_setpoint_block(
    y=CrkB_capacity_setpoint)
    annotation (Placement(transformation(extent={{-101.96471666983399,-377.5704590550618},{-71.88540636831954,-357.5704590550618}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkB_fanSpeed_setpoint_block(
    y=CrkB_fanSpeed_setpoint)
    annotation (Placement(transformation(extent={{-101.59434629946361,-390.894533129136},{-71.51503599794916,-370.894533129136}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkB_T_sst_min_limit_comp_block(
    y=CrkB_T_sst_min_limit_comp)
    annotation (Placement(transformation(extent={{-101.59434629946361,-221.644533129136},{-71.51503599794916,-201.644533129136}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkB_T_sdt_max_limit_fan_block(
    y=CrkB_T_sdt_max_limit_fan)
    annotation (Placement(transformation(extent={{-101.59434629946361,-305.894533129136},{-71.51503599794916,-285.894533129136}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkB_T_dgt_max_limit_exv_block(
    y=CrkB_T_dgt_max_limit_exv)
    annotation (Placement(transformation(extent={{-101.59434629946361,-263.644533129136},{-71.51503599794916,-243.644533129136}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkB_T_dgt_max_limit_fan_block(
    y=CrkB_T_dgt_max_limit_fan)
    annotation (Placement(transformation(extent={{-101.59434629946361,-277.644533129136},{-71.51503599794916,-257.644533129136}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression CrkB_ecoEXV_max_opening_block(
    y=CrkB_ecoEXV_max_opening)
    annotation (Placement(transformation(extent={{-101.59434629946361,-405.644533129136},{-71.51503599794916,-385.644533129136}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Tables.CombiTable1Ds CrkB_dT_dsh_low_lwt_linear_interpolation(
    extrapolation=.Modelica.Blocks.Types.Extrapolation.HoldLastPoint,
    smoothness=.Modelica.Blocks.Types.Smoothness.MonotoneContinuousDerivative1,
    table=[
      0.49,5;
      0.5,5;
      1,7;
      1.01,7])
    "Interpolation table for low DSH setpoint in EXV controller in case of R1234ze and low LWT"
    annotation (Placement(transformation(extent={{-234.5546911487064,-323.644533129136},{-214.5546911487064,-303.644533129136}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Tables.CombiTable1Ds CrkB_T_sdt_low_map_interpolation(
    extrapolation=.Modelica.Blocks.Types.Extrapolation.HoldLastPoint,
    smoothness=.Modelica.Blocks.Types.Smoothness.LinearSegments,
    table={{-23.31,3.9},{-23.3,3.9},{-15,7.9},{5,20},{17,28.7},{22,32.3},{22.01,32.3}} .+ 273.15)
    "Interpolation table for low SDT setpoint in Fan controller in case of low OAT"
    annotation (Placement(transformation(extent={{-232.92506151907676,-363.5704590550618},{-212.92506151907676,-343.5704590550618}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Tables.CombiTable1Ds CrkB_ecoEXV_max_opening_interpolation(
    extrapolation=.Modelica.Blocks.Types.Extrapolation.HoldLastPoint,
    smoothness=.Modelica.Blocks.Types.Smoothness.MonotoneContinuousDerivative1,
    table=[
      0.59,0.3;
      0.61,1.1])
    "Interpolation table for Economizer EXV max opening calculation"
    annotation (Placement(transformation(extent={{-234.92506151907676,-403.5704590550618},{-214.92506151907676,-383.5704590550618}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Sources.RealExpression CrkB_rel_cooler_level_setpoint_block(
    y=CrkB_rel_cooler_level_setpoint)
    annotation (Placement(transformation(extent={{-101.59434629946361,-419.644533129136},{-71.51503599794916,-399.644533129136}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput CrkB_rel_cooler_level
    "Compressor speed in Hz"
    annotation (Placement(transformation(extent={{-233.19902744291917,-177.71152424649745},{-213.19902744291917,-157.71152424649745}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-46.4}},origin={-175,130})));
  .Modelica.Blocks.Interfaces.RealInput CrkB_T_sst
    "Suction saturated temperature"
    annotation (Placement(transformation(extent={{-232.55469114870638,-217.644533129136},{-212.55469114870638,-197.644533129136}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-46.4}},origin={-175,130})));
  .Modelica.Blocks.Interfaces.RealInput CrkB_compressorFrequency
    "Compressor speed in Hz"
    annotation (Placement(transformation(extent={{-232.55469114870638,-197.644533129136},{-212.55469114870638,-177.644533129136}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-46.4}},origin={-175,130})));
  .Modelica.Blocks.Interfaces.RealInput CrkB_T_oat
    "Outside air temperature"
    annotation (Placement(transformation(extent={{-232.55469114870638,-237.644533129136},{-212.55469114870638,-217.644533129136}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-46.4}},origin={-175,130})));
  .Modelica.Blocks.Interfaces.RealInput CrkB_T_lwt
    "Leaving water temperature"
    annotation (Placement(transformation(extent={{-232.55469114870638,-257.644533129136},{-212.55469114870638,-237.644533129136}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-46.4}},origin={-175,130})));
  .Modelica.Blocks.Interfaces.RealInput CrkB_T_sdt
    "Suction discharge temperature"
    annotation (Placement(transformation(extent={{-232.55469114870638,-277.644533129136},{-212.55469114870638,-257.644533129136}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-46.4}},origin={-175,130})));
  parameter Real CrkB_machineError_Temperature=0.01
    annotation (Dialog(group="Parameters",tab="CrkB"));
  .Workspace.Interfaces.MeasurementBus measurementBus_CrkB
    annotation (Placement(transformation(extent={{-286.5546911487063,-307.644533129136},{-246.5546911487063,-267.644533129136}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-120.0,50.0},{-80.0,90.0}},origin={160,-180})));
  .Workspace.Interfaces.LimitsBus limitsBus_CrkB
    annotation (Placement(transformation(extent={{-54.10472708323075,-327.1945690636603},{-14.104727083230749,-287.1945690636603}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-110.53884282723261,19.461157172767386},{-89.46115717276739,40.538842827232614}},origin={130,-135})));
equation
  assert(
    refrigerant ==.Workspace.Controller.Components.Types.RefrigerantSelector.R134a or refrigerant ==.Workspace.Controller.Components.Types.RefrigerantSelector.R1234ze,
    "Not supported refrigerant chosen.");
  assert(
    CrkA_compressor ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 or CrkA_compressor ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 or CrkA_compressor ==.Workspace.Controller.Components.Types.CompressorSelector.NG3,
    "Not supported compressor chosen.");
  assert(
    refrigerant ==.Workspace.Controller.Components.Types.RefrigerantSelector.R134a or refrigerant ==.Workspace.Controller.Components.Types.RefrigerantSelector.R1234ze,
    "Not supported refrigerant chosen.");
  assert(
    CrkB_compressor ==.Workspace.Controller.Components.Types.CompressorSelector.NG1 or CrkB_compressor ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 or CrkB_compressor ==.Workspace.Controller.Components.Types.CompressorSelector.NG3,
    "Not supported compressor chosen.");
  ////////////////////////// crkA //////////////////////////    
  // CrkA Compressor High SDT
  CrkA_T_sdt_max_limit_comp=min(
    CrkA_SDT_max,
    1.11111*CrkA_T_sst+39.46481);
  // CrkA EXV SBC
  CrkA_dT_sbc_setpoint=.Workspace.Controller.Components.Functions.subcooling(
    refrigerant,
    CrkA_compressorFrequency,
    CrkA_compressorFrequency_min,
    CrkA_compressorFrequency_max,
    CrkA_dT_sbc_min,
    CrkA_dT_sbc_max,
    T_oat,
    CrkA_T_oat_min,
    CrkA_T_oat_max,
    CrkA_subcoolingSmoothing);
  // crkA EXV High SST
  //T_sst_max_limit = min(295.15, -0.36190 * T_sdt + 410.71857);
  CrkA_T_sst_max_limit=295.15;
  // Extension for Data Center Application Not available for NG3 in R134a (not use now, 11 nov 2021)
  // crkA EXV Low DSH
  CrkA_dT_dsh_low_lwt_linear_interpolation.u=CrkA_circuitLoad;
  if refrigerant ==.Workspace.Controller.Components.Types.RefrigerantSelector.R134a or CrkA_T_lwt >= 273.15+15 then
    // R134a or R1234ze with LWT >= 15C
    CrkA_dT_dsh_min_limit=7;
  else
    // R1234ze with LWT < 15C
    CrkA_dT_dsh_min_limit=CrkA_dT_dsh_low_lwt_linear_interpolation.y[1];
  end if;
  // crkA Fan optimal speed
  CrkA_fanSpeed_setpoint=.BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax(
    0,
    .BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMin(
      50,
      .Workspace.Controller.Components.Functions.fanSpeed(
        CrkA_compressorFrequency,
        CrkA_nbrCoils,
        CrkA_T_lwt,
        T_oat,
        CrkA_fanCoefficients),
      1e-4),
    1e-4);
  // CrkA Fan Low SDT
  CrkA_T_sdt_low_map_interpolation.u=CrkA_T_sst;
  // Interpolation table is defined inside T_sdt_low_map_interpolation by the points 8-7-6-5 from compressor map.
  CrkA_SDT_lmap=CrkA_T_sdt_low_map_interpolation.y[1];
  //     if T_oat > T_oat_low then
  //         // alpha (soft factor for softMin and softMax) = 1e-4 * nominal setpoint (~300k) = 0.03
  //         T_sdt_min_limit = softMax(293.15, softMin(301.7, 0.71296 * T_sst + 94.83835, 0.03), 0.03);
  //     else  // T_oat <= T_oat_low
  //         T_sdt_min_limit = softMax(SDT_lmap, SDT_lmap + (circuitLoad * ((T_oat - T_oat_low) / (T_oat_low_sdt_act - T_oat_low)) * deltaT), 0.03);
  //     end if;
  CrkA_T_sdt_min_limit=
    if T_oat-CrkA_T_oat_low > CrkA_machineError_Temperature then
      .BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax(
        293.15,
        .BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMin(
          301.7,
          0.71296*CrkA_T_sst+94.83835,
          0.03),
        0.03)
    else
      .BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax(
        CrkA_SDT_lmap,
        CrkA_SDT_lmap+(CrkA_circuitLoad*((T_oat-CrkA_T_oat_low)/(CrkA_T_oat_low_sdt_act-CrkA_T_oat_low))*CrkA_deltaT),
        0.03);
  // CrkA Economizer EXV max opening
  CrkA_ecoEXV_max_opening_interpolation.u=CrkA_circuitLoad;
  //     if T_lwt < (273.15 + 1) then
  //         ecoEXV_max_opening = ecoEXV_max_opening_interpolation.y[1];
  //     else
  //         ecoEXV_max_opening = 1.1;
  //     end if;
  CrkA_ecoEXV_max_opening=
    if CrkA_T_lwt-(273.15+1) <-CrkA_machineError_Temperature then
      CrkA_ecoEXV_max_opening_interpolation.y[1]
    else
      1.1;
  ////////////////////////// crkB ////////////////////////// 
  // CrkB Compressor High SDT
  CrkB_T_sdt_max_limit_comp=min(
    CrkB_SDT_max,
    1.11111*CrkB_T_sst+39.46481);
  // CrkB EXV SBC
  CrkB_dT_sbc_setpoint=.Workspace.Controller.Components.Functions.subcooling(
    refrigerant,
    CrkB_compressorFrequency,
    CrkB_compressorFrequency_min,
    CrkB_compressorFrequency_max,
    CrkB_dT_sbc_min,
    CrkB_dT_sbc_max,
    T_oat,
    CrkB_T_oat_min,
    CrkB_T_oat_max,
    CrkB_subcoolingSmoothing);
  // crkB EXV High SST  
  CrkB_T_sst_max_limit=295.15;
  // Extension for Data Center Application Not available for NG3 in R134a (not use now, 11 nov 2021)    
  // crkB EXV Low DSH
  CrkB_dT_dsh_low_lwt_linear_interpolation.u=CrkB_circuitLoad;
  if refrigerant ==.Workspace.Controller.Components.Types.RefrigerantSelector.R134a or CrkB_T_lwt >= 273.15+15 then
    // R134a or R1234ze with LWT >= 15C
    CrkB_dT_dsh_min_limit=7;
  else
    CrkB_dT_dsh_min_limit=CrkB_dT_dsh_low_lwt_linear_interpolation.y[1];
  end if;
  // crkB Fan optimal speed
  CrkB_fanSpeed_setpoint=.BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax(
    0,
    .BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMin(
      50,
      .Workspace.Controller.Components.Functions.fanSpeed(
        CrkB_compressorFrequency,
        CrkB_nbrCoils,
        CrkB_T_lwt,
        CrkB_T_oat,
        CrkB_fanCoefficients),
      1e-4),
    1e-4);
  // CrkB Fan Low SDT
  CrkB_T_sdt_low_map_interpolation.u=CrkB_T_sst;
  // Interpolation table is defined inside T_sdt_low_map_interpolation by the points 8-7-6-5 from compressor map.
  CrkB_SDT_lmap=CrkB_T_sdt_low_map_interpolation.y[1];
  CrkB_T_sdt_min_limit=
    if CrkB_T_oat-CrkB_T_oat_low > CrkB_machineError_Temperature then
      .BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax(
        293.15,
        .BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMin(
          301.7,
          0.71296*CrkB_T_sst+94.83835,
          0.03),
        0.03)
    else
      .BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax(
        CrkB_SDT_lmap,
        CrkB_SDT_lmap+(CrkB_circuitLoad*((CrkB_T_oat-CrkB_T_oat_low)/(CrkB_T_oat_low_sdt_act-CrkB_T_oat_low))*CrkB_deltaT),
        0.03);
  // CrkB Economizer EXV max opening
  CrkB_ecoEXV_max_opening_interpolation.u=CrkB_circuitLoad;
  CrkB_ecoEXV_max_opening=
    if CrkB_T_lwt-(273.15+1) <-CrkB_machineError_Temperature then
      CrkB_ecoEXV_max_opening_interpolation.y[1]
    else
      1.1;
  ////////////////////////// crkA ////////////////////////// 
  connect(CrkA_rel_cooler_level,measurementBus_CrkA.rel_cooler_level)
    annotation (Line(points={{-220.67600574712634,89.89772908684546},{-264.67600574712634,89.89772908684546},{-264.67600574712634,-28.102270913154534}},color={0,0,127}));
  connect(CrkA_compressorFrequency,measurementBus_CrkA.compressorFrequency)
    annotation (Line(points={{-220.67600574712634,71.89772908684546},{-264.67600574712634,71.89772908684546},{-264.67600574712634,-28.102270913154534}},color={0,0,127}));
  connect(CrkA_T_sst,measurementBus_CrkA.T_sst)
    annotation (Line(points={{-220.67600574712634,51.897729086845466},{-264.67600574712634,51.897729086845466},{-264.67600574712634,-28.102270913154534}},color={0,0,127}));
  connect(CrkA_T_lwt,measurementBus_CrkA.T_lwt)
    annotation (Line(points={{-220.67600574712634,11.897729086845466},{-264.67600574712634,11.897729086845466},{-264.67600574712634,-28.102270913154534}},color={0,0,127}));
  connect(CrkA_T_sdt,measurementBus_CrkA.T_sdt)
    annotation (Line(points={{-221.8390327581805,-9.265297924208703},{-264.67600574712634,-9.265297924208703},{-264.67600574712634,-28.102270913154534}},color={0,0,127}));
  connect(CrkA_T_sst_max_limit_block.y,limitsBus_CrkA.T_sst_max_limit)
    annotation (Line(points={{-68.54993537668882,62.5759778175958},{-32.67600574712634,62.5759778175958},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  connect(CrkA_T_sst_min_limit_comp_block.y,limitsBus_CrkA.T_sst_min_limit_comp)
    annotation (Line(points={{-68.13238508129339,47.897729086845466},{-32.67600574712634,47.897729086845466},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  connect(CrkA_T_sst_min_limit_exv_block.y,limitsBus_CrkA.T_sst_min_limit_exv)
    annotation (Line(points={{-67.56520590796677,34.181318673508784},{-32.67600574712634,34.181318673508784},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  connect(CrkA_T_dgt_max_limit_comp_block.y,limitsBus_CrkA.T_dgt_max_limit_comp)
    annotation (Line(points={{-68.13238508129339,19.897729086845466},{-32.67600574712634,19.897729086845466},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  connect(CrkA_T_dgt_max_limit_exv_block.y,limitsBus_CrkA.T_dgt_max_limit_exv)
    annotation (Line(points={{-68.13238508129339,5.897729086845466},{-32.67600574712634,5.897729086845466},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  connect(CrkA_T_dgt_max_limit_fan_block.y,limitsBus_CrkA.T_dgt_max_limit_fan)
    annotation (Line(points={{-68.13238508129339,-8.102270913154577},{-32.67600574712634,-8.102270913154577},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  connect(CrkA_T_sdt_max_limit_comp_block.y,limitsBus_CrkA.T_sdt_max_limit_comp)
    annotation (Line(points={{-68.13238508129339,-22.352270913154534},{-32.67600574712634,-22.352270913154534},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  connect(CrkA_T_sdt_max_limit_fan_block.y,limitsBus_CrkA.T_sdt_max_limit_fan)
    annotation (Line(points={{-68.13238508129339,-36.352270913154534},{-32.67600574712634,-36.352270913154534},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  connect(CrkA_T_sdt_min_limit_block.y,limitsBus_CrkA.T_sdt_min_limit)
    annotation (Line(points={{-68.13238508129339,-51.352270913154534},{-32.67600574712634,-51.352270913154534},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  connect(CrkA_dT_sbc_setpoint_block.y,limitsBus_CrkA.dT_sbc_setpoint)
    annotation (Line(points={{-68.13238508129339,-66.10227091315454},{-32.67600574712634,-66.10227091315454},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  connect(CrkA_dT_esh_setpoint_block.y,limitsBus_CrkA.dT_esh_setpoint)
    annotation (Line(points={{-68.13238508129339,-79.35227091315454},{-32.67600574712634,-79.35227091315454},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  connect(CrkA_dT_dsh_min_limit_block.y,limitsBus_CrkA.dT_dsh_min_limt)
    annotation (Line(points={{-68.13238508129339,-93.35227091315454},{-32.67600574712634,-93.35227091315454},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  connect(CrkA_capacity_setpoint_block.y,limitsBus_CrkA.capacity_setpoint)
    annotation (Line(points={{-68.13238508129339,-106.10227091315454},{-32.67600574712634,-106.10227091315454},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  connect(CrkA_fanSpeed_setpoint_block.y,limitsBus_CrkA.fanSpeed_setpoint)
    annotation (Line(points={{-68.13238508129339,-120.10227091315454},{-32.67600574712634,-120.10227091315454},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  connect(CrkA_ecoEXV_max_opening_block.y,limitsBus_CrkA.ecoEXV_max_opening)
    annotation (Line(points={{-68.41597466795668,-136.38586049981785},{-32.67600574712634,-136.38586049981785},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  connect(CrkA_rel_cooler_level_setpoint_block.y,limitsBus_CrkA.rel_cooler_level_setpoint)
    annotation (Line(points={{-68.13238508129339,-150.10227091315454},{-32.67600574712634,-150.10227091315454},{-32.67600574712634,-48.102270913154534}},color={0,0,127}));
  ////////////////////////// crkB ////////////////////////// 
  connect(CrkB_rel_cooler_level,measurementBus_CrkB.rel_cooler_level)
    annotation (Line(points={{-223.19902744291917,-167.71152424649745},{-266.5546911487064,-167.71152424649745},{-266.5546911487064,-287.644533129136}},color={0,0,127}));
  connect(CrkB_compressorFrequency,measurementBus_CrkB.compressorFrequency)
    annotation (Line(points={{-222.55469114870635,-187.644533129136},{-266.5546911487064,-187.644533129136},{-266.5546911487064,-287.644533129136}},color={0,0,127}));
  connect(CrkB_T_sst,measurementBus_CrkB.T_sst)
    annotation (Line(points={{-222.55469114870635,-207.644533129136},{-266.5546911487064,-207.644533129136},{-266.5546911487064,-287.644533129136}},color={0,0,127}));
  connect(CrkB_T_oat,measurementBus_CrkB.T_oat)
    annotation (Line(points={{-222.55469114870635,-227.644533129136},{-266.5546911487064,-227.644533129136},{-266.5546911487064,-287.644533129136}},color={0,0,127}));
  connect(CrkB_T_lwt,measurementBus_CrkB.T_lwt)
    annotation (Line(points={{-222.55469114870635,-247.644533129136},{-266.5546911487064,-247.644533129136},{-266.5546911487064,-287.644533129136}},color={0,0,127}));
  connect(CrkB_T_sdt,measurementBus_CrkB.T_sdt)
    annotation (Line(points={{-222.55469114870638,-267.644533129136},{-266.5546911487064,-267.644533129136},{-266.5546911487064,-287.644533129136}},color={0,0,127}));
  connect(CrkB_T_sst_max_limit_block.y,limitsBus_CrkB.T_sst_max_limit)
    annotation (Line(points={{-70.01107048287341,-197.644533129136},{-34.10472708323075,-197.644533129136},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(CrkB_T_sst_min_limit_comp_block.y,limitsBus_CrkB.T_sst_min_limit_comp)
    annotation (Line(points={{-70.01107048287341,-211.644533129136},{-34.10472708323075,-211.644533129136},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(CrkB_T_sst_min_limit_exv_block.y,limitsBus_CrkB.T_sst_min_limit_exv)
    annotation (Line(points={{-70.01107048287341,-225.22698283374052},{-34.10472708323075,-225.22698283374052},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(CrkB_T_dgt_max_limit_comp_block.y,limitsBus_CrkB.T_dgt_max_limit_comp)
    annotation (Line(points={{-71.2698508149297,-240.90331346119223},{-34.10472708323075,-240.90331346119223},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(CrkB_T_dgt_max_limit_exv_block.y,limitsBus_CrkB.T_dgt_max_limit_exv)
    annotation (Line(points={{-70.01107048287341,-253.644533129136},{-34.10472708323075,-253.644533129136},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(CrkB_T_dgt_max_limit_fan_block.y,limitsBus_CrkB.T_dgt_max_limit_fan)
    annotation (Line(points={{-70.01107048287341,-267.644533129136},{-34.10472708323075,-267.644533129136},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(CrkB_T_sdt_max_limit_comp_block.y,limitsBus_CrkB.T_sdt_max_limit_comp)
    annotation (Line(points={{-69.59352018747794,-282.31208342453147},{-34.10472708323075,-282.31208342453147},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(CrkB_T_sdt_max_limit_fan_block.y,limitsBus_CrkB.T_sdt_max_limit_fan)
    annotation (Line(points={{-70.01107048287341,-295.894533129136},{-34.10472708323075,-295.894533129136},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(CrkB_T_sdt_min_limit_block.y,limitsBus_CrkB.T_sdt_min_limit)
    annotation (Line(points={{-70.01107048287341,-312.15331346119217},{-34.10472708323075,-312.15331346119217},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(CrkB_dT_sbc_setpoint_block.y,limitsBus_CrkB.dT_sbc_setpoint)
    annotation (Line(points={{-70.01107048287341,-324.894533129136},{-34.10472708323075,-324.894533129136},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(CrkB_dT_esh_setpoint_block.y,limitsBus_CrkB.dT_esh_setpoint)
    annotation (Line(points={{-70.01107048287341,-338.894533129136},{-34.10472708323075,-338.894533129136},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(CrkB_dT_dsh_min_limit_block.y,limitsBus_CrkB.dT_dsh_min_limt)
    annotation (Line(points={{-70.01107048287341,-352.894533129136},{-34.10472708323075,-352.894533129136},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(CrkB_capacity_setpoint_block.y,limitsBus_CrkB.capacity_setpoint)
    annotation (Line(points={{-70.38144085324379,-367.5704590550618},{-34.10472708323075,-367.5704590550618},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(CrkB_fanSpeed_setpoint_block.y,limitsBus_CrkB.fanSpeed_setpoint)
    annotation (Line(points={{-70.01107048287341,-380.894533129136},{-34.10472708323075,-380.894533129136},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(CrkB_ecoEXV_max_opening_block.y,limitsBus_CrkB.ecoEXV_max_opening)
    annotation (Line(points={{-70.01107048287341,-395.644533129136},{-34.10472708323075,-395.644533129136},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(CrkB_rel_cooler_level_setpoint_block.y,limitsBus_CrkB.rel_cooler_level_setpoint)
    annotation (Line(points={{-70.01107048287341,-409.644533129136},{-34.10472708323075,-409.644533129136},{-34.10472708323075,-307.1945690636603}},color={0,0,127}));
  connect(T_oat,measurementBus_CrkA.T_oat)
    annotation (Line(points={{-222.42054626370762,32.47924259237257},{-264.67600574712634,32.47924259237257},{-264.67600574712634,-28.102270913154534}},color={0,0,127}));
  annotation (
    Placement(
      transformation(
        extent={{-80,-80},{-60,-60}})));
end ControllerSettings_Duplex;
