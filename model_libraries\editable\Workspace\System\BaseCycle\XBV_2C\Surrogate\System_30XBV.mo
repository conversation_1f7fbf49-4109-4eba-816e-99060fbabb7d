within Workspace.System.BaseCycle.XBV_2C.Surrogate;
model System_30XBV
  extends.Workspace.System.BaseCycle.XBV_2C.Physical.System_30XBV(
    redeclare replaceable.Workspace.System.BaseCycle.Equipement_surrogate ModuleA,
    choiceBlock(
      Selector_ModuleA=Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Selector.Unit_30XBV_0900));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={245,166,35},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end System_30XBV;
