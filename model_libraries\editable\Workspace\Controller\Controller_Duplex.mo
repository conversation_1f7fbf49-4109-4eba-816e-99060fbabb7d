within Workspace.Controller;
model Controller_Duplex
  extends.Workspace.Controller.SubSystems.BaseClasses.ControllerBase_Duplex(
    redeclare.Workspace.Controller.SubSystems.FanControl fanControl_moduleA_crkA(
      isOffSDTmin=isOffSDTmin_fan,
      isOffSDTmax=isOffSDTmax_fan,
      isOffDGTmax=isOffDGTmax_fan,
      MaxFrequency=MaxFrequency,
      setpointController(
        manualOff=manualOff_fan_crkA,
        AV_value_off=frq_fan_sp_manual_crkA)),
    redeclare.Workspace.Controller.SubSystems.FanControl fanControl_moduleA_crkB(
      isOffSDTmin=isOffSDTmin_fan,
      isOffSDTmax=isOffSDTmax_fan,
      isOffDGTmax=isOffDGTmax_fan,
      MaxFrequency=MaxFrequency,
      setpointController(
        AV_value_off=frq_fan_sp_manual_crkB,
        manualOff=manualOff_fan_crkB)),
    redeclare.Workspace.Controller.SubSystems.FanControl fanControl_moduleB_crkA(
      isOffSDTmin=isOffSDTmin_fan,
      isOffSDTmax=isOffSDTmax_fan,
      isOffDGTmax=isOffDGTmax_fan,
      MaxFrequency=MaxFrequency,
      setpointController(
        AV_value_off=frq_fan_sp_manual_crkC,
        manualOff=manualOff_fan_crkC)),
    redeclare.Workspace.Controller.SubSystems.FanControl fanControl_moduleB_crkB(
      isOffSDTmin=isOffSDTmin_fan,
      isOffSDTmax=isOffSDTmax_fan,
      isOffDGTmax=isOffDGTmax_fan,
      MaxFrequency=MaxFrequency,
      setpointController(
        AV_value_off=frq_fan_sp_manual_crkD,
        manualOff=manualOff_fan_crkD)),
    redeclare.Workspace.Controller.SubSystems.EXVControl eXVControl_moduleA_crkA(
      isOffSSTmin=isOffSSTmin_EXV,
      isOffSSTmax=isOffSSTmax_EXV,
      isOffDSHmin=isOffDSHmin_EXV,
      isOffDGTmax=isOffDGTmax_EXV,
      MaxFanFrequency=MaxFrequency),
    redeclare.Workspace.Controller.SubSystems.EXVControl eXVControl_moduleA_crkB(
      isOffSSTmin=isOffSSTmin_EXV,
      isOffSSTmax=isOffSSTmax_EXV,
      isOffDSHmin=isOffDSHmin_EXV,
      isOffDGTmax=isOffDGTmax_EXV,
      MaxFanFrequency=MaxFrequency),
    redeclare.Workspace.Controller.SubSystems.EXVControl eXVControl_moduleB_crkA(
      isOffSSTmin=isOffSSTmin_EXV,
      isOffSSTmax=isOffSSTmax_EXV,
      isOffDSHmin=isOffDSHmin_EXV,
      isOffDGTmax=isOffDGTmax_EXV,
      MaxFanFrequency=MaxFrequency),
    redeclare.Workspace.Controller.SubSystems.EXVControl eXVControl_moduleB_crkB(
      isOffSSTmin=isOffSSTmin_EXV,
      isOffSSTmax=isOffSSTmax_EXV,
      isOffDSHmin=isOffDSHmin_EXV,
      isOffDGTmax=isOffDGTmax_EXV,
      MaxFanFrequency=MaxFrequency),
    redeclare.Workspace.Controller.SubSystems.CompleteCompressor_duplex completeCompressorControlBase_duplex(
      capacityDesign_moduleA_max=capacityDesign_moduleA_max,
      capacityDesign_moduleB_max=capacityDesign_moduleB_max,
      compressorControl_moduleA_crkA(
        isOffDGTmax=isOffDGTmax_comp,
        isOffSSTmin=isOffSSTmin_comp,
        isOffSDTmax=isOffSDTmax_comp,
        setpointController(
          manualOff=manualOff_compressor_crkA,
          AV_value_off=frq_comp_sp_manual_crkA)),
      compressorControl_moduleA_crkB(
        isOffDGTmax=isOffDGTmax_comp,
        isOffSSTmin=isOffSSTmin_comp,
        isOffSDTmax=isOffSDTmax_comp,
        setpointController(
          AV_value_off=frq_comp_sp_manual_crkA,
          isOff=manualOff_compressor_crkB)),
      compressorControl_moduleB_crkA(
        isOffDGTmax=isOffDGTmax_comp,
        isOffSSTmin=isOffSSTmin_comp,
        isOffSDTmax=isOffSDTmax_comp,
        setpointController(
          AV_value_off=frq_comp_sp_manual_crkC,
          manualOff=manualOff_compressor_crkC)),
      compressorControl_moduleB_crkB(
        isOffDGTmax=isOffDGTmax_comp,
        isOffSSTmin=isOffSSTmin_comp,
        isOffSDTmax=isOffSDTmax_comp,
        setpointController(
          AV_value_off=frq_comp_sp_manual_crkD,
          manualOff=manualOff_compressor_crkD)),
      isOffSSTmin=isOffSSTmin_comp,
      isOffSDTmax=isOffSDTmax_comp,
      isOffDGTmax=isOffDGTmax_comp),
    redeclare.Workspace.Controller.SubSystems.EcoEXVControl ecoEXVControl_moduleA_crkA,
    redeclare.Workspace.Controller.SubSystems.EcoEXVControl ecoEXVControl_moduleA_crkB,
    redeclare.Workspace.Controller.SubSystems.EcoEXVControl ecoEXVControl_moduleB_crkA,
    redeclare.Workspace.Controller.SubSystems.EcoEXVControl ecoEXVControl_moduleB_crkB,
    pumpUserControl_moduleA(
      isEWTControl=isEWTControl,
      UserPumpPresent=isPumpUserPresent),
    pumpUserControl_moduleB(
      isEWTControl=isEWTControl,
      UserPumpPresent=isPumpUserPresent));
  parameter Real capacityDesign_moduleA_max
    "Minimum capacity design of module A (w)"
    annotation (Dialog(group="capacity design"));
  parameter Real capacityDesign_moduleB_max
    "Minimum capacity design of module B (w)"
    annotation (Dialog(group="capacity design"));
  parameter Boolean isPumpUserPresent=false
    annotation (Dialog(tab="General",group="User Pump"));
  parameter Boolean isEWTControl=false
    "True when EWT is controlled"
    annotation (Dialog(tab="General",group="User Pump"));
  parameter Real min_speed_pumpUser=20.0
    annotation (Dialog(tab="General",group="User Pump"));
  parameter Real max_speed_pumpUser=2900.0
    annotation (Dialog(tab="General",group="User Pump"));
  parameter Real MaxFrequency;
  parameter Boolean isOffSDTmin_fan
    annotation (Dialog(group="fanControl ",tab="isOff controller"));
  parameter Boolean isOffSDTmax_fan
    annotation (Dialog(group="fanControl ",tab="isOff controller"));
  parameter Boolean isOffDGTmax_fan
    annotation (Dialog(group="fanControl ",tab="isOff controller"));
  parameter Boolean isOffSSTmin_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffSSTmax_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffDSHmin_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffDGTmax_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffSSTmin_comp
    annotation (Dialog(group="Compressor ",tab="isOff controller"));
  parameter Boolean isOffSDTmax_comp
    annotation (Dialog(group="Compressor ",tab="isOff controller"));
  parameter Boolean isOffDGTmax_comp
    annotation (Dialog(group="Compressor ",tab="isOff controller"));
  parameter Boolean manualOff_fan_crkA=false
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Real frq_fan_sp_manual_crkA=1
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Boolean manualOff_fan_crkB=false
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Real frq_fan_sp_manual_crkB=1
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Boolean manualOff_fan_crkC=false
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Real frq_fan_sp_manual_crkC=1
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Boolean manualOff_fan_crkD=false
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Real frq_fan_sp_manual_crkD=1
    annotation (Dialog(group="Fan ",tab="manualOff"));
  parameter Boolean manualOff_compressor_crkA=false
    annotation (Dialog(group="Compressor ",tab="manualOff"));
  parameter Real frq_comp_sp_manual_crkA=95
    annotation (Dialog(group="Compressor ",tab="manualOff"));
  parameter Boolean manualOff_compressor_crkB=false
    annotation (Dialog(group="Compressor ",tab="manualOff"));
  parameter Real frq_comp_sp_manual_crkB=95
    annotation (Dialog(group="Compressor ",tab="manualOff"));
  parameter Boolean manualOff_compressor_crkC=false
    annotation (Dialog(group="Compressor ",tab="manualOff"));
  parameter Real frq_comp_sp_manual_crkC=95
    annotation (Dialog(group="Compressor ",tab="manualOff"));
  parameter Boolean manualOff_compressor_crkD=false
    annotation (Dialog(group="Compressor ",tab="manualOff"));
  parameter Real frq_comp_sp_manual_crkD=95
    annotation (Dialog(group="Compressor ",tab="manualOff"));
end Controller_Duplex;
