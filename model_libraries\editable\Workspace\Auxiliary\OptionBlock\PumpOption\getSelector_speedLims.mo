within OptionBlock1.PumpOption;
function getSelector_speedLims
  input Workspace.Auxiliary.RecordBase.Selector Selector;
  input Workspace.Auxiliary.OptionBlock.PumpOption.Pump_selector Pump_selector;
  output Real[2] speed_limits;
protected
  Real[:,:,:] speed_limits_base={{{870.0,2900.0},{870.0,2900.0},{870.0,2900.0},{870.0,2900.0},{870.0,2900.0},{870.0,2900.0}},{{870.0,2900.0},{870.0,2900.0},{870.0,2900.0},{870.0,2900.0},{870.0,2900.0},{870.0,2900.0}}};
algorithm
  speed_limits := speed_limits_base[Integer(
    Pump_selector),Integer(
    Selector),:];
end getSelector_speedLims;
