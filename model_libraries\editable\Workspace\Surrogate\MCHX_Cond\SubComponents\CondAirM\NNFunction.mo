within Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM;
function NNFunction
  "NN Function"
  package SI=Modelica.SIunits;
  input LayerBase layer;
  input SI.MassFlowRate m_flow_in_ref;
  input Real x_in;
  input Real x_out;
  input SI.VolumeFlowRate V_flow_in_air;
  input SI.Temperature T_in_air;
  input Real Z_dpr;
  input Real Z_U;
  output Real XOutput;
protected
  Real X0_Unscaled[:]={m_flow_in_ref,x_in,x_out,V_flow_in_air,T_in_air,Z_dpr,Z_U};
  Real X0[size(
    X0_Unscaled,
    1)];
  Real XA[layer.MaxRow];
  Real[:] Mean={layer.Mean.m_flow_in_ref,layer.Mean.x_in,layer.Mean.x_out,layer.Mean.V_flow_in_air,layer.Mean.T_in_air,layer.Mean.Z_dpr,layer.Mean.Z_U};
  Real[:] Std={layer.Std.m_flow_in_ref,layer.Std.x_in,layer.Std.x_out,layer.Std.V_flow_in_air,layer.Std.T_in_air,layer.Std.Z_dpr,layer.Std.Z_U};
algorithm
  X0 :=(X0_Unscaled-Mean) ./ Std;
  XA[1:size(
    layer.W0,
    1)] := 1.0 ./(1.0 .+ Modelica.Math.exp(
    -(layer.W0*X0+layer.B0)));
  XA[1:size(
    layer.W1,
    1)] := 1.0 ./(1.0 .+ Modelica.Math.exp(
    -(layer.W1*XA[1:size(
      layer.W0,
      1)]+layer.B1)));
  XA[1:size(
    layer.W2,
    1)] := 1.0 ./(1.0 .+ Modelica.Math.exp(
    -(layer.W2*XA[1:size(
      layer.W1,
      1)]+layer.B2)));
  XA[1:size(
    layer.W3,
    1)] := layer.W3*XA[1:size(
    layer.W2,
    1)]+layer.B3;
  XOutput := XA[1]
    annotation (Placement(transformation(extent={{-20,4},{0,24}})));
end NNFunction;
