within Workspace;
package Interfaces
  expandable connector LimitsBus
    extends.Modelica.Icons.SignalBus;
    .Modelica.SIunits.Temperature T_sst_max_limit
      "Saturated suction temprature max limit";
    .Modelica.SIunits.Temperature T_sst_min_limit_comp
      "Saturated suction temprature min limit";
    .Modelica.SIunits.Temperature T_sst_min_limit_exv
      "Saturated suction temprature min limit";
    .Modelica.SIunits.Temperature T_dgt_max_limit_comp
      "Discharge gas temperature max limit";
    .Modelica.SIunits.Temperature T_dgt_max_limit_exv
      "Discharge gas temperature max limit";
    .Modelica.SIunits.Temperature T_dgt_max_limit_fan
      "Discharge gas temperature max limit";
    .Modelica.SIunits.Temperature T_sdt_max_limit_comp
      "Suction discharge temperature max limit";
    .Modelica.SIunits.Temperature T_sdt_max_limit_fan
      "Suction discharge temperature max limit";
    .Modelica.SIunits.Temperature T_sdt_min_limit
      "Suction discharge temperature min limit";
    .Modelica.SIunits.TemperatureDifference dT_sbc_setpoint
      "Subcooling setpoint";
    .Modelica.SIunits.TemperatureDifference dT_esh_setpoint
      "Economizer superheat setpoint";
    .Modelica.SIunits.TemperatureDifference dT_dsh_min_limt
      "Discharge superheat min limit";
    .Modelica.SIunits.Power capacity_setpoint
      "Cooling capacity setpoint";
    Real VflowPumpUserSetpoint;
    Real EWTSetpoint;
    Real extPressure_SetPoint;
    Real fanSpeed_setpoint
      "Fan speed setpoint (Hz)";
    Real ecoEXV_max_opening
      "Max opening of Economizer EXV";
    Real rel_cooler_level_setpoint
      "Evap relative level control for HR";
  end LimitsBus;
  expandable connector MeasurementBus
    "MeasurmentBus with different nomenclature corresponding to the BOLTSteadyStateControls Workflow document"
    extends.Modelica.Icons.SignalBus;
    .Modelica.SIunits.Temperature T_oat
      "Outside air temperature";
    .Modelica.SIunits.Temperature T_ewt
      "Entering water temperature (evaporator)";
    .Modelica.SIunits.Temperature T_lwt
      "Leaving water temperature (evaporator)";
    .Modelica.SIunits.Temperature T_sst
      "Saturated suction temperature (compressor)";
    .Modelica.SIunits.Temperature T_sdt
      "Saturated discharge temperature (compressor)";
    .Modelica.SIunits.Temperature T_dgt
      "Discharge gas temperature (compressor)";
    .Modelica.SIunits.TemperatureDifference dT_ssh
      "Suction superheat (compressor)";
    .Modelica.SIunits.TemperatureDifference dT_esh
      "Economizer superheat (compressor)";
    .Modelica.SIunits.TemperatureDifference dT_dsh
      "Discharge superheat (compressor)";
    .Modelica.SIunits.TemperatureDifference dT_sbc
      "Subcooling (condenser)";
    .Modelica.SIunits.Power capacity
      "Cooling capacity";
    // Note that compressorFrequency is an output of compressor controller which is needed in controller settings.
    // It was added to this bus as this is more convenient than making a separate connection on a system level.
    .Modelica.SIunits.Frequency compressorFrequency
      "Compressor speed [Hz], output of a controller";
    Real External_Pressure;
    Real Vflow_coolant_pumpUser;
    Real rel_cooler_level
      "Relative level for evaporator";
  end MeasurementBus;
end Interfaces;
