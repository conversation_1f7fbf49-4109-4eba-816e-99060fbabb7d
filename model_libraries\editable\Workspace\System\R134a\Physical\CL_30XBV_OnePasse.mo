within Workspace.System.R134a.Physical;
model CL_30XBV_OnePasse
  extends.Workspace.System.BaseCycle.XBV_2C.Physical.System_30XBV(
    ModuleA(
      n_passes_evap=1),
    choiceBlock(
      SixtyHz_selector=.Workspace.Auxiliary.OptionBlock.SixtyHzOption.SixtyHz_selector.STANDARD,
      Selector_ModuleA=Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Selector.Unit_30XBV_1000,
      FAN_SPEED_selector=Workspace.Auxiliary.OptionBlock.FanSpeed.Selector.FAN_FS,
      Pump_selector=Workspace.Auxiliary.OptionBlock.PumpOption.Pump_selector.NO_PUMP),
    ECAT(
      EvapPumpSpeed_rpm(
        fixed=false),
      ExternalSystemKa_kPas2L2(
        fixed=false),
      CondExternalSystemKa_kPas2L2(
        fixed=false),
      ExternalSystemPressureDrop_Pa(
        fixed=false,
        setPoint=0),
      CondExternalSystemPressureDrop_Pa(
        fixed=false),
      HighStaticFanExternalKa_kPas2L2(
        fixed={false,false}),
      CondCoilHeatingCapacity_W(
        fixed={false,false}),
      TargetCoolingCapacity_W(
        setPoint=1000000)),
    ECO_ON_inter=3.517,
    ECO_ON_slope=0.827)
    annotation (Icon(graphics={Text(textString="CL",origin={2,-12},extent={{-100,75},{100,-75}})}));
  annotation (
    Icon(
      graphics={
        Text(
          textString="CL",
          origin={-2,-10},
          extent={{-96.01685393258424,70.66502830561285},{97,-70}})}));
end CL_30XBV_OnePasse;
