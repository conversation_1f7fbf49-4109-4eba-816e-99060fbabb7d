within Workspace.Auxiliary.OptionBlock.HighAmbiantTemperature;
function getSelector_R513A
    input .Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Selector Selector;
    input .Workspace.Auxiliary.OptionBlock.HighAmbiantTemperature.HighAmbiant_selector High_Ambiant_selector;
    output .BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector[2] VFD_Type_moduleA;
    output .BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector[2] VFD_Type_moduleB;
protected
    .BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector[:,:,:] VFD_Type_base = {{{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_110kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_110kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_132kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_132kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_132kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_132kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_200kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_132kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_200kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_132kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_200kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_200kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_250kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_250kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_315kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_200kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_315kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_250kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_315kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_315kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_132kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_132kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_200kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_132kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_200kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_132kW}},{{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_132kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_132kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_132kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_132kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_160kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_160kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_250kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_160kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_250kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_160kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_250kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_250kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_250kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_250kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_315kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_250kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_315kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_315kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_315kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_315kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_160kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_160kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_250kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_160kW},{.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_250kW,.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_160kW}}};
algorithm
    VFD_Type_moduleA := VFD_Type_base[Integer(High_Ambiant_selector),Integer(Selector),:];
    VFD_Type_moduleB := VFD_Type_base[Integer(High_Ambiant_selector),Integer(Selector),:];
end getSelector_R513A;
