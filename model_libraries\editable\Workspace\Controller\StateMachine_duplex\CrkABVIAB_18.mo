within StateMachine;
record CrkABVIAB_18
  import BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod;
  extends Mode30XBVBase(
    final modeID=ModeID30XBV.CrkABVIAB_18,
    final CrkA=true,
    final CrkB=true,
    final CrkC=false,
    final CrkD=false,
    final EcoA=false,
    final EcoB=false,
    final EcoC=false,
    final EcoD=false,
    final ViA=true,
    final ViB=true,
    final ViC=false,
    final ViD=false,
    initMethod=ModeInitMethod.External);
end CrkABVIAB_18;
