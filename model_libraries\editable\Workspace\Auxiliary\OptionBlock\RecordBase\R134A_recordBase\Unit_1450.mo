within Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase;
record Unit_1450
  extends.Workspace.Auxiliary.OptionBlock.RecordBase.UnitBase_2C(
    Capacity_design=1407782.7688411,
    CompType_CKA=.Workspace.Controller.Components.Types.CompressorSelector.NG3,
    CompType_CKB=.Workspace.Controller.Components.Types.CompressorSelector.NG3,
    selector_Comp_CKA=.Public_database.Compressor.PD_3Port.ScrewMap.Polynomial10.VI.Selector.R134a_06ZJG3,
    selector_Comp_CKB=.Public_database.Compressor.PD_3Port.ScrewMap.Polynomial10.VI.Selector.R134a_06ZJG3,
    selector_VFDA_CKA=.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_315kW,
    selector_VFDA_CKB=.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_315kW,
    CompVoltage_CKA=460,
    CompVoltage_CKB=460,
    NcompMax_CKA=78.5,
    NcompMax_CKB=78.5,
    NcompMax_coating_CKA=78.5,
    NcompMax_coating_CKB=78.5,
    NcompMin_CKA=19,
    NcompMin_CKB=19,
    Eco_Geo_CKA=.BlackBoxLibrary.GeoSelector.GeoSelector_RefRef.C62E,
    Eco_Geo_CKB=.BlackBoxLibrary.GeoSelector.GeoSelector_RefRef.C62E,
    EXV_main_A=.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector.Danfoss_ETS400,
    EXV_main_B=.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector.Danfoss_ETS400,
    EXV_eco_A=.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector.Danfoss_ETS50C,
    EXV_eco_B=.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector.Danfoss_ETS50C,
    eco_nPlate_CKA=126,
    eco_nPlate_CKB=126,
    evap_n_passes=1,
    evap_selector_tube=.Public_database.Evaporator.Tube.Selector.B4HSL_075_025_R134a,
    evap_n_tubes_pass1=172,
    evap_n_tubes_pass2=0,
    evap_diameter_in_shell=0.4572,
    evap_length_tube_CKA=2.09,
    evap_length_tube_CKB=2.09,
    nCoil_CKA=10,
    nCoil_CKB=10,
    OilSepDiameter_CKA=0.4572,
    OilSepDiameter_CKB=0.4572,
    OilSepLength_CKA=1.36,
    OilSepLength_CKB=1.36,
    Suc_lineA_DP=16000,
    Suc_lineA_MF=4.3,
    Oil_sepA_DP=15000,
    Oil_sepA_MF=4.5,
    Dis_lineA_DP=15000,
    Dis_lineA_MF=4.993983271166,
    Suc_lineB_DP=16000,
    Suc_lineB_MF=4.3,
    Oil_sepB_DP=15000,
    Oil_sepB_MF=4.5,
    Dis_lineB_DP=15000,
    Dis_lineB_MF=4.993983271166,
    eco_DportL_b_A=0.028575,
    eco_DportH_a_A=0.041275,
    eco_DportL_a_A=0.022224999999999998,
    eco_DportH_b_A=0.041275,
    eco_DportL_b_B=0.028575,
    eco_DportH_a_B=0.041275,
    eco_DportL_a_B=0.022224999999999998,
    eco_DportH_b_B=0.041275,
    evap_diameter_nozzle=0.1524,
    longEvap=true,

    //not used
    NcompMax_MEPS_ME_CKA=0,
    NcompMax_MEPS_ME_CKB=0,
    NcompMax_MEPS_ME_coating_CKA=0,
    NcompMax_MEPS_ME_coating_CKB=0,

    selector_pump_type=BOLT.InternalLibrary.Coolant.Pumps.DataBase.PumpPoly.Selector.LNE_80_160_150,
    selector_Comp_CKA_refInd=.Public_database.Compressor.PD_3Port.RefIndMap.Polynomial4.VI.Selector_VS_VI.RefInd_06ZCE1,
    selector_Comp_CKB_refInd=.Public_database.Compressor.PD_3Port.RefIndMap.Polynomial4.VI.Selector_VS_VI.RefInd_06ZCE1);
end Unit_1450;
