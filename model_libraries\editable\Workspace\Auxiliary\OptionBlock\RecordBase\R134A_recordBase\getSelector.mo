within Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase;
function getSelector
  input.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Selector Selector;
  output.Workspace.Auxiliary.OptionBlock.RecordBase.UnitBase_2C Unit;
protected
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Unit_500 Unit_30XBV_0500;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Unit_600 Unit_30XBV_0600;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Unit_700 Unit_30XBV_0700;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Unit_800 Unit_30XBV_0800;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Unit_900 Unit_30XBV_0900;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Unit_1000 Unit_30XBV_1000;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Unit_1100 Unit_30XBV_1100;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Unit_1200 Unit_30XBV_1200;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Unit_1300 Unit_30XBV_1300;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Unit_1450 Unit_30XBV_1450;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Unit_700_Duplex Unit_30XBV_0700_Duplex;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Unit_800_Duplex Unit_30XBV_0800_Duplex;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Unit_900_Duplex Unit_30XBV_0900_Duplex;
  .Workspace.Auxiliary.OptionBlock.RecordBase.UnitBase_2C[:] Unit_base={Unit_30XBV_0500,Unit_30XBV_0600,Unit_30XBV_0700,Unit_30XBV_0800,Unit_30XBV_0900,Unit_30XBV_1000,Unit_30XBV_1100,Unit_30XBV_1200,Unit_30XBV_1300,Unit_30XBV_1450,Unit_30XBV_0700_Duplex,Unit_30XBV_0800_Duplex,Unit_30XBV_0900_Duplex};
algorithm
  Unit := Unit_base[Integer(
    Selector)];
end getSelector;
