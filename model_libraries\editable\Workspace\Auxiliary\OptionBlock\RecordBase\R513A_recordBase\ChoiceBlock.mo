model ChoiceBlock
  parameter.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Selector Selector_ModuleA=.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Selector.Unit_30XBV_0500
    annotation (Dialog(group="Selector Unit"));
  parameter.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Selector Selector_ModuleB=.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Selector.Unit_30XBV_0500
    annotation (Dialog(group=" 'Selector_ModuleB' --> not to be taken into account"));
  parameter.Workspace.Auxiliary.OptionBlock.PumpOption.Pump_selector Pump_selector=.Workspace.Auxiliary.OptionBlock.PumpOption.Pump_selector.NO_PUMP
    annotation (Dialog(group="Options"),choicesAllMatching=true,Evaluate=false);
  final parameter Boolean is_Pump=.Workspace.Auxiliary.OptionBlock.PumpOption.getSelector_bool(
    Pump_selector)
    annotation (Dialog(tab="Options",group="Options"));
  parameter.Workspace.Auxiliary.OptionBlock.FanSpeed.Selector FAN_SPEED_selector=.Workspace.Auxiliary.OptionBlock.FanSpeed.Selector.FAN_VS
    annotation (Dialog(group="Options"));
  parameter.Workspace.Auxiliary.OptionBlock.HighAmbiantTemperature.HighAmbiant_selector HighAmbiant_selector=.Workspace.Auxiliary.OptionBlock.HighAmbiantTemperature.HighAmbiant_selector.STANDARD
    annotation (Dialog(group="Options"));
  parameter.Workspace.Auxiliary.OptionBlock.SixtyHzOption.SixtyHz_selector SixtyHz_selector=.Workspace.Auxiliary.OptionBlock.SixtyHzOption.SixtyHz_selector.STANDARD
    annotation (Dialog(group="Options"));
  final parameter.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector[2] VFD_Type_moduleA=.Workspace.Auxiliary.OptionBlock.HighAmbiantTemperature.getSelector_R513A(
    Selector_ModuleA,
    HighAmbiant_selector);
  final parameter Boolean is60HZ=.Workspace.Auxiliary.OptionBlock.SixtyHzOption.getSelector_bool(
    SixtyHz_selector);
  final parameter.Workspace.Auxiliary.OptionBlock.RecordBase.UnitBase_2C Unit=.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.getSelector(
    Selector_ModuleA);
  final parameter Boolean FAN_SPEED=.Workspace.Auxiliary.OptionBlock.FanSpeed.getSelector_bool(
    FAN_SPEED_selector);
  final parameter Boolean isHighAmbiant=.Workspace.Auxiliary.OptionBlock.HighAmbiantTemperature.getSelector_bool(
    HighAmbiant_selector);
  parameter.Workspace.Auxiliary.OptionBlock.CoatingOption.Coating_selector Coating_selector=.Workspace.Auxiliary.OptionBlock.CoatingOption.Coating_selector.STANDARD
    annotation (Dialog(group="Options"));
  final parameter Boolean isCoatingOption=.Workspace.Auxiliary.OptionBlock.CoatingOption.getSelector_bool(
    Coating_selector);
  parameter.Workspace.Auxiliary.OptionBlock.MEPS_ME.MEPS_ME_selector MEPS_ME_selector=.Workspace.Auxiliary.OptionBlock.MEPS_ME.MEPS_ME_selector.STANDARD
    annotation (Dialog(group="Options"));
  final parameter Boolean isMEPS_ME=.Workspace.Auxiliary.OptionBlock.MEPS_ME.getSelector_bool(
    MEPS_ME_selector);
    
    
  annotation (
    Icon(
      graphics={
        Rectangle(
          extent={{-100,-100},{100,100}},
          fillColor={103,25,25},
          fillPattern=FillPattern.Solid),
        Text(
          textString="ChoiceBlock",
          origin={-3,-5},
          extent={{-99,41},{99,-41}},
          lineColor={255,255,255})}));
end ChoiceBlock;
