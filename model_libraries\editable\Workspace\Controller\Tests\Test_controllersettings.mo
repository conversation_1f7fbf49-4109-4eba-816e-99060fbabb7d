within KAV_initiation.System30KAV.Controller30KAV.Tests;
model Test_controllersettings
  ControllerSettings controllerSettings
    annotation (Placement(transformation(extent={{60,-10},{80,10}})));
  .Modelica.Blocks.Sources.RealExpression comp_freq(
    y=23)
    annotation (Placement(transformation(extent={{50.96034484924278,94.0},{81.03965515075723,114.0}},origin={-124,-58},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sst(
    y=5+273.15)
    annotation (Placement(transformation(extent={{50.96034484924278,94.0},{81.03965515075723,114.0}},origin={-124,-76},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sdt(
    y=55+273.15)
    annotation (Placement(transformation(extent={{50.96034484924278,94.0},{81.03965515075723,114.0}},origin={-124,-94},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression oat(
    y=30+273.15)
    annotation (Placement(transformation(extent={{50.96034484924278,94.0},{81.03965515075723,114.0}},origin={-124,-114},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression lwt(
    y=7+273.15)
    annotation (Placement(transformation(extent={{50.96034484924278,94.0},{81.03965515075723,114.0}},origin={-124,-136},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-48,-9},{-32,9}},origin={50,11},rotation=0.0)));
equation
  connect(comp_freq.y,measurementBus.compressorFrequency)
    annotation (Line(points={{-41.4564,46},{-16,46},{-16,11.045},{10.04,11.045}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(measurementBus,controllerSettings.measurementBus)
    annotation (Line(points={{10,11},{36,11},{36,0},{60,0}},color={255,204,51},thickness=0.5),Text(string="%first",index=-1,extent={{-6,3},{-6,3}}));
  connect(sst.y,measurementBus.T_sst)
    annotation (Line(points={{-41.4564,28},{-16,28},{-16,11.045},{10.04,11.045}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(sdt.y,measurementBus.T_sdt)
    annotation (Line(points={{-41.4564,10},{-16,10},{-16,11.045},{10.04,11.045}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(oat.y,measurementBus.T_oat)
    annotation (Line(points={{-41.4564,-10},{-16,-10},{-16,11.045},{10.04,11.045}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  connect(lwt.y,measurementBus.T_lwt)
    annotation (Line(points={{-41.4564,-32},{-16,-32},{-16,11.045},{10.04,11.045}},color={0,0,127}),Text(string="%second",index=1,extent={{6,3},{6,3}}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false)),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)));
end Test_controllersettings;
