within StateMachine;
record CrkAB_17
  import BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod;
  extends Mode30XBVBase(
    final modeID=ModeID30XBV.CrkAB_17,
    final CrkA=true,
    final CrkB=true,
    final CrkC=false,
    final CrkD=false,
    final EcoA=false,
    final EcoB=false,
    final EcoC=false,
    final EcoD=false,
    final ViA=false,
    final ViB=false,
    final ViC=false,
    final ViD=false,
    initMethod=ModeInitMethod.External);
end CrkAB_17;
