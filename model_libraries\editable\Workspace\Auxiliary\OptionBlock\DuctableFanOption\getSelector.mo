within Workspace.Auxiliary.OptionBlock.DuctableFanOption;
function getSelector
  input.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Selector UnitSelector=.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Selector.Unit_500;
  input Selector selector=.Workspace.Auxiliary.OptionBlock.DuctableFanOption.Selector.NONE;
  output Real Ka;
protected
  Real[:,:] Ka_Base=[
    1e-10,106.356001717794;
    1e-10,106.611714920404;
    1e-10,97.7884177481623;
    1e-10,97.9034922046771;
    1e-10,29.7236950897306;
    1e-10,29.7557800689296;
    1e-10,29.8060856878303;
    1e-10,31.4572569569502;
    1e-10,31.5007240211804;
    1e-10,29.7146775832434];
algorithm
  Ka := Ka_Base[Integer(
    UnitSelector),Integer(
    selector)];
end getSelector;
