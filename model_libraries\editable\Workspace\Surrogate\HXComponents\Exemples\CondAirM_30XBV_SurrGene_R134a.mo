within Workspace.Surrogate.HXComponents.Exemples;
model CondAirM_30XBV_SurrGene_R134a
  extends.BOLT.InternalLibrary.BuildingBlocks.Icons.Example;
  RefAir_MCHX.CondAirM_30XBV condAirM_30XBV(
    tubeOrientation=.BOLT.InternalLibrary.BuildingBlocks.Types.TubeOrientation.Vertical_inletTop,
    n_slabs=1,
    n_tubes={{90,30}},
    userDefined_geo(
      length_tube=1.937,
      diameter_in_header=0.02395,
      length_tube_inlet=0.02286,
      diameter_in_inletTube=0.0157,
      length_outlet=0.02286,
      diameter_in_outlet=0.0157),
    selector_curve_num=106,
    nCoils=1)
    annotation (Placement(transformation(extent={{-53.0,-16.0},{17.0,52.0}},rotation=0.0,origin={0.0,0.0})));
  .BOLT.BoundaryNode.Refrigerant.Source RefIn(
    m_flow_set=1,
    Tsat_fixed=false,
    T_fixed=false,
    x_set=1.54882291)
    annotation (Placement(transformation(extent={{-6.459106834751182,-6.459106834751182},{6.459106834751182,6.459106834751182}},origin={-94.8696792944757,44.53973349565522},rotation=-90.0)));
  .BOLT.BoundaryNode.Refrigerant.Sink RefOut(
    p_fixed=false,
    x_fixed=true,
    x_set=-0.252564386)
    annotation (Placement(transformation(extent={{7.0738408075374934,-7.07384080753749},{-7.0738408075374934,7.07384080753749}},origin={61.1303207055243,-11.46026650434478},rotation=-90.0)));
  .BOLT.BoundaryNode.Air.Source AirIn(
    Tdb_set=271.7454216,
    Vds_flow_set=2,
    Vds_flow_fixed=false,
    Vd_flow_fixed=true,
    Vd_flow_set=5.88247395)
    annotation (Placement(transformation(extent={{-81.26004671639222,-45.850633926261295},{-68.47931187255918,-33.06989908242825}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Air.Sink AirOut(
    pg_set=0)
    annotation (Placement(transformation(extent={{-6.114381372769246,6.114381372769245},{6.114381372769246,-6.114381372769245}},origin={28.609037883427106,70.38079951303433},rotation=180.0)));
equation
  connect(RefIn.port,condAirM_30XBV.port_a_ref)
    annotation (Line(points={{-94.8696792944757,38.08062666090404},{-94.8696792944757,18},{-64.9,18}},color={255,0,0}));
  connect(condAirM_30XBV.port_b_ref,RefOut.port)
    annotation (Line(points={{17,18},{61.1303207055243,18},{61.1303207055243,-4.386425696807287}},color={255,0,0}));
  connect(AirIn.port,condAirM_30XBV.port_a_air)
    annotation (Line(points={{-68.47931187255918,-39.46026650434477},{-18,-39.46026650434477},{-18,-11.240000000000002}},color={0,0,255}));
  connect(condAirM_30XBV.port_b_air,AirOut.port)
    annotation (Line(points={{-18,47.92},{-18,70.38079951303433},{22.494656510657858,70.38079951303433}},color={0,0,255}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end CondAirM_30XBV_SurrGene_R134a;
