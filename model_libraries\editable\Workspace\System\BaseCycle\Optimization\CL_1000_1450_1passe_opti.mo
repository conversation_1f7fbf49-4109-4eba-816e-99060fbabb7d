within Workspace.System.BaseCycle.Optimization;
model CL_1000_1450_1passe_opti
  extends.Workspace.System.BaseCycle.Optimization.System_XBV_opti(
    equipment(
      n_passes_evap=1,
      nodecondAiroutA(
        dTsh_fixed=false),
      globalParameters(
        capacity_design=900000),
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a,
      condAirA(
        use_Z_fanScaling=false),
      condAirB(
        use_Z_fanScaling=false)),
    choiceBlock(
      Selector=.Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Selector.Unit_1000,
      FAN_SPEED_selector=.Workspace.Auxiliary.OptionBlock.FanSpeed.Selector.FAN_VS),
    UseStateMachine=true,
    controllerSettings_crkA(
      capacity_setpoint=targetCapacity,
      dT_sbc_max=5),
    controllerSettings_crkB(
      capacity_setpoint=targetCapacity,
      dT_sbc_max=5),
    targetCapacity=ECAT.TargetCoolingCapacity_W.setPoint,
    ECAT(
      EvapBrineType_nd=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW,
      CondBrineType_nd=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW,
      RefrigerantType_nd=.BOLT.InternalLibrary.ECATBlock.Types.RefrigerantTypes.R134a));
  annotation (
    Icon(
      graphics={
        Text(
          textString="Text",
          origin={44,-30},
          extent={{0.6816829616425224,0.2303023726851734},{0,0}}),
        Text(
          textString="CL",
          extent={{-98,75},{98,-75}},
          lineColor={255,255,255},
          origin={0,-11})}));
end CL_1000_1450_1passe_opti;
