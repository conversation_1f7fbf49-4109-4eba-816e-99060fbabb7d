within Workspace.Controller.SubSystems.BaseClasses;
model LoadDistribution_DesignCapa_duplex
  parameter Real capacityDesign_moduleA_max
    "Minimum capacity design of module A (w)"
    annotation (Dialog(group="capacity design"));
  parameter Real capacityDesign_moduleB_max
    "Minimum capacity design of module B (w)"
    annotation (Dialog(group="capacity design"));
  parameter Boolean isOffModuleB;
  Modelica.Blocks.Interfaces.RealInput ControllerSignal
    // Target capacity 
    annotation (Placement(transformation(extent={{-110.0,8.0},{-70.0,48.0}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-46.4}},rotation=180,origin={-30,-38})));
  .Modelica.Blocks.Interfaces.RealInput EWT
    annotation (Placement(transformation(extent={{-108.0,-30.0},{-68.0,10.0}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-66.4}},rotation=180,origin={-30,-38})));
  .Modelica.Blocks.Interfaces.RealInput LWT
    annotation (Placement(transformation(extent={{-106.0,-66.0},{-66.0,-26.0}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{72.8,-30.4},{56.8,-26.4}},rotation=180,origin={-30,-38})));

  Real SetPoint_capacity_moduleA=
    if isOffModuleB then
      ControllerSignal
    else
      ControllerSignal*((2+0.025*(EWT-LWT))/(4+0.025*(EWT-LWT)));
  Real SetPoint_capacity_moduleB=
    if isOffModuleB then
      0
    else
      ControllerSignal-SetPoint_capacity_moduleA;
  //annotation(Icon(graphics = {Rectangle(origin={-1,41},extent={{-79,57},{79,-57}},fillPattern=FillPattern.Solid,fillColor={98,27,27})}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=true,
        extent={{-100,-100},{100,100}}),
      graphics={
        Line(
          points={{100,60},{60,60},{0,0}},
          color={0,0,127}),
        Line(
          points={{100,-60},{60,-60},{0,0}},
          color={0,0,127}),
        Line(
          points={{-100,0},{0,0}},
          color={0,0,127}),
        Rectangle(
          extent={{-40,40},{40,-40}},
          lineColor={0,0,0},
          fillColor={235,235,235},
          fillPattern=FillPattern.Solid),
        Text(
          extent={{-100,140},{100,100}},
          lineColor={28,108,200},
          textString="%name")}));
end LoadDistribution_DesignCapa_duplex;
