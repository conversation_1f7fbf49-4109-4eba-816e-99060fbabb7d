within KAV_initiation.System30KAV.Controller30KAV.SubSystems.Tests.BaseClasses;
model EXVControlBaseTest
  .Workspace.Controller.SubSystems.EXVControl eXVControl
    annotation (Placement(transformation(extent={{-27.178801759966298,-11.178801759966298},{39.1788017599663,55.1788017599663}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.Ramp dT_sbc(
    height=0,
    duration=10,
    offset=5,
    startTime=25)
    annotation (Placement(transformation(extent={{-98,130},{-78,150}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.Ramp T_sst(
    height=0,
    duration=10,
    offset=280,
    startTime=25)
    annotation (Placement(transformation(extent={{-98,98},{-78,118}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.Ramp dT_dsh(
    height=0,
    duration=10,
    offset=7,
    startTime=25)
    annotation (Placement(transformation(extent={{-98,64},{-78,84}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Sources.Ramp T_dgt(
    height=0,
    duration=10,
    offset=367.15,
    startTime=25)
    annotation (Placement(transformation(extent={{-98.0,32.0},{-78.0,52.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_sbc_setpoint(
    y=5)
    annotation (Placement(transformation(extent={{-95.83332112552287,-7.833321125522852},{-76.16667887447713,11.833321125522852}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_exv(
    y=273.15+0.5)
    annotation (Placement(transformation(extent={{-96.0,-22.0},{-76.0,-2.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_max_limit(
    y=290.15)
    annotation (Placement(transformation(extent={{-96.0,-36.0},{-76.0,-16.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_dsh_min_limit(
    y=7)
    annotation (Placement(transformation(extent={{-96.0,-50.0},{-76.0,-30.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_exv(
    y=367.15)
    annotation (Placement(transformation(extent={{-96.0,-64.0},{-76.0,-44.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal
    annotation (Placement(transformation(extent={{72,12},{92,32}},origin={18,0},rotation=0)));
equation
  connect(dT_dsh.y,eXVControl.measurementBus.dT_dsh)
    annotation (Line(points={{-77,74},{-40,74},{-40,48},{-27.178801759966298,48},{-27.178801759966298,41.90728105597978}},color={0,0,127}));
  connect(T_dgt.y,eXVControl.measurementBus.T_dgt)
    annotation (Line(points={{-77,42},{-27.178801759966298,42},{-27.178801759966298,41.90728105597978}},color={0,0,127}));
  connect(T_dgt_max_limit_exv.y,eXVControl.limitsBus.T_dgt_max_limit_exv)
    annotation (Line(points={{-75,-54},{-40,-54},{-40,2.09271894402022},{-27.178801759966305,2.09271894402022}},color={0,0,127}));
  connect(T_sst_min_limit_exv.y,eXVControl.limitsBus.T_sst_min_limit_exv)
    annotation (Line(points={{-75,-12},{-40,-12},{-40,2.09271894402022},{-27.178801759966305,2.09271894402022}},color={0,0,127}));
  connect(T_sst_max_limit.y,eXVControl.limitsBus.T_sst_max_limit)
    annotation (Line(points={{-75,-26},{-40,-26},{-40,2.09271894402022},{-27.178801759966305,2.09271894402022}},color={0,0,127}));
  connect(T_sst.y,eXVControl.measurementBus.T_sst)
    annotation (Line(points={{-77,108},{-40,108},{-40,41.90728105597978},{-27.178801759966298,41.90728105597978}},color={0,0,127}));
  connect(dT_sbc.y,eXVControl.measurementBus.dT_sbc)
    annotation (Line(points={{-77,140},{-40,140},{-40,41.90728105597978},{-27.178801759966298,41.90728105597978}},color={0,0,127}));
  connect(eXVControl.actuatorSignal,actuatorSignal)
    annotation (Line(points={{39.178801759966305,22},{100,22}},color={0,0,127}));
  connect(dT_sbc_setpoint.y,eXVControl.limitsBus.dT_sbc_setpoint)
    annotation (Line(points={{-75.18334676192484,2},{-52.50825036358461,2},{-52.50825036358461,2.09271894402022},{-27.178801759966305,2.09271894402022}},color={0,0,127}));
  connect(dT_dsh_min_limit.y,eXVControl.limitsBus.dT_dsh_min_limt)
    annotation (Line(points={{-75,-40},{-40,-40},{-40,2.09271894402022},{-27.178801759966305,2.09271894402022}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}),
    experiment(
      StopTime=60));
end EXVControlBaseTest;
