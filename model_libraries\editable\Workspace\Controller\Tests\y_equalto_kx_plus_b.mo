within KAV_initiation.System30KAV.Controller30KAV.Tests;
model y_equalto_kx_plus_b
  /* size of K matrix is defined below. Don't change or remove this line that is an indicator for python scripts.*/ parameter Integer n=8
    "Length of x";
  parameter Integer m=13
    "Length of y";
  /* size of K matrix is defined above. Don't change or remove this line that is an indicator for python scripts.*//* K matrix is defined below. Don't change or remove this line that is an indicator for python scripts.*/parameter Real K[m,n]={{2777.77777778,0.,0.,0.,2564.1025641,0.,0.,0.},{-0.23611111,17.,0.,0.,0.,0.,0.,0.},{0.93055556,0.,0.,-67.,0.,0.,0.,0.},{1.30555556,-94.,0.,-94.,0.,0.,0.,0.},{0.,5.,0.,0.,0.,0.,0.,0.},{0.,-7.,0.,0.,0.,0.,0.,0.},{0.,0.,-10.,0.,0.,0.,0.,0.},{0.,0.,0.,0.,-0.21794872,17.,0.,0.},{0.,0.,0.,0.,0.85897436,0.,0.,-67.},{0.,0.,0.,0.,1.20512821,-94.,0.,-94.},{0.,0.,0.,0.,0.,5.,0.,0.},{0.,0.,0.,0.,0.,-7.,0.,0.},{0.,0.,0.,0.,0.,0.,-10.,0.}}
    "K matrix in y = k * (x - xmin) + b";
  /* K matrix is defined above. Don't change or remove this line that is an indicator for python scripts.*//* b vector is defined below. Don't change or remove this line that is an indicator for python scripts.*/parameter Real b[m]={1.50e+05,5.00e-01,6.50e+01,9.30e+01,3.75e+00,5.25e+00,7.50e+00,5.00e-01,6.50e+01,9.30e+01,3.75e+00,5.25e+00,7.50e+00}
    "b vector in y = k * (x - xmin) + b";
  /* b vector is defined above. Don't change or remove this line that is an indicator for python scripts.*//* xmin vector is defined below. Don't change or remove this line that is an indicator for python scripts.*/parameter Real xmin[n]={23.,0.,0.,0.,27.,0.,0.,0.}
    "xmin vector in y = k * (x - xmin) + b";
  /* xmin vector is defined above. Don't change or remove this line that is an indicator for python scripts.*/Modelica.Blocks.Interfaces.RealInput x[n]
    annotation (Placement(transformation(extent={{-84,-12},{-60,12}}),iconTransformation(extent={{-56,-8},{-42,6}})));
  Modelica.Blocks.Interfaces.RealOutput y[m]
    annotation (Placement(transformation(extent={{50,-10},{70,10}}),iconTransformation(extent={{44,-8},{58,6}})));
  parameter Real cf_K=1;
algorithm
  y := cf_K*K*(x-xmin)+b;
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false),
      graphics={
        Rectangle(
          extent={{-42,36},{44,-36}},
          lineColor={28,108,200}),
        Text(
          extent={{-32,20},{32,-24}},
          lineColor={28,108,200},
          textString="y= K*x + b")}),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)),
    uses(
      Modelica(
        version="3.2.2")));
end y_equalto_kx_plus_b;
