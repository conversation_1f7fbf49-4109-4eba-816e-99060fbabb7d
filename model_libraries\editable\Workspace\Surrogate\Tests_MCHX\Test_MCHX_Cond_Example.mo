within Workspace.Surrogate.Tests_MCHX;
model Test_MCHX_Cond_Example
  .Workspace.Surrogate.CondAirM_Surrogate MCHX(
    selector=Workspace.Surrogate.MCHX_Cond.Selector.MCHX_Cond_R134a,
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a,
    redeclare replaceable package AirMedium=.BOLT.InternalLibrary.Media.Air.MoistAir,
    use_ZU_in=false,
    use_Z_fanScaling=false,
    isOff_ref=false,
    isOff=false,
    Z_dpr=1.35,
    Z_U=0.67,
    nCoils=5)
    annotation (Placement(transformation(extent={{7.416148277459065,-12.583851722540935},{52.583851722540935,32.583851722540935}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Refrigerant.Source RefIn(
    m_flow_set=3,
    x_set=1.53)
    annotation (Placement(transformation(extent={{-53.778867479952154,4.221132520047846},{-42.221132520047846,15.778867479952154}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Refrigerant.Sink RefOut(
    p_fixed=false,
    x_fixed=true,
    x_set=-0.157)
    annotation (Placement(transformation(extent={{107.29383119171627,4.70616880828373},{96.70616880828373,15.29383119171627}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Air.Source AirIn(
    Vds_flow_fixed=false,
    Tdb_set=300,
    Vd_flow_fixed=true,
    Vd_flow_set=0.6,
    Vds_flow_set=3)
    annotation (Placement(transformation(extent={{2.3878639479097608,-57.612136052090236},{13.61213605209024,-46.387863947909764}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Air.Sink AirOut(
    pg_fixed=true,
    pg_set=0.01)
    annotation (Placement(transformation(extent={{-5.68148412273816,60.318515877261845},{5.68148412273816,71.68148412273815}},origin={0.0,0.0},rotation=0.0)));
  inner.BOLT.GlobalParameters globalParameters
    annotation (Placement(transformation(extent={{-100,106},{-80,126}},origin={0,0},rotation=0)));
equation
  connect(MCHX.port_a_air,AirIn.port)
    annotation (Line(points={{30,-12.583851722540935},{30,-52},{13.61213605209024,-52}},color={0,0,255}));
  connect(AirOut.port,MCHX.port_b_air)
    annotation (Line(points={{5.68148412273816,66},{30,66},{30,32.583851722540935}},color={0,0,255}));
  connect(RefIn.port,MCHX.port_a_ref)
    annotation (Line(points={{-42.221132520047846,10},{7.416148277459065,10}},color={255,0,0}));
  connect(RefOut.port,MCHX.port_b_ref)
    annotation (Line(points={{96.70616880828373,10},{52.583851722540935,10}},color={255,0,0}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Test_MCHX_Cond_Example;
