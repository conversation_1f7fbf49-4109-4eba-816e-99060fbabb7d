within Workspace.Controller.SubSystems.Tests.LoadDistribution_duplex;
model test_LoadDistribution_duplex
  .Modelica.Blocks.Sources.RealExpression moduleA_min2(
    y=crkA_min_speed)
    annotation (Placement(transformation(extent={{160.15365714285713,254.45877366071426},{180.15365714285713,274.45877366071426}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Gain gainA2(
    k=crkA_max_speed-crkA_min_speed)
    annotation (Placement(transformation(extent={{159.04000357274344,226.84623616202916},{182.41016785582792,250.21640044511358}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Add addA2
    annotation (Placement(transformation(extent={{208.72508571428568,248.53131830357137},{228.72508571428568,268.53131830357137}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput compressorFrequency
    annotation (Placement(transformation(extent={{258.43937142857135,248.5302022321428},{278.43937142857135,268.5302022321428}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Gain gainB2(
    k=crkB_max_speed-crkB_min_speed)
    annotation (Placement(transformation(extent={{159.2542892870291,146.2826200906006},{182.6244535701136,169.65278437368502}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression moduleB_min2(
    y=crkB_min_speed)
    annotation (Placement(transformation(extent={{162.65365714285713,117.4587736607142},{182.65365714285713,137.4587736607142}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Add addB2
    annotation (Placement(transformation(extent={{212.65365714285713,123.4587736607142},{232.65365714285713,143.4587736607142}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal
    annotation (Placement(transformation(extent={{481.50908571428556,251.18731830357132},{501.50908571428556,271.1873183035713}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{365.3120560639413,197.11828865322713},{406.63811536463004,238.4443479539156}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression totalCapacity(
    y=300000)
    annotation (Placement(transformation(extent={{260.7849151900439,198.59114777932953},{300.6652562385275,238.4714888278132}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController(
    AV_start=1,
    AV_max=crkA_max_speed,
    AV_min=crkA_min_speed)
    annotation (Placement(transformation(extent={{402.7250857142857,252.53131830357137},{422.7250857142857,272.53131830357137}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation compressorFrequency_error(
    setPoint=compressorFrequency,
    gain=1/crkA_max_speed,
    measurement=actuatorSignal,
    ID=1)
    annotation (Placement(transformation(extent={{302.61714631809,250.0},{357.38285368191,270.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController2(
    AV_min=0,
    AV_max=1,
    AV_start=1)
    annotation (Placement(transformation(extent={{106.72508571428568,202.53131830357137},{126.72508571428568,222.53131830357137}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation compressorFrequency_error2(
    ID=1,
    measurement=totalCapacity.y+totalCapacityB.y,
    gain=1/(500000),
    setPoint=test_norm.SetPoint_capacity_moduleA)
    annotation (Placement(transformation(extent={{35.34223203237573,172.53131830357137},{90.10793939619563,192.53131830357137}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput compressorFrequencyB
    annotation (Placement(transformation(extent={{260.7250857142857,122.53131830357137},{280.7250857142857,142.53131830357137}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController3(
    AV_start=1,
    AV_max=crkB_max_speed,
    AV_min=crkB_min_speed)
    annotation (Placement(transformation(extent={{416.32864126984134,127.27442941468252},{436.32864126984134,147.27442941468252}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal2
    annotation (Placement(transformation(extent={{493.92330793650797,126.15976274801582},{513.923307936508,146.15976274801582}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation compressorFrequency_error3(
    setPoint=compressorFrequencyB,
    gain=1/(crkB_max_speed),
    measurement=actuatorSignal2,
    ID=1)
    annotation (Placement(transformation(extent={{310.540454254598,126.15976274801582},{365.30616161841795,146.15976274801582}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBusB
    annotation (Placement(transformation(extent={{366.3120560639413,162.11828865322713},{407.63811536463004,203.4443479539156}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression totalCapacityB(
    y=300000)
    annotation (Placement(transformation(extent={{260.7849151900439,160.59114777932953},{300.6652562385275,200.4714888278132}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Actuator actuator(
    setPoint=800000,
    minBound=0,
    maxBound=100000000)
    annotation (Placement(transformation(extent={{-10.0,-10.0},{10.0,10.0}},origin={-45.0,80.0},rotation=-180.0)));
  .Workspace.Controller.SubSystems.Tests.LoadDistribution_duplex.test_norm test_norm(
    capacityDesign_moduleB_max=10)
    annotation (Placement(transformation(extent={{-13.0,66.0},{7.0,86.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression moduleA_min(
    y=crkA_min_speed)
    annotation (Placement(transformation(extent={{168.0899114285715,50.48533957589285},{188.0899114285715,70.48533957589285}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Gain gainA(
    k=crkA_max_speed-crkA_min_speed)
    annotation (Placement(transformation(extent={{166.9762578584578,22.872802077207766},{190.3464221415423,46.24296636029216}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Add addA
    annotation (Placement(transformation(extent={{216.66134000000005,44.55788421874996},{236.66134000000005,64.55788421874996}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput compressorFrequency2
    annotation (Placement(transformation(extent={{266.3756257142857,44.55676814732141},{286.3756257142857,64.5567681473214}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Gain gainB(
    k=crkB_max_speed-crkB_min_speed)
    annotation (Placement(transformation(extent={{167.19054357274348,-57.690813994220804},{190.56070785582796,-34.32064971113638}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression moduleB_min(
    y=crkB_min_speed)
    annotation (Placement(transformation(extent={{170.5899114285715,-86.5146604241072},{190.5899114285715,-66.5146604241072}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Add addB
    annotation (Placement(transformation(extent={{220.5899114285715,-80.5146604241072},{240.5899114285715,-60.514660424107205}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal3
    annotation (Placement(transformation(extent={{489.44533999999993,47.21388421874991},{509.44533999999993,67.21388421874991}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus2
    annotation (Placement(transformation(extent={{373.2483103496557,-6.855145431594281},{414.5743696503444,34.470913869094204}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression totalCapacity2(
    y=300000)
    annotation (Placement(transformation(extent={{268.72116947575824,-5.382286305491874},{308.60151052424186,34.4980547429918}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController4(
    AV_min=crkA_min_speed,
    AV_max=crkA_max_speed,
    AV_start=1)
    annotation (Placement(transformation(extent={{410.66134000000005,48.55788421874996},{430.66134000000005,68.55788421874996}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation compressorFrequency_error4(
    ID=1,
    measurement=actuatorSignal,
    gain=1/crkA_max_speed,
    setPoint=compressorFrequency)
    annotation (Placement(transformation(extent={{310.5534006038044,46.026565915178594},{365.31910796762435,66.0265659151786}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController5(
    AV_start=1,
    AV_max=1,
    AV_min=0)
    annotation (Placement(transformation(extent={{114.66134000000005,-1.4421157812500383},{134.66134000000005,18.55788421874996}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation compressorFrequency_error5(
    setPoint=test_norm.SetPoint_capacity_moduleB,
    gain=1/(500000),
    measurement=totalCapacity.y+totalCapacityB.y,
    ID=1)
    annotation (Placement(transformation(extent={{44.61714631809005,-32.0},{99.38285368190995,-12.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput compressorFrequencyB2
    annotation (Placement(transformation(extent={{268.66134000000005,-81.44211578125004},{288.66134000000005,-61.44211578125004}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController6(
    AV_min=crkB_min_speed,
    AV_max=crkB_max_speed,
    AV_start=1)
    annotation (Placement(transformation(extent={{424.2648955555557,-76.69900467013889},{444.2648955555557,-56.69900467013889}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal4
    annotation (Placement(transformation(extent={{501.85956222222234,-77.81367133680558},{521.8595622222224,-57.813671336805584}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation compressorFrequency_error6(
    ID=1,
    measurement=actuatorSignal2,
    gain=1/(crkB_max_speed),
    setPoint=compressorFrequencyB)
    annotation (Placement(transformation(extent={{318.47670854031236,-77.81367133680558},{373.2424159041323,-57.813671336805584}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBusB2
    annotation (Placement(transformation(extent={{374.2483103496557,-41.85514543159428},{415.5743696503444,-0.5290861309057959}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression totalCapacityB2(
    y=300000)
    annotation (Placement(transformation(extent={{268.72116947575824,-43.382286305491874},{308.60151052424186,-3.5019452570082024}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(gainA2.y,addA2.u2)
    annotation (Line(points={{183.57867606998212,238.53131830357137},{196.58045232070532,238.53131830357137},{196.58045232070532,252.53131830357137},{206.72508571428568,252.53131830357137}},color={0,0,127}));
  connect(moduleA_min2.y,addA2.u1)
    annotation (Line(points={{181.15365714285713,264.4587736607142},{206.72508571428568,264.4587736607142},{206.72508571428568,264.53131830357137}},color={0,0,127}));
  connect(gainB2.y,addB2.u1)
    annotation (Line(points={{183.79296178426785,157.9677022321428},{198.08045232070532,157.9677022321428},{198.08045232070532,139.45877366071426},{210.65365714285713,139.45877366071426}},color={0,0,127}));
  connect(moduleB_min2.y,addB2.u2)
    annotation (Line(points={{183.65365714285713,127.45877366071426},{210.65365714285713,127.45877366071426}},color={0,0,127}));
  connect(addB2.y,compressorFrequencyB)
    annotation (Line(points={{233.65365714285713,133.45877366071426},{270.7250857142857,133.45877366071426},{270.7250857142857,132.53131830357137}},color={0,0,127}));
  connect(actuatorSignal,measurementBus.compressorFrequency)
    annotation (Line(points={{491.5090857142858,261.1873183035713},{516.7250857142857,261.1873183035713},{516.7250857142857,217.78131830357137},{385.9750857142857,217.78131830357137}},color={0,0,127}));
  connect(totalCapacity.y,measurementBus.capacity)
    annotation (Line(points={{302.65927329095166,218.53131830357137},{302.65927329095166,217.78131830357137},{385.9750857142857,217.78131830357137}},color={0,0,127}));
  connect(compressorFrequency_error.sensor,setpointController.errorSignal)
    annotation (Line(points={{356.83519660827176,259.2},{402.7250857142857,259.2},{402.7250857142857,262.53131830357137}},color={28,108,200}));
  connect(setpointController.actuatorSignal,actuatorSignal)
    annotation (Line(points={{423.3250857142857,262.53131830357137},{491.50908571428556,262.53131830357137},{491.50908571428556,261.1873183035713}},color={0,0,127}));
  connect(addA2.y,compressorFrequency)
    annotation (Line(points={{229.72508571428568,258.53131830357137},{229.72508571428568,258.5302022321428},{268.43937142857135,258.5302022321428}},color={0,0,127}));
  connect(compressorFrequency_error2.sensor,setpointController2.errorSignal)
    annotation (Line(points={{89.56028232255744,181.73131830357138},{97.14268401842156,181.73131830357138},{97.14268401842156,212.53131830357137},{106.72508571428568,212.53131830357137}},color={28,108,200}));
  connect(setpointController2.actuatorSignal,gainA2.u)
    annotation (Line(points={{127.3250857142857,212.53131830357137},{141.7283221436461,212.53131830357137},{141.7283221436461,238.53131830357137},{156.70298714443499,238.53131830357137}},color={0,0,127}));
  connect(setpointController2.actuatorSignal,gainB2.u)
    annotation (Line(points={{127.3250857142857,212.53131830357137},{141.1211792865032,212.53131830357137},{141.1211792865032,157.9677022321428},{156.9172728587207,157.9677022321428}},color={0,0,127}));
  connect(compressorFrequency_error3.sensor,setpointController3.errorSignal)
    annotation (Line(points={{364.7585045447797,135.3597627480158},{416.32864126984134,135.3597627480158},{416.32864126984134,137.27442941468252}},color={28,108,200}));
  connect(setpointController3.actuatorSignal,actuatorSignal2)
    annotation (Line(points={{436.92864126984136,137.27442941468252},{503.92330793650797,137.27442941468252},{503.92330793650797,136.15976274801582}},color={0,0,127}));
  connect(actuatorSignal2,measurementBusB.compressorFrequency)
    annotation (Line(points={{503.92330793650797,136.15976274801582},{522.7250857142857,136.15976274801582},{522.7250857142857,182.78131830357137},{386.9750857142857,182.78131830357137}},color={0,0,127}));
  connect(totalCapacityB.y,measurementBusB.capacity)
    annotation (Line(points={{302.65927329095166,180.53131830357137},{386.9750857142857,180.53131830357137},{386.9750857142857,182.78131830357137}},color={0,0,127}));
  connect(actuator.value,test_norm.ControllerSignal)
    annotation (Line(points={{-40,80},{-34.5,80},{-34.5,80.19999999999999},{-13,80.19999999999999}},color={0,0,127}));
  connect(gainA.y,addA.u2)
    annotation (Line(points={{191.5149303556965,34.55788421874996},{204.5167066064197,34.55788421874996},{204.5167066064197,48.55788421874996},{214.66134000000005,48.55788421874996}},color={0,0,127}));
  connect(moduleA_min.y,addA.u1)
    annotation (Line(points={{189.0899114285715,60.485339575892795},{214.66134000000005,60.485339575892795},{214.66134000000005,60.55788421874996}},color={0,0,127}));
  connect(gainB.y,addB.u1)
    annotation (Line(points={{191.72921606998221,-46.00573185267859},{206.0167066064197,-46.00573185267859},{206.0167066064197,-64.51466042410715},{218.5899114285715,-64.51466042410715}},color={0,0,127}));
  connect(moduleB_min.y,addB.u2)
    annotation (Line(points={{191.5899114285715,-76.51466042410715},{218.5899114285715,-76.51466042410715}},color={0,0,127}));
  connect(addB.y,compressorFrequencyB2)
    annotation (Line(points={{241.5899114285715,-70.51466042410715},{278.66134000000005,-70.51466042410715},{278.66134000000005,-71.44211578125004}},color={0,0,127}));
  connect(actuatorSignal3,measurementBus2.compressorFrequency)
    annotation (Line(points={{499.44534000000016,57.21388421874991},{524.6613400000001,57.21388421874991},{524.6613400000001,13.807884218749962},{393.91134000000005,13.807884218749962}},color={0,0,127}));
  connect(totalCapacity2.y,measurementBus2.capacity)
    annotation (Line(points={{310.59552757666603,14.557884218749962},{310.59552757666603,13.807884218749962},{393.91134000000005,13.807884218749962}},color={0,0,127}));
  connect(compressorFrequency_error4.sensor,setpointController4.errorSignal)
    annotation (Line(points={{364.7714508939861,55.22656591517858},{410.66134000000005,55.22656591517858},{410.66134000000005,58.55788421874996}},color={28,108,200}));
  connect(setpointController4.actuatorSignal,actuatorSignal3)
    annotation (Line(points={{431.2613400000001,58.55788421874996},{499.44533999999993,58.55788421874996},{499.44533999999993,57.21388421874991}},color={0,0,127}));
  connect(addA.y,compressorFrequency2)
    annotation (Line(points={{237.66134000000005,54.55788421874996},{237.66134000000005,54.55676814732141},{276.3756257142857,54.55676814732141}},color={0,0,127}));
  connect(compressorFrequency_error5.sensor,setpointController5.errorSignal)
    annotation (Line(points={{98.83519660827176,-22.80000000000001},{105.07893830413593,-22.80000000000001},{105.07893830413593,8.557884218749962},{114.66134000000005,8.557884218749962}},color={28,108,200}));
  connect(setpointController5.actuatorSignal,gainA.u)
    annotation (Line(points={{135.26134000000008,8.557884218749962},{149.66457642936047,8.557884218749962},{149.66457642936047,34.55788421874996},{164.63924143014935,34.55788421874996}},color={0,0,127}));
  connect(setpointController5.actuatorSignal,gainB.u)
    annotation (Line(points={{135.26134000000008,8.557884218749962},{149.05743357221758,8.557884218749962},{149.05743357221758,-46.00573185267859},{164.85352714443508,-46.00573185267859}},color={0,0,127}));
  connect(compressorFrequency_error6.sensor,setpointController6.errorSignal)
    annotation (Line(points={{372.6947588304941,-68.6136713368056},{424.2648955555557,-68.6136713368056},{424.2648955555557,-66.69900467013889}},color={28,108,200}));
  connect(setpointController6.actuatorSignal,actuatorSignal4)
    annotation (Line(points={{444.86489555555573,-66.69900467013889},{511.85956222222234,-66.69900467013889},{511.85956222222234,-67.81367133680558}},color={0,0,127}));
  connect(actuatorSignal4,measurementBusB2.compressorFrequency)
    annotation (Line(points={{511.85956222222234,-67.81367133680558},{530.6613400000001,-67.81367133680558},{530.6613400000001,-21.19211578125004},{394.91134000000005,-21.19211578125004}},color={0,0,127}));
  connect(totalCapacityB2.y,measurementBusB2.capacity)
    annotation (Line(points={{310.59552757666603,-23.44211578125004},{394.91134000000005,-23.44211578125004},{394.91134000000005,-21.19211578125004}},color={0,0,127}));
end test_LoadDistribution_duplex;
