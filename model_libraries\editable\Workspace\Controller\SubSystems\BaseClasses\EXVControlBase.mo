within KAV_initiation.System30KAV.Controller30KAV.SubSystems.BaseClasses;
partial model EXVControlBase
  extends BOLT.InternalLibrary.BuildingBlocks.Icons.ValveIcon;
  parameter Boolean crkIsOff=false
    "Specify whether the controller is on or off";
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-120.0,40.0},{-80.0,80.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-120,40},{-80,80}})));
  .Workspace.Interfaces.LimitsBus limitsBus
    annotation (Placement(transformation(extent={{-46,-56},{-26,-36}},origin={-64,-14},rotation=0),iconTransformation(extent={{-110,-70},{-90,-50}})));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal
    annotation (Placement(transformation(extent={{90.0,-10.0},{110.0,10.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{90,-10},{110,10}})));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal_sst
    annotation (Placement(transformation(extent={{90.0,-36.0},{110.0,-16.0}},origin={0.0,0.0},rotation=0.0)));
end EXVControlBase;
