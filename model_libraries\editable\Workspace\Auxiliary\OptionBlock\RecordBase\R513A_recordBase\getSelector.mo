within Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase;
function getSelector
  input.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Selector Selector;
  output.Workspace.Auxiliary.OptionBlock.RecordBase.UnitBase_2C Unit;
protected
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Unit_500_R513A Unit_30XBV_0500_R513A;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Unit_600_R513A Unit_30XBV_0600_R513A;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Unit_700_R513A Unit_30XBV_0700_R513A;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Unit_800_R513A Unit_30XBV_0800_R513A;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Unit_900_R513A Unit_30XBV_0900_R513A;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Unit_1000_R513A Unit_30XBV_1000_R513A;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Unit_1100_R513A Unit_30XBV_1100_R513A;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Unit_1200_R513A Unit_30XBV_1200_R513A;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Unit_1300_R513A Unit_30XBV_1300_R513A;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Unit_1450_R513A Unit_30XBV_1450_R513A;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Unit_700_Duplex_R513A Unit_30XBV_0700_Duplex_R513A;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Unit_800_Duplex_R513A Unit_30XBV_0800_Duplex_R513A;
  constant.Workspace.Auxiliary.OptionBlock.RecordBase.R513A_recordBase.Unit_900_Duplex_R513A Unit_30XBV_0900_Duplex_R513A;
  .Workspace.Auxiliary.OptionBlock.RecordBase.UnitBase_2C[:] Unit_base={Unit_30XBV_0500_R513A,Unit_30XBV_0600_R513A,Unit_30XBV_0700_R513A,Unit_30XBV_0800_R513A,Unit_30XBV_0900_R513A,Unit_30XBV_1000_R513A,Unit_30XBV_1100_R513A,Unit_30XBV_1200_R513A,Unit_30XBV_1300_R513A,Unit_30XBV_1450_R513A,Unit_30XBV_0700_Duplex_R513A,Unit_30XBV_0800_Duplex_R513A,Unit_30XBV_0900_Duplex_R513A};
algorithm
  Unit := Unit_base[Integer(
    Selector)];
end getSelector;
