within KAV_initiation.System30KAV.Controller30KAV.Tests;
model Test_1DTable
  parameter Real SST=273.15;
  Modelica.Blocks.Tables.CombiTable1Ds T_sdt_low_map_interpolation_Linear(
    table={{-23.31,3.9},{-23.3,3.9},{-15,7.9},{5,20},{17,28.7},{22,32.3},{22.01,32.3}} .+ 273.15,
    smoothness=Modelica.Blocks.Types.Smoothness.LinearSegments,
    extrapolation=Modelica.Blocks.Types.Extrapolation.HoldLastPoint)
    "Interpolation table for low SDT setpoint in Fan controller in case of low OAT"
    annotation (Placement(transformation(extent={{-60.0,-60.0},{-40.0,-40.0}},rotation=0.0,origin={50,80})));
  Modelica.Blocks.Tables.CombiTable1Ds T_sdt_low_map_interpolation_MCD1(
    table={{-23.31,3.9},{-23.3,3.9},{-15,7.9},{5,20},{17,28.7},{22,32.3},{22.01,32.3}} .+ 273.15,
    smoothness=Modelica.Blocks.Types.Smoothness.LinearSegments,
    extrapolation=Modelica.Blocks.Types.Extrapolation.HoldLastPoint)
    "Interpolation table for low SDT setpoint in Fan controller in case of low OAT"
    annotation (Placement(transformation(extent={{-60.0,-60.0},{-40.0,-40.0}},rotation=0.0,origin={50,20})));
  Modelica.Blocks.Sources.RealExpression SST_block(
    y=SST)
    annotation (Placement(transformation(extent={{50.96034484924278,38.0},{81.03965515075723,58.0}},origin={-124,-48},rotation=0.0)));
equation
  connect(SST_block.y,T_sdt_low_map_interpolation_Linear.u)
    annotation (Line(points={{-41.4564,0},{-20,0},{-20,30},{-12,30}},color={0,0,127}));
  connect(SST_block.y,T_sdt_low_map_interpolation_MCD1.u)
    annotation (Line(points={{-41.4564,0},{-20,0},{-20,-30},{-12,-30}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false)),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)));
end Test_1DTable;
