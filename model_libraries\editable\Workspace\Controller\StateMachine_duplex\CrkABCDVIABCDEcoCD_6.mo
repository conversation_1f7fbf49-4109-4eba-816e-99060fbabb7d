within StateMachine;
record CrkABCDVIABCDEcoCD_6
  import BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod;
  extends Mode30XBVBase(
    final modeID=ModeID30XBV.CrkABCDVIABCDEcoCD_6,
    final CrkA=true,
    final CrkB=true,
    final CrkC=true,
    final CrkD=true,
    final EcoA=false,
    final EcoB=false,
    final EcoC=true,
    final EcoD=true,
    final ViA=true,
    final ViB=true,
    final ViC=true,
    final ViD=true,
    initMethod=ModeInitMethod.External);
end CrkABCDVIABCDEcoCD_6;
