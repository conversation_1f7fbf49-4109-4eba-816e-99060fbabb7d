within Workspace.Surrogate.Tests_MCHX;
model Test_MCHX
  .Workspace.Surrogate.CondAirM_Surrogate condAirM_Surrogate(
    OAT=308,
    Tsat_in_start=308,
    p_in_air_gage_start=101300,
    RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a,
    isOff_ref=false,
    nCoils=7,
    streamID=1,
    x_out_start=-0.08,
    selector=Workspace.Surrogate.MCHX_Cond.Selector.MCHX_Cond_R134a,
    Z_U=0.74,
    Z_dpr_fixed=true,
    Z_U_fixed=true)
    annotation (Placement(transformation(extent={{6.0,8.0},{26.0,28.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Surrogate.HXComponents.RefAir_MCHX.CondAirM_30XBV condAirM_30XBV(
    nCoils=7,
    selector_curve_num=106,
    userDefined_geo(
      length_tube=1.937,
      diameter_in_header=0.02395,
      length_tube_inlet=0.02286,
      diameter_in_inletTube=0.0157,
      length_outlet=0.02286,
      diameter_in_outlet=0.0157),
    n_tubes={{90,30}},
    n_slabs=1,
    tubeOrientation=.BOLT.InternalLibrary.BuildingBlocks.Types.TubeOrientation.Vertical_inletTop,
    Z_U=0.74)
    annotation (Placement(transformation(extent={{-26.49652390608573,-67.96805179448327},{10.49652390608573,-32.03194820551673}},rotation=0.0,origin={0.0,0.0})));
  .BOLT.BoundaryNode.Refrigerant.Source RefIn(
    x_set=1.0768,
    T_fixed=false,
    Tsat_fixed=false,
    m_flow_set=3.415298)
    annotation (Placement(transformation(extent={{-6.459106834751182,-6.459106834751184},{6.459106834751182,6.459106834751184}},origin={-26.0,26.0},rotation=-90.0)));
  .BOLT.BoundaryNode.Air.Sink AirOut(
    pg_set=0)
    annotation (Placement(transformation(extent={{-6.114381372769245,6.114381372769245},{6.114381372769245,-6.114381372769245}},origin={42.609037883427106,84.38079951303433},rotation=180)));
  .BOLT.BoundaryNode.Refrigerant.Sink RefOut(
    x_set=-0.0359,
    x_fixed=true,
    p_fixed=false)
    annotation (Placement(transformation(extent={{7.0738408075374934,-7.07384080753749},{-7.0738408075374934,7.07384080753749}},origin={42.0,8.0},rotation=-90.0)));
  .BOLT.BoundaryNode.Air.Source AirIn(
    Vd_flow_set=5.458861,
    Vd_flow_fixed=true,
    Vds_flow_fixed=false,
    Vds_flow_set=2,
    Tdb_set=288.15)
    annotation (Placement(transformation(extent={{19.609632578083477,-20.390367421916523},{32.39036742191652,-7.6096325780834775}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Refrigerant.Source RefIn2(
    m_flow_set=3.415298,
    Tsat_fixed=false,
    T_fixed=false,
    x_set=1.0768)
    annotation (Placement(transformation(extent={{-6.459106834751182,-6.459106834751182},{6.459106834751182,6.459106834751182}},origin={-65.15225947085678,-66.09519987825858},rotation=-90.0)));
  .BOLT.BoundaryNode.Air.Source AirIn2(
    Tdb_set=288.15,
    Vds_flow_set=2,
    Vds_flow_fixed=false,
    Vd_flow_fixed=true,
    Vd_flow_set=5.458861)
    annotation (Placement(transformation(extent={{-19.5426268927733,-112.4855673001751},{-6.761892048940254,-99.70483245634206}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Refrigerant.Sink RefOut2(
    p_fixed=false,
    x_fixed=true,
    x_set=-0.0359)
    annotation (Placement(transformation(extent={{7.0738408075374934,-7.0738408075374934},{-7.0738408075374934,7.0738408075374934}},origin={32.0,-56.0},rotation=-90.0)));
  .BOLT.BoundaryNode.Air.Sink AirOut2(
    pg_set=0)
    annotation (Placement(transformation(extent={{-6.114381372769245,6.114381372769245},{6.114381372769245,-6.114381372769245}},origin={3.45677841257033,-7.71440036522425},rotation=180.0)));
equation
  connect(RefIn.port,condAirM_Surrogate.port_a_ref)
    annotation (Line(points={{-26,19.540893165248818},{-26,11.540893165248818},{0,11.540893165248818},{0,18},{6,18}},color={255,0,0}));
  connect(condAirM_Surrogate.port_b_ref,RefOut.port)
    annotation (Line(points={{26,18},{32,18},{32,21.073840807537493},{42,21.073840807537493},{42,15.073840807537493}},color={255,0,0}));
  connect(condAirM_Surrogate.port_b_air,AirOut.port)
    annotation (Line(points={{16,28},{16,84.38079951303433},{36.49465651065786,84.38079951303433}},color={0,0,255}));
  connect(AirIn.port,condAirM_Surrogate.port_a_air)
    annotation (Line(points={{32.39036742191652,-14},{16,-14},{16,8}},color={0,0,255}));
  connect(RefIn2.port,condAirM_30XBV.port_a_ref)
    annotation (Line(points={{-65.15225947085678,-72.55430671300977},{-38.78534203415488,-72.55430671300977},{-38.78534203415488,-50},{-32.78534203415488,-50}},color={255,0,0}));
  connect(condAirM_30XBV.port_b_air,AirOut2.port)
    annotation (Line(points={{-8,-34.18811442085472},{-8,-7.714400365224249},{-2.657602960198915,-7.714400365224249}},color={0,0,255}));
  connect(condAirM_30XBV.port_b_ref,RefOut2.port)
    annotation (Line(points={{10.496523906085727,-50},{16.496523906085727,-50},{16.496523906085727,-42.92615919246251},{32,-42.92615919246251},{32,-48.92615919246251}},color={255,0,0}));
  connect(condAirM_30XBV.port_a_air,AirIn2.port)
    annotation (Line(points={{-8,-65.4525245432556},{-8,-71.4525245432556},{-0.7618920489402541,-71.4525245432556},{-0.7618920489402541,-106.09519987825858},{-6.761892048940254,-106.09519987825858}},color={0,0,255}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Test_MCHX;
