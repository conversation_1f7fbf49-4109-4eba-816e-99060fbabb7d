model Configuration
  "One circuit surrogate CondAir1 setup"
  replaceable package AirMedium=BOLT.InternalLibrary.Media.Air.MoistAir
    constrainedby BOLT.InternalLibrary.Media.Air.Interfaces.PartialMedium
    "Air medium"
    annotation (choicesAllMatching=true);
   parameter BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R32 
    "Refrigerant medium"
    annotation (choicesAllMatching=true);
  final parameter BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefBase refBase = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.GetRefSelector(RefMedium);
  package refBaseMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.GenericPureRefrigerant;
  // Initialization
  parameter Real x_out_start=-0.05
    "Initial value of outlet refrigerant quality"
    annotation (Dialog(tab="Initialization"));
  parameter refBaseMedium.Temperature OAT=globalParameters.T_ambient
    "Outside Air Temperature"
    annotation (Dialog(tab="Initialization"));
  final parameter refBaseMedium.Temperature LWT_start=OAT+5
    "Initial value of leaving air temperature"
    annotation (Dialog(tab="Initialization"));
  parameter Modelica.SIunits.PressureDifference p_in_air_gage_start=10
    "Initial value of inlet air gage pressure"
    annotation (Dialog(tab="Initialization"));
  parameter refBaseMedium.Temperature Tsat_in_start=globalParameters.T_ambient
    "Initial value of inlet refrigerant saturation temperature"
    annotation (Dialog(tab="Initialization"));
  final parameter refBaseMedium.AbsolutePressure p_in_ref_start=refBaseMedium.saturationPressure(refBase.refName,
    Tsat_in_start,
    1)
    "Initial value of inlet refrigerant absolute pressure";
  // General
  extends BOLT.InternalLibrary.BuildingBlocks.Configuration.Base;
  parameter Integer streamID=1
    "Identifier of the circuit this component is connected to";
  final parameter Modelica.SIunits.Pressure p_atm=globalParameters.p_ambient
    "Atmosphere Pressure";
  parameter Integer nCoils(
    min=1)=1
    "Number of coils"
    annotation (Dialog(group="Geometry"));
  //Calibration factors
  parameter Boolean use_ZU_in=false
    "If true, get the Z_U from the input connector"
    annotation (Dialog(group="Calibration"));
  parameter Boolean use_Zdpr_in=false
    "if true, get the Zdpr from the input connector"
    annotation (Dialog(group="Calibration"));
  parameter Boolean use_Z_fanScaling=false
    "If true, get the Z_fanScaling from the input connector"
    annotation (Dialog(group="Calibration"));
  parameter Real Z_U(
    min=0.135,
    max=2.2)=1.0
    "Z_U     "
    annotation (Dialog(group="Calibration",enable=Z_U_fixed and not use_ZU_in,__Dymola_compact=true,__Dymola_joinNext=true,__Dymola_descriptionLabel=true),__Modelon(DialogExtensions(row="Z_U",column="Set",hide=Z_U_fixed)));
  parameter Boolean Z_U_fixed=true
    " Calibration factor for total heat transfer coefficient Fixed"
    annotation (Dialog(group="Calibration",__Dymola_compact=true,__Dymola_descriptionLabel=true),choices(__Dymola_checkBox=true),__Modelon(DialogExtensions(row="Z_U",column="Fixed",hide=Z_U_fixed)));
  parameter Real Z_dpr(
    min=0.135,
    max=2.2)=1.0
    "Z_dpr     "
    annotation (Dialog(group="Calibration",enable=Z_dpr_fixed and not use_Zdpr_in,__Dymola_compact=true,__Dymola_joinNext=true,__Dymola_descriptionLabel=true),__Modelon(DialogExtensions(row="Z_dpr",column="Set",hide=Z_dpr_fixed)));
  parameter Boolean Z_dpr_fixed=true
    " Calibration factor for refrigerant pressure drop Fixed"
    annotation (Dialog(group="Calibration",__Dymola_compact=true,__Dymola_descriptionLabel=true),choices(__Dymola_checkBox=true),__Modelon(DialogExtensions(row="Z_dpr",column="Fixed",hide=Z_dpr_fixed)));
  parameter Modelica.SIunits.HeatFlowRate capacity(
    min=0.0,
    max=2.0e7)=7e4
    "capacity    "
    annotation (Dialog(tab="SteadyState",group="Fixed condition",enable=
      if stateSelection == BuildingBlocks.Types.StateSelection.steadyState then
        capacity_fixed
      else
        false,__Dymola_compact=true,__Dymola_joinNext=true,__Dymola_descriptionLabel=true),__Modelon(DialogExtensions(row="capacity",column="Set",hide=not capacity_fixed)));
  parameter Boolean capacity_fixed=false
    " Value of capacity Fixed"
    annotation (Dialog(tab="SteadyState",group="Fixed condition",enable=
      if stateSelection == BuildingBlocks.Types.StateSelection.steadyState then
        true
      else
        false,__Dymola_compact=true,__Dymola_descriptionLabel=true),choices(__Dymola_checkBox=true),__Modelon(DialogExtensions(row="capacity",column="Fixed",hide=not capacity_fixed)));
  parameter Real dpr(
    min=0.0,
    max=1e6)=3e4
    "dpr     "
    annotation (Dialog(tab="SteadyState",group="Fixed condition",enable=
      if stateSelection == BuildingBlocks.Types.StateSelection.steadyState then
        capacity_fixed
      else
        false,__Dymola_compact=true,__Dymola_joinNext=true,__Dymola_descriptionLabel=true),__Modelon(DialogExtensions(row="dpr",column="Set",hide=not dpr_fixed)));
  parameter Boolean dpr_fixed=false
    " Value of refrigerant pressure loss Fixed"
    annotation (Dialog(tab="SteadyState",group="Fixed condition",enable=
      if stateSelection == BuildingBlocks.Types.StateSelection.steadyState then
        true
      else
        false,__Dymola_compact=true,__Dymola_descriptionLabel=true),choices(__Dymola_checkBox=true),__Modelon(DialogExtensions(row="dpr",column="Fixed",hide=not dpr_fixed)));
  // K factor
  parameter Real k_CoolCap(
    min=0,
    max=1)=0
    "If 0 then component net total cooling capacity will not contribute to total system net total cooling capacity"
    annotation (__Modelon(DialogExtensions(row="CoolCap",column="Contribution")),Dialog(group="Contribution"));
  parameter Real k_HeatCap(
    min=0,
    max=1)=1
    "If 0 then component net total heating capacity will not contribute to total system net total heating capacity"
    annotation (__Modelon(DialogExtensions(row="HeatCap",column="Contribution")),Dialog(group="Contribution"));
  parameter Boolean isOff_ref=false
    "Set true to turn off the refrigerant pressure drop and heat transfer"
    annotation (Dialog(tab="SteadyState"));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false)),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)));
end Configuration;
