within StateMachine;
record CrkABEcoAB_19
  import BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod;
  extends Mode30XBVBase(
    final modeID=ModeID30XBV.CrkABEcoAB_19,
    final CrkA=true,
    final CrkB=true,
    final CrkC=false,
    final CrkD=false,
    final EcoA=true,
    final EcoB=true,
    final EcoC=false,
    final EcoD=false,
    final ViA=false,
    final ViB=false,
    final ViC=false,
    final ViD=false,
    initMethod=ModeInitMethod.External);
end CrkABEcoAB_19;
