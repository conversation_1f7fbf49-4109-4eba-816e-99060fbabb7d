within Workspace.System.R134a.Surrogate;
model CL_30XBV_OnePasse
  extends.Workspace.System.BaseCycle.XBV_2C.Surrogate.System_30XBV(
    ModuleA(
      shaftPumpUser(
        streamID=1),
      c_load2_bf_pow_opt17A=0.16,
      c_load_bf_pow_opt17A=-0.16,
      bf_pow_min_opt17A=0.96),
    choiceBlock(
      FAN_SPEED_selector=Workspace.Auxiliary.OptionBlock.FanSpeed.Selector.FAN_VS,
      Selector_ModuleA=Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Selector.Unit_30XBV_1200,
      HighAmbiant_selector=Workspace.Auxiliary.OptionBlock.HighAmbiantTemperature.HighAmbiant_selector.HighAmbiant,
      SixtyHz_selector=Workspace.Auxiliary.OptionBlock.SixtyHzOption.SixtyHz_selector.SixtyHz,
      Coating_selector=Workspace.Auxiliary.OptionBlock.CoatingOption.Coating_selector.STANDARD),
    ECAT(
      AmbientAirDBTemp_K(
        setPoint=273.15 + 35),
      EvapBrineLWT_K(
        setPoint=7+273.15),
      Evap<PERSON>rineEWT_K(
        setPoint=12+273.15),
      ExternalSystemPressureDrop_Pa(
        setPoint=15000)),use_ECATBlock = true)
    annotation (Icon(graphics={Text(textString="CL",origin={2,-12},extent={{-100,75},{100,-75}})}));
  annotation (
    Icon(
      graphics={
        Text(
          textString="CL",
          origin={-2,-10},
          extent={{-96.01685393258424,70.66502830561285},{97,-70}})}));
end CL_30XBV_OnePasse;
