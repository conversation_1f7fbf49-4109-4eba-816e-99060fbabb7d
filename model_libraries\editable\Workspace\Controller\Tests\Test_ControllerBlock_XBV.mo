within Workspace.Controller.Tests;
model Test_ControllerBlock_XBV
  .Workspace.Controller.Controller controller(
    crkA_isOff=false,
    crkB_isOff=false,
    ecoA_isOff=false,
    ecoB_isOff=false)
    annotation (Placement(transformation(extent={{-16.40871812252771,-3.941579598417732},{18.48174362450554,30.948882148615517}},origin={0.0,0.0},rotation=0.0)));
  parameter Modelica.SIunits.Power capacity_setpoint=150000;
  parameter Modelica.SIunits.Temperature T_sst_min_limit_comp=0;
  parameter Modelica.SIunits.Temperature T_sdt_max_limit_comp=67;
  parameter Modelica.SIunits.Temperature T_dgt_max_limit_comp=94;
  parameter Modelica.SIunits.Temperature T_sst_min_limit_exv=0.5;
  parameter Modelica.SIunits.Temperature T_sst_max_limit=17;
  parameter Modelica.SIunits.Temperature dT_dsh_min_limit=7;
  parameter Modelica.SIunits.Temperature dT_sbc_setpoint=-5;
  parameter Modelica.SIunits.Temperature T_dgt_max_limit_exv=93;
  parameter Modelica.SIunits.Temperature dT_esh_setpoint=10;
  parameter Real fanSpeed_setpoint=0.8;
  parameter Real rel_cooler_level_setPoint=0.7;
  parameter Real ecoEXV_max_opening=1;
  parameter Modelica.SIunits.Temperature T_sdt_min_limit=20;
  parameter Modelica.SIunits.Temperature T_sdt_max_limit_fan=65;
  parameter Modelica.SIunits.Temperature T_dgt_max_limit_fan=92;
  .Workspace.Interfaces.MeasurementBus measurementBusA
    annotation (Placement(transformation(extent={{63.83934389564879,76.30648241975877},{133.62026738971528,146.08740591382525}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBusB
    annotation (Placement(transformation(extent={{60.350297720945456,-119.08010336362744},{130.13122121501195,-49.29917986956095}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Controller.Tests.y_equalto_kx_plus_b y_equalto_kx_plus_b(
    n=8,
    m=13)
    annotation (Placement(transformation(extent={{64.89393968470144,-7.106060315298567},{111.10606031529856,39.10606031529856}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_max_limit_block(
    y=295)
    annotation (Placement(transformation(extent={{-231.65247856108107,145.12822191427782},{-187.61626692028545,174.40828930902674}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_exv_block(
    y=273)
    annotation (Placement(transformation(extent={{-230.13060651557208,103.32048475379881},{-186.09439487477647,132.60055214854773}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_comp_block(
    y=367)
    annotation (Placement(transformation(extent={{-230.13060651557208,82.8244375774745},{-186.09439487477647,112.10450497222348}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_max_limit_comp_block(
    y=349)
    annotation (Placement(transformation(extent={{-230.13060651557208,20.970295206067306},{-186.09439487477647,50.25036260081629}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_min_limit_block(
    y=293)
    annotation (Placement(transformation(extent={{-231.65247856108107,-20.871778085722212},{-187.61626692028545,8.40828930902677}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_sbc_setpoint_block(
    y=-5)
    annotation (Placement(transformation(extent={{-230.13060651557208,-41.981849692642996},{-186.09439487477647,-12.701782297894013}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_esh_setpoint_block(
    y=10)
    annotation (Placement(transformation(extent={{-230.13060651557208,-62.47789686896728},{-186.09439487477647,-33.197829474218295}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_dsh_min_limit_block(
    y=7)
    annotation (Placement(transformation(extent={{-230.13060651557208,-82.97394404529156},{-186.09439487477647,-53.69387665054258}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression capacity_setpoint_block(
    y=capacity_setpoint)
    annotation (Placement(transformation(extent={{-230.13060651557208,-103.46999122161583},{-186.09439487477647,-74.18992382686687}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression fanSpeed_setpoint_block(
    y=50)
    annotation (Placement(transformation(extent={{-230.13060651557208,-123.96603839794011},{-186.09439487477647,-94.68597100319113}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sst_min_limit_comp_block(
    y=273)
    annotation (Placement(transformation(extent={{-230.13060651557208,123.81653193012309},{-186.09439487477647,153.09659932487202}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_sdt_max_limit_fan_block(
    y=338)
    annotation (Placement(transformation(extent={{-229.65247856108107,1.1282219142777876},{-185.61626692028545,30.40828930902677}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_exv_block(
    y=366)
    annotation (Placement(transformation(extent={{-230.13060651557208,62.328390401150216},{-186.09439487477647,91.6084577958992}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression T_dgt_max_limit_fan_block(
    y=365)
    annotation (Placement(transformation(extent={{-230.13060651557208,41.832343224825934},{-186.09439487477647,71.11241061957492}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ecoEXV_max_opening_block(
    y=1.1)
    annotation (Placement(transformation(extent={{-230.13060651557208,-145.5600881015675},{-186.09439487477647,-116.2800207068185}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_level_setpoint_block(
    y=0.97)
    annotation (Placement(transformation(extent={{-230.13060651557208,-166.05613527789177},{-186.09439487477647,-136.7760678831428}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_moduleA
    annotation (Placement(transformation(extent={{-171.28006739474898,-31.280067394748983},{-112.71993260525102,27.280067394748983}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_moduleB
    annotation (Placement(transformation(extent={{-101.28006739474898,-33.28006739474898},{-42.71993260525102,25.280067394748983}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(measurementBusB,controller.measurementBus_crkB)
    annotation (Line(points={{95.24075946797869,-84.18964161659422},{-16.408718122527713,-84.18964161659422},{-16.408718122527713,8.270082013043904}},color={255,204,51}));
  connect(controller.measurementBus_crkA,measurementBusA)
    annotation (Line(points={{-16.408718122527713,25.715312886560532},{-16.408718122527713,111.196944166792},{98.72980564268202,111.196944166792}},color={255,204,51}));
  connect(controller.exv_crkA,y_equalto_kx_plus_b.x[2])
    annotation (Line(points={{19.87736209438687,23.970789799208866},{48.409029829231315,23.970789799208866},{48.409029829231315,15.768939396847014},{76.67803044550371,15.768939396847014}},color={0,0,127}));
  connect(controller.ecoExv_crkA,y_equalto_kx_plus_b.x[3])
    annotation (Line(points={{19.87736209438687,19.435029772094545},{48.409029829231315,19.435029772094545},{48.409029829231315,15.768939396847014},{76.67803044550371,15.768939396847014}},color={0,0,127}));
  connect(controller.compressor_crkB,y_equalto_kx_plus_b.x[5])
    annotation (Line(points={{19.87736209438687,11.368355016180459},{48.409029829231315,11.368355016180459},{48.409029829231315,15.768939396847014},{76.67803044550371,15.768939396847014}},color={0,0,127}));
  connect(controller.compressor_crkA,y_equalto_kx_plus_b.x[1])
    annotation (Line(points={{19.87736209438687,15.206305808354116},{48.409029829231315,15.206305808354116},{48.409029829231315,15.768939396847014},{76.67803044550371,15.768939396847014}},color={0,0,127}));
  connect(controller.fan_crkB,y_equalto_kx_plus_b.x[8])
    annotation (Line(points={{19.87736209438687,7.57227277810324},{48.409029829231315,7.57227277810324},{48.409029829231315,15.768939396847014},{76.67803044550371,15.768939396847014}},color={0,0,127}));
  connect(controller.fan_crkA,y_equalto_kx_plus_b.x[4])
    annotation (Line(points={{19.87736209438687,28.855454443793526},{48.409029829231315,28.855454443793526},{48.409029829231315,15.768939396847014},{76.67803044550371,15.768939396847014}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[1],measurementBusB.T_oat)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110.51389640348428,15.768939396847014},{110.51389640348428,-84.18964161659422},{95.24075946797869,-84.18964161659422}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[2],measurementBusB.T_ewt)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110.51389640348428,15.768939396847014},{110.51389640348428,-84.18964161659422},{95.24075946797869,-84.18964161659422}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[3],measurementBusB.T_lwt)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110.51389640348428,15.768939396847014},{110.51389640348428,-84.18964161659422},{95.24075946797869,-84.18964161659422}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[4],measurementBusB.T_sst)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110.51389640348428,15.768939396847014},{110.51389640348428,-84.18964161659422},{95.24075946797869,-84.18964161659422}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[5],measurementBusB.T_sdt)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110.51389640348428,15.768939396847014},{110.51389640348428,-84.18964161659422},{95.24075946797869,-84.18964161659422}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[6],measurementBusB.T_dgt)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110.51389640348428,15.768939396847014},{110.51389640348428,-84.18964161659422},{95.24075946797869,-84.18964161659422}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[7],measurementBusB.dT_ssh)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110.51389640348428,15.768939396847014},{110.51389640348428,-84.18964161659422},{95.24075946797869,-84.18964161659422}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[8],measurementBusB.dT_esh)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110.51389640348428,15.768939396847014},{110.51389640348428,-84.18964161659422},{95.24075946797869,-84.18964161659422}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[9],measurementBusB.dT_dsh)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110.51389640348428,15.768939396847014},{110.51389640348428,-84.18964161659422},{95.24075946797869,-84.18964161659422}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[10],measurementBusB.dT_sbc)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110.51389640348428,15.768939396847014},{110.51389640348428,-84.18964161659422},{95.24075946797869,-84.18964161659422}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[11],measurementBusB.capacity)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110.51389640348428,15.768939396847014},{110.51389640348428,-84.18964161659422},{95.24075946797869,-84.18964161659422}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[12],measurementBusB.compressorFrequency)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110.51389640348428,15.768939396847014},{110.51389640348428,-84.18964161659422},{95.24075946797869,-84.18964161659422}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[13],measurementBusB.rel_cooler_level)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110.51389640348428,15.768939396847014},{110.51389640348428,-84.18964161659422},{95.24075946797869,-84.18964161659422}},color={0,0,127}));
  connect(T_sst_max_limit_block.y,limitsBus_moduleA.T_sst_max_limit)
    annotation (Line(points={{-185.41445633824566,159.76825561165228},{-142,159.76825561165228},{-142,-2}},color={0,0,127}));
  connect(T_sst_min_limit_comp_block.y,limitsBus_moduleA.T_sst_min_limit_comp)
    annotation (Line(points={{-183.89258429273673,138.45656562749755},{-142,138.45656562749755},{-142,-2}},color={0,0,127}));
  connect(T_sst_min_limit_exv_block.y,limitsBus_moduleA.T_sst_min_limit_exv)
    annotation (Line(points={{-183.89258429273673,117.96051845117327},{-142,117.96051845117327},{-142,-2}},color={0,0,127}));
  connect(T_dgt_max_limit_comp_block.y,limitsBus_moduleA.T_dgt_max_limit_comp)
    annotation (Line(points={{-183.89258429273673,97.46447127484899},{-142,97.46447127484899},{-142,-2}},color={0,0,127}));
  connect(T_dgt_max_limit_exv_block.y,limitsBus_moduleA.T_dgt_max_limit_exv)
    annotation (Line(points={{-183.89258429273673,76.96842409852471},{-142,76.96842409852471},{-142,-2}},color={0,0,127}));
  connect(T_dgt_max_limit_fan_block.y,limitsBus_moduleA.T_dgt_max_limit_fan)
    annotation (Line(points={{-183.89258429273673,56.472376922200425},{-142,56.472376922200425},{-142,-2}},color={0,0,127}));
  connect(T_sdt_max_limit_comp_block.y,limitsBus_moduleA.T_sdt_max_limit_comp)
    annotation (Line(points={{-183.89258429273673,35.6103289034418},{-142,35.6103289034418},{-142,-2}},color={0,0,127}));
  connect(T_sdt_max_limit_fan_block.y,limitsBus_moduleA.T_sdt_max_limit_fan)
    annotation (Line(points={{-183.41445633824566,15.768255611652279},{-142,15.768255611652279},{-142,-2}},color={0,0,127}));
  connect(T_sdt_min_limit_block.y,limitsBus_moduleA.T_sdt_min_limit)
    annotation (Line(points={{-185.41445633824566,-6.231744388347721},{-142,-6.231744388347721},{-142,-2}},color={0,0,127}));
  connect(dT_sbc_setpoint_block.y,limitsBus_moduleA.dT_sbc_setpoint)
    annotation (Line(points={{-183.89258429273673,-27.34181599526849},{-142,-27.34181599526849},{-142,-2}},color={0,0,127}));
  connect(dT_esh_setpoint_block.y,limitsBus_moduleA.dT_esh_setpoint)
    annotation (Line(points={{-183.89258429273673,-47.83786317159277},{-142,-47.83786317159277},{-142,-2}},color={0,0,127}));
  connect(dT_dsh_min_limit_block.y,limitsBus_moduleA.dT_dsh_min_limt)
    annotation (Line(points={{-183.89258429273673,-68.33391034791705},{-142,-68.33391034791705},{-142,-2}},color={0,0,127}));
  connect(capacity_setpoint_block.y,limitsBus_moduleA.capacity_setpoint)
    annotation (Line(points={{-183.89258429273673,-88.82995752424134},{-142,-88.82995752424134},{-142,-2}},color={0,0,127}));
  connect(fanSpeed_setpoint_block.y,limitsBus_moduleA.fanSpeed_setpoint)
    annotation (Line(points={{-183.89258429273673,-109.32600470056562},{-142,-109.32600470056562},{-142,-2}},color={0,0,127}));
  connect(ecoEXV_max_opening_block.y,limitsBus_moduleA.ecoEXV_max_opening)
    annotation (Line(points={{-183.89258429273673,-130.920054404193},{-142,-130.920054404193},{-142,-2}},color={0,0,127}));
  connect(rel_cooler_level_setpoint_block.y,limitsBus_moduleA.rel_cooler_level_setpoint)
    annotation (Line(points={{-183.89258429273673,-151.41610158051725},{-142,-151.41610158051725},{-142,-2}},color={0,0,127}));
  connect(T_sst_max_limit_block.y,limitsBus_moduleB.T_sst_max_limit)
    annotation (Line(points={{-185.41445633824569,159.76825561165228},{-72,159.76825561165228},{-72,-4}},color={0,0,127}));
  connect(T_sst_min_limit_comp_block.y,limitsBus_moduleB.T_sst_min_limit_comp)
    annotation (Line(points={{-183.8925842927367,138.45656562749755},{-72,138.45656562749755},{-72,-4}},color={0,0,127}));
  connect(T_sst_min_limit_exv_block.y,limitsBus_moduleB.T_sst_min_limit_exv)
    annotation (Line(points={{-183.8925842927367,117.96051845117327},{-72,117.96051845117327},{-72,-4}},color={0,0,127}));
  connect(T_dgt_max_limit_comp_block.y,limitsBus_moduleB.T_dgt_max_limit_comp)
    annotation (Line(points={{-183.8925842927367,97.46447127484899},{-72,97.46447127484899},{-72,-4}},color={0,0,127}));
  connect(T_dgt_max_limit_exv_block.y,limitsBus_moduleB.T_dgt_max_limit_exv)
    annotation (Line(points={{-183.8925842927367,76.96842409852471},{-72,76.96842409852471},{-72,-4}},color={0,0,127}));
  connect(T_dgt_max_limit_fan_block.y,limitsBus_moduleB.T_dgt_max_limit_fan)
    annotation (Line(points={{-183.8925842927367,56.472376922200425},{-72,56.472376922200425},{-72,-4}},color={0,0,127}));
  connect(T_sdt_max_limit_comp_block.y,limitsBus_moduleB.T_sdt_max_limit_comp)
    annotation (Line(points={{-183.8925842927367,35.6103289034418},{-72,35.6103289034418},{-72,-4}},color={0,0,127}));
  connect(T_sdt_max_limit_fan_block.y,limitsBus_moduleB.T_sdt_max_limit_fan)
    annotation (Line(points={{-183.41445633824569,15.768255611652279},{-125.70722816912284,15.768255611652279},{-125.70722816912284,-4},{-72,-4}},color={0,0,127}));
  connect(T_sdt_min_limit_block.y,limitsBus_moduleB.T_sdt_min_limit)
    annotation (Line(points={{-185.41445633824569,-6.23174438834772},{-126.70722816912284,-6.23174438834772},{-126.70722816912284,-4},{-72,-4}},color={0,0,127}));
  connect(dT_sbc_setpoint_block.y,limitsBus_moduleB.dT_sbc_setpoint)
    annotation (Line(points={{-183.8925842927367,-27.341815995268504},{-72,-27.341815995268504},{-72,-4}},color={0,0,127}));
  connect(dT_esh_setpoint_block.y,limitsBus_moduleB.dT_esh_setpoint)
    annotation (Line(points={{-183.8925842927367,-47.83786317159279},{-72,-47.83786317159279},{-72,-4}},color={0,0,127}));
  connect(dT_dsh_min_limit_block.y,limitsBus_moduleB.dT_dsh_min_limt)
    annotation (Line(points={{-183.8925842927367,-68.33391034791707},{-72,-68.33391034791707},{-72,-4}},color={0,0,127}));
  connect(capacity_setpoint_block.y,limitsBus_moduleB.capacity_setpoint)
    annotation (Line(points={{-183.8925842927367,-88.82995752424135},{-72,-88.82995752424135},{-72,-4}},color={0,0,127}));
  connect(fanSpeed_setpoint_block.y,limitsBus_moduleB.fanSpeed_setpoint)
    annotation (Line(points={{-183.8925842927367,-109.32600470056562},{-72,-109.32600470056562},{-72,-4}},color={0,0,127}));
  connect(ecoEXV_max_opening_block.y,limitsBus_moduleB.ecoEXV_max_opening)
    annotation (Line(points={{-183.8925842927367,-130.920054404193},{-72,-130.920054404193},{-72,-4}},color={0,0,127}));
  connect(rel_cooler_level_setpoint_block.y,limitsBus_moduleB.rel_cooler_level_setpoint)
    annotation (Line(points={{-183.8925842927367,-151.41610158051728},{-72,-151.41610158051728},{-72,-4}},color={0,0,127}));
  connect(controller.limitsBus_crkA,limitsBus_moduleA)
    annotation (Line(points={{-16.40871812252771,18.73722053715388},{-16.40871812252771,56},{-142,56},{-142,-2}},color={255,204,51}));
  connect(controller.limitsBus_crkB,limitsBus_moduleB)
    annotation (Line(points={{-16.40871812252771,1.1175373549020904},{-42.20435906126386,1.1175373549020904},{-42.20435906126386,-4},{-72,-4}},color={255,204,51}));
  connect(y_equalto_kx_plus_b.y[1],measurementBusA.T_oat)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110,15.768939396847014},{110,111.19694416679201},{98.72980564268204,111.19694416679201}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[2],measurementBusA.T_ewt)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110,15.768939396847014},{110,111.19694416679201},{98.72980564268204,111.19694416679201}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[3],measurementBusA.T_lwt)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110,15.768939396847014},{110,111.19694416679201},{98.72980564268204,111.19694416679201}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[4],measurementBusA.T_sst)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110,15.768939396847014},{110,111.19694416679201},{98.72980564268204,111.19694416679201}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[5],measurementBusA.T_sdt)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110,15.768939396847014},{110,111.19694416679201},{98.72980564268204,111.19694416679201}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[6],measurementBusA.T_dgt)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110,15.768939396847014},{110,111.19694416679201},{98.72980564268204,111.19694416679201}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[7],measurementBusA.dT_ssh)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110,15.768939396847014},{110,111.19694416679201},{98.72980564268204,111.19694416679201}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[8],measurementBusA.dT_esh)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110,15.768939396847014},{110,111.19694416679201},{98.72980564268204,111.19694416679201}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[9],measurementBusA.dT_sbc)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110,15.768939396847014},{110,111.19694416679201},{98.72980564268204,111.19694416679201}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[10],measurementBusA.capacity)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110,15.768939396847014},{110,111.19694416679201},{98.72980564268204,111.19694416679201}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[11],measurementBusA.compressorFrequency)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110,15.768939396847014},{110,111.19694416679201},{98.72980564268204,111.19694416679201}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[12],measurementBusA.rel_cooler_level)
    annotation (Line(points={{99.78409076080227,15.768939396847014},{110,15.768939396847014},{110,111.19694416679201},{98.72980564268204,111.19694416679201}},color={0,0,127}));
  connect(y_equalto_kx_plus_b.y[13],measurementBusA.rel_cooler_level)
    annotation (Line(points={{99.78409076080227,15.768939396847012},{105.78409076080227,15.768939396847012},{105.78409076080227,111.19694416679201},{98.72980564268204,111.19694416679201}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={130,108,108},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Test_ControllerBlock_XBV;
