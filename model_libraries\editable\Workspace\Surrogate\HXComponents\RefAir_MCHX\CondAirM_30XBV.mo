within Workspace.Surrogate.HXComponents.RefAir_MCHX;
model CondAirM_30XBV
  extends.BOLT.Condenser.RefAir.CondAirM(
    n_passes={2},
    n_tubes=[
      89,20],
    n_inlets={2},
    n_outlets={1},
    selector_curve=.BOLT.InternalLibrary.HeatExchangers.RefAir.DataBase.MCHXGeo.SelectorCurve.MCHXCurve98,
    tubeOrientation=.BOLT.InternalLibrary.BuildingBlocks.Types.TubeOrientation.Vertical_inletTop,
    userDefined_geo(
      length_tube=2.4,
      diameter_in_header=0.0378,
      length_tube_inlet=0.050,
      diameter_in_inletTube=0.018925,
      length_outlet=0.05,
      diameter_in_outlet=0.0129),
    dehumidifying=false);
end CondAirM_30XBV;
