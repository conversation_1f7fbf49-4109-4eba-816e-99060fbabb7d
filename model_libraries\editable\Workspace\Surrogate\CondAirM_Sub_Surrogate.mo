within Workspace.Surrogate;
model CondAirM_Sub_Surrogate
  extends Workspace.Surrogate.MCHX_Cond.SubComponents.Configuration(
    Z_dpr_fixed=true,
    use_ZU_in=false,
    Z_U_fixed=true,
    x_out_start=x_out_start,
    OAT=OAT,
    p_in_air_gage_start=p_in_air_gage_start,
    Tsat_in_start=Tsat_in_start);
  .BOLT.InternalLibrary.BuildingBlocks.Refrigerant.Ports.FluidPort_a port_a_ref(
    RefMedium = RefMedium,
    streamID=streamID)
    annotation (Placement(transformation(extent={{-112,-8},{-92,12}}),iconTransformation(extent={{-112,-8},{-92,12}})));
  .BOLT.InternalLibrary.BuildingBlocks.Refrigerant.Ports.FluidPort_b port_b_ref(
    RefMedium = RefMedium,
    streamID=streamID)
    annotation (Placement(transformation(extent={{92,-10},{112,10}}),iconTransformation(extent={{92,-10},{112,10}})));
  .BOLT.InternalLibrary.BuildingBlocks.Air.Ports.FluidPort_a port_a_air(
    redeclare package Medium=AirMedium)
    annotation (Placement(transformation(extent={{-10,-96},{10,-76}}),iconTransformation(extent={{-10,-96},{10,-76}})));
  .BOLT.InternalLibrary.BuildingBlocks.Air.Ports.FluidPort_b port_b_air(
    redeclare package Medium=AirMedium)
    annotation (Placement(transformation(extent={{-10,78},{10,98}}),iconTransformation(extent={{-10,78},{10,98}})));
  .BOLT.InternalLibrary.Controls.SensorConstraint capacity_set(
    setPoint=capacity,
    fixed=capacity_fixed,
    isOff=isOff)
    annotation (Placement(transformation(extent={{-88,-136},{-58,-108}})));
  .Modelica.Blocks.Sources.RealExpression capacityValue(
    y=Q_flow_air)
    annotation (Placement(transformation(extent={{-98,-160},{-78,-140}})));
  .BOLT.InternalLibrary.Controls.SensorConstraint dpr_set(
    isOff=isOff,
    fixed=dpr_fixed,
    setPoint=dpr)
    annotation (Placement(transformation(extent={{-34,-134},{-4,-106}})));
  .Modelica.Blocks.Sources.RealExpression dprValue(
    y=dp_ref)
    annotation (Placement(transformation(extent={{-52,-160},{-32,-140}})));
  .BOLT.InternalLibrary.Controls.Actuator calibration_alpha(
    maxBound=2.2,
    minBound=0.135,
    isOff=isOff,
    isFixed=Z_U_fixed,
    setPoint=Z_U)
    annotation (Placement(transformation(extent={{-80,122},{-100,142}})));
  .BOLT.InternalLibrary.Controls.Actuator calibration_dpr(
    maxBound=2.2,
    minBound=0.135,
    isOff=isOff,
    isFixed=Z_dpr_fixed,
    setPoint=Z_dpr)
    annotation (Placement(transformation(extent={{-80,104},{-100,124}})));
  .Modelica.Blocks.Interfaces.RealInput ZU_in if use_ZU_in
    "External Z_U"
    annotation (Placement(transformation(extent={{-141.0,-39.0},{-99.0,3.0}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{-118,-44},{-100,-26}})));
  .Modelica.Blocks.Interfaces.RealInput Zdpr_in if use_Zdpr_in
    "External Z_dpr"
    annotation (Placement(transformation(extent={{-141.0,-69.0},{-99.0,-27.0}},rotation=0.0,origin={0.0,0.0}),iconTransformation(extent={{-118,-24},{-100,-26}})));
  .Modelica.Blocks.Interfaces.RealInput Z_fanScaling if use_Z_fanScaling
    "External Z_fanScaling"
    annotation (Placement(transformation(extent={{-142,-100},{-100,-58}},rotation=0),iconTransformation(extent={{-118,-76},{-100,-58}})));
  refBaseMedium.MassFlowRate m_flow_in_ref=port_a_ref.m_flow
    "Refrigerant inlet mass flow rate";
  refBaseMedium.AbsolutePressure p_in_ref(
    start=p_in_ref_start);
  AirMedium.MassFlowRate m_flow_in_air=port_a_air.m_flow
    "Air inlet mass flow rate";
  .Modelica.SIunits.PressureDifference p_in_air_gage(
    start=p_in_air_gage_start,
    nominal=10);
  .Modelica.SIunits.VolumeFlowRate V_flow_in_air=m_flow_in_air/d_in_air
    "Air inlet volume flow rate";
  .Modelica.SIunits.VolumeFlowRate V_flow_in_air_scaled=V_flow_in_air*Z_fanScaling_internal
    "Air inlet volume flow rate (scaled)";
  .Modelica.SIunits.Density d_in_air=AirMedium.density(
    state_a_air)
    "Air inlet density";
  AirMedium.Temperature T_in_air=state_a_air.T
    "Air inlet temperature";
  AirMedium.Temperature T_out_air(
    start=LWT_start,
    min=233.15,
    max=400)
    "Air outlet temperature";
  .Modelica.SIunits.Temperature Tsat_in
    "Saturated temperature of refrigerant inlet";
  .Modelica.SIunits.Temperature Tsat_out
    "Saturated temperature of refrigerant outlet";
  .Modelica.SIunits.HeatFlowRate Q_flow_ref
    "Refrigerant heat flow for cooler";
  .Modelica.SIunits.HeatFlowRate Q_flow_air
    "Air heat flow for cooler";
  .Modelica.SIunits.PressureDifference dp_air
    "Pressure drop of coolant side";
  .Modelica.SIunits.PressureDifference dp_ref
    "Pressure drop of refrigerant side";
  .Modelica.SIunits.TemperatureDifference delta_Tsat
    "Pressure drop of coolant side";
  .Modelica.SIunits.Mass mass_ref
    "Total refrigerant charge of cooler";
  Real x_in=state_a_ref.x;
  Real x_out_scaled(
    start=x_out_start,
    min=-1,
    max=1,
    nominal=0.1);
  refBaseMedium.ThermodynamicState state_a_ref
    "Refrigerant state at port a";
  refBaseMedium.ThermodynamicState state_b_ref
    "Refrigerant state at port b";
  AirMedium.ThermodynamicState state_a_air
    "Air state at port a";
  AirMedium.ThermodynamicState state_b_air
    "Air state at port a";
  Real x_out=(x_out_max+x_out_min)/2+x_out_scaled*(x_out_max-x_out_min)/2;
  final parameter Real x_out_scaled_nom=(0.1-(x_out_max+x_out_min)/2)*2/(x_out_max-x_out_min);
  final parameter Real x_out_scaled_start=(x_out_start-(x_out_max+x_out_min)/2)*2/(x_out_max-x_out_min);
  final parameter Real x_out_min=
    if is_HR then
      -0.5
    else
      -0.5;
  // 
  final parameter Real x_out_max=
    if is_HR then
      0.2
    else
      0.1;
  final parameter Boolean is_HR=
    if selector == Workspace.Surrogate.MCHX_Cond.Selector.MCHX_Cond_R134a then
      true
    else
      false;
  parameter.Modelica.SIunits.Length length=1.404
    "tube length";
  parameter.Workspace.Surrogate.MCHX_Cond.Selector selector;
  final parameter.Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.Configuration configuration=Workspace.Surrogate.MCHX_Cond.getSelector(
    selector);
  Modelica.SIunits.TemperatureDifference Tglide=refBaseMedium.Tglide_p(refBase.refName,
    port_a_ref.p);
  Modelica.SIunits.TemperatureDifference delta_Tsat_min=Tglide+1.0E-3
    "Minimal allowed saturation temperature difference";
  final parameter Real delta_Tsat_alpha=1.0E-6
    "The tolerance for delta Tsat";
  Real ZU_input
    "Internal Z_U as input of NNfunction";
  Real Zdpr_input
    "Internal Zdpr as input of NNfunction";
protected
  .Modelica.Blocks.Interfaces.RealInput ZU_internal
    "Internal Z_U for external input";
  .Modelica.Blocks.Interfaces.RealInput Zdpr_internal;
  .Modelica.Blocks.Interfaces.RealInput Z_fanScaling_internal
    "Internal Z_fanScaling for external input";
public
  Real x_out_crit=-0.2;
  Real delta_x_out_interp=0.05;
  //Output Tsat_in
  Modelica.SIunits.Temperature Tsat_in_low=
    if x_out <= x_out_crit then
      Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.NNFunction(
        configuration.layer_Tsat_in,
        m_flow_in_ref,
        x_in,
        x_out_crit,
        V_flow_in_air_scaled,
        T_in_air,
        Zdpr_input,
        ZU_input)
    else
      0;
  Modelica.SIunits.Temperature Tsat_in_high=
    if x_out <= x_out_crit then
      Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.NNFunction(
        configuration.layer_Tsat_in,
        m_flow_in_ref,
        x_in,
        x_out_crit+delta_x_out_interp,
        V_flow_in_air_scaled,
        T_in_air,
        Zdpr_input,
        ZU_input)
    else
      0;
  Real slope_Tsat_in=
    if x_out <= x_out_crit then
      (Tsat_in_high-Tsat_in_low)/delta_x_out_interp
    else
      0;
  Modelica.SIunits.Temperature Tsat_in_extrap=
    if x_out <= x_out_crit then
      slope_Tsat_in*(x_out-x_out_crit)+Tsat_in_low
    else
      0;
  //Output delta_Tsat
  Modelica.SIunits.TemperatureDifference delta_Tsat_low=
    if x_out <= x_out_crit then
      Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.NNFunction(
        configuration.layer_delta_Tsat,
        m_flow_in_ref,
        x_in,
        x_out_crit,
        V_flow_in_air_scaled,
        T_in_air,
        Zdpr_input,
        ZU_input)
    else
      0;
  Modelica.SIunits.TemperatureDifference delta_Tsat_high=
    if x_out <= x_out_crit then
      Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.NNFunction(
        configuration.layer_delta_Tsat,
        m_flow_in_ref,
        x_in,
        x_out_crit+delta_x_out_interp,
        V_flow_in_air_scaled,
        T_in_air,
        Zdpr_input,
        ZU_input)
    else
      0;
  Real slope_delta_Tsat=
    if x_out <= x_out_crit then
      (delta_Tsat_high-delta_Tsat_low)/delta_x_out_interp
    else
      0;
  Modelica.SIunits.TemperatureDifference delta_Tsat_extrap=
    if x_out <= x_out_crit then
      slope_delta_Tsat*(x_out-x_out_crit)+delta_Tsat_low
    else
      0;
  // Output mass_ref
  Modelica.SIunits.Mass mass_ref_low=
    if x_out <= x_out_crit then
      Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.NNFunction(
        configuration.layer_mass_ref,
        m_flow_in_ref,
        x_in,
        x_out_crit,
        V_flow_in_air_scaled,
        T_in_air,
        Zdpr_input,
        ZU_input)
    else
      0;
  Modelica.SIunits.Mass mass_ref_high=
    if x_out <= x_out_crit then
      Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.NNFunction(
        configuration.layer_mass_ref,
        m_flow_in_ref,
        x_in,
        x_out_crit+delta_x_out_interp,
        V_flow_in_air_scaled,
        T_in_air,
        Zdpr_input,
        ZU_input)
    else
      0;
  Real slope_mass_ref=
    if x_out <= x_out_crit then
      (mass_ref_high-mass_ref_low)/delta_x_out_interp
    else
      0;
  Modelica.SIunits.Mass mass_ref_extrap=
    if x_out <= x_out_crit then
      slope_mass_ref*(x_out-x_out_crit)+mass_ref_low
    else
      0;
  // Output dp_air
  Modelica.SIunits.PressureDifference dp_air_low=
    if x_out <= x_out_crit then
      Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.NNFunction(
        configuration.layer_dp_air,
        m_flow_in_ref,
        x_in,
        x_out_crit,
        V_flow_in_air_scaled,
        T_in_air,
        Zdpr_input,
        ZU_input)
    else
      0;
  Modelica.SIunits.PressureDifference dp_air_high=
    if x_out <= x_out_crit then
      Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.NNFunction(
        configuration.layer_dp_air,
        m_flow_in_ref,
        x_in,
        x_out_crit+delta_x_out_interp,
        V_flow_in_air_scaled,
        T_in_air,
        Zdpr_input,
        ZU_input)
    else
      0;
  Real slope_dp_air=
    if x_out <= x_out_crit then
      (dp_air_high-dp_air_low)/delta_x_out_interp
    else
      0;
  Modelica.SIunits.PressureDifference dp_air_extrap=
    if x_out <= x_out_crit then
      slope_dp_air*(x_out-x_out_crit)+dp_air_low
    else
      0;
equation
  port_a_ref.p=p_in_ref;
  port_a_air.p=p_in_air_gage+p_atm;
  port_a_ref.m_flow*inStream(
    port_a_ref.h_outflow)+Q_flow_ref+port_b_ref.m_flow*port_b_ref.h_outflow=0;
  port_a_air.m_flow*Z_fanScaling_internal*inStream(
    port_a_air.h_outflow)+Q_flow_air+port_b_air.m_flow*Z_fanScaling_internal*port_b_air.h_outflow=0
    annotation (__Modelon(ResidualEquation(iterationVariable(hold=isOff)=T_out_air,hold=isOff),name=EnergyEqAir));
  Q_flow_air+Q_flow_ref=0;
  port_a_ref.m_flow+port_b_ref.m_flow=0;
  port_a_air.m_flow+port_b_air.m_flow=0;
  port_a_ref.p=port_b_ref.p+dp_ref;
  port_a_air.p=port_b_air.p+dp_air
    annotation (__Modelon(ResidualEquation(iterationVariable(hold=isOff)=p_in_air_gage,hold=isOff),name=MomentumEqAir));
  port_a_ref.h_outflow=inStream(
    port_b_ref.h_outflow);
  port_b_ref.h_outflow=refBaseMedium.specificEnthalpy_px(refBase.refName,
    port_b_ref.p,
    x_out);
  port_a_ref.S_outflow=inStream(
    port_b_ref.S_outflow);
  port_b_ref.S_outflow=inStream(
    port_a_ref.S_outflow);
  port_a_air.h_outflow=inStream(
    port_b_air.h_outflow);
  //C_outflow  instream integration
  port_a_ref.C_outflow=inStream(
    port_b_ref.C_outflow);
  port_b_ref.C_outflow=inStream(
    port_a_ref.C_outflow);
  port_b_air.h_outflow=AirMedium.specificEnthalpy_pTX(
    port_b_air.p,
    T_out_air,
    inStream(
      port_b_air.Xi_outflow));
  port_a_air.Xi_outflow=inStream(
    port_b_air.Xi_outflow);
  port_b_air.Xi_outflow=inStream(
    port_a_air.Xi_outflow);
  state_a_air=AirMedium.setState_phX(
    port_a_air.p,
    inStream(
      port_a_air.h_outflow),
    inStream(
      port_a_air.Xi_outflow));
  state_b_air=AirMedium.setState_phX(
    port_b_air.p,
    port_b_air.h_outflow,
    port_b_air.Xi_outflow);
  state_a_ref=refBaseMedium.setState_ph(refBase.refName,
    port_a_ref.p,
    inStream(
      port_a_ref.h_outflow));
  state_b_ref=refBaseMedium.setState_ph(refBase.refName,
    port_b_ref.p,
    port_b_ref.h_outflow);
  ZU_input=
    if not Z_U_fixed then
      calibration_alpha.value
    elseif not use_ZU_in then
      calibration_alpha.value
    else
      ZU_internal;
  Zdpr_input=
    if not Z_dpr_fixed then
      calibration_dpr.value
    elseif not use_Zdpr_in then
      calibration_dpr.value
    else
      Zdpr_internal;
  Tsat_in=state_a_ref.Tsat
    annotation (__Modelon(ResidualEquation(iterationVariable(hold=isOff or isOff_ref)=x_out_scaled,hold=isOff or isOff_ref),name=StateARefTsat));
  Tsat_out=state_b_ref.Tsat;
  Tsat_in=Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.NNFunction(
    configuration.layer_Tsat_in,
    m_flow_in_ref,
    x_in,
    x_out,
    V_flow_in_air_scaled,
    T_in_air,
    Zdpr_input,
    ZU_input);
  Tsat_in=Tsat_out+delta_Tsat
    annotation (__Modelon(ResidualEquation(iterationVariable(hold=isOff or isOff_ref)=p_in_ref,hold=isOff or isOff_ref),name=TsatIn));
  delta_Tsat=.BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax(
    Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.NNFunction(
      configuration.layer_delta_Tsat,
      m_flow_in_ref,
      x_in,
      x_out,
      V_flow_in_air_scaled,
      T_in_air,
      Zdpr_input,
      ZU_input),
    delta_Tsat_min,
    delta_Tsat_alpha);
  mass_ref=Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.NNFunction(
    configuration.layer_mass_ref,
    m_flow_in_ref,
    x_in,
    x_out,
    V_flow_in_air_scaled,
    T_in_air,
    Zdpr_input,
    ZU_input);
  dp_air=Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM.NNFunction(
    configuration.layer_dp_air,
    m_flow_in_ref,
    x_in,
    x_out,
    V_flow_in_air,
    T_in_air,
    Zdpr_input,
    ZU_input);
  connect(capacityValue.y,capacity_set.sensor)
    annotation (Line(points={{-77,-150},{-73,-150},{-73,-138.52}},color={0,0,127}));
  if use_ZU_in then
    connect(ZU_in,ZU_internal);
  else
    ZU_internal=1;
  end if;
  if use_Zdpr_in then
    connect(Zdpr_in,Zdpr_internal);
  else
    Zdpr_internal=1;
  end if;
  if use_Z_fanScaling then
    connect(Z_fanScaling,Z_fanScaling_internal);
  else
    Z_fanScaling_internal=1;
  end if;
  connect(dprValue.y,dpr_set.sensor)
    annotation (Line(points={{-31,-150},{-18,-150},{-18,-136.52},{-19,-136.52}},color={0,0,127}));
  annotation (
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100,-100},{100,100}})),
    Icon(
      coordinateSystem(
        preserveAspectRatio=false),
      graphics={
        Rectangle(
          extent={{-88,64},{-72,-66}},
          fillPattern=FillPattern.HorizontalCylinder,
          pattern=LinePattern.None,
          lineColor={0,0,0},
          fillColor={238,46,47}),
        Rectangle(
          extent={{72,64},{88,-66}},
          fillPattern=FillPattern.HorizontalCylinder,
          pattern=LinePattern.None,
          lineColor={0,0,0},
          fillColor={238,46,47}),
        Line(
          points={{-72,6},{-60,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,6},{-60,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,6},{-34,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,6},{-34,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,6},{-8,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,6},{-8,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,6},{18,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,6},{18,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,6},{44,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,6},{44,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,6},{70,-8}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,-18},{-60,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-72,-18},{-60,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,-18},{-34,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,-18},{-34,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,-18},{-8,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,-18},{-8,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,-18},{18,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,-18},{18,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,-18},{44,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,-18},{44,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,-42},{-60,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-72,-42},{-60,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,-42},{-34,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,-42},{-34,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,-42},{-8,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,-42},{-8,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,-42},{18,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,-42},{18,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,-42},{44,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,-42},{44,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,-18},{70,-32}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,-42},{70,-56}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,30},{-60,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-72,30},{-60,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,30},{-34,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,30},{-34,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,30},{-8,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,30},{-8,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,30},{18,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,30},{18,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,30},{44,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,30},{44,16}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,30},{70,16}},
          color={0,0,0},
          thickness=0.5),
        Rectangle(
          extent={{-72,64},{72,54}},
          lineColor={0,0,0},
          lineThickness=1,
          fillPattern=FillPattern.HorizontalCylinder,
          fillColor={238,46,47}),
        Line(
          points={{-46,54},{-60,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-72,54},{-60,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,54},{-34,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-46,54},{-34,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,54},{-8,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{-20,54},{-8,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,54},{18,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{6,54},{18,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,54},{44,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{32,54},{44,40}},
          color={0,0,0},
          thickness=0.5),
        Line(
          points={{58,54},{70,40}},
          color={0,0,0},
          thickness=0.5),
        Rectangle(
          extent={{-72,64},{72,54}},
          lineThickness=1,
          lineColor={0,0,0}),
        Rectangle(
          extent={{-88,64},{-72,-66}},
          lineThickness=1,
          lineColor={0,0,0}),
        Rectangle(
          extent={{72,64},{88,-66}},
          lineThickness=1,
          lineColor={0,0,0}),
        Rectangle(
          extent={{-72,40},{72,30}},
          lineColor={0,0,0},
          lineThickness=1,
          fillPattern=FillPattern.HorizontalCylinder,
          fillColor={238,46,47}),
        Rectangle(
          extent={{-72,40},{72,30}},
          lineThickness=1,
          lineColor={0,0,0}),
        Rectangle(
          extent={{-72,16},{72,6}},
          lineColor={0,0,0},
          lineThickness=1,
          fillPattern=FillPattern.HorizontalCylinder,
          fillColor={238,46,47}),
        Rectangle(
          extent={{-72,16},{72,6}},
          lineThickness=1,
          lineColor={0,0,0}),
        Rectangle(
          extent={{-72,-8},{72,-18}},
          lineColor={0,0,0},
          lineThickness=1,
          fillPattern=FillPattern.HorizontalCylinder,
          fillColor={238,46,47}),
        Rectangle(
          extent={{-72,-8},{72,-18}},
          lineThickness=1,
          lineColor={0,0,0}),
        Rectangle(
          extent={{-72,-32},{72,-42}},
          lineColor={0,0,0},
          lineThickness=1,
          fillPattern=FillPattern.HorizontalCylinder,
          fillColor={238,46,47}),
        Rectangle(
          extent={{-72,-32},{72,-42}},
          lineThickness=1,
          lineColor={0,0,0}),
        Rectangle(
          extent={{-72,-56},{72,-66}},
          lineColor={0,0,0},
          lineThickness=1,
          fillPattern=FillPattern.HorizontalCylinder,
          fillColor={238,46,47}),
        Rectangle(
          extent={{-72,-56},{72,-66}},
          lineThickness=1,
          lineColor={0,0,0})}),
    Documentation(
      info="<html>
</html>",
      revisions="<html>
<p></p>
</html>"));
end CondAirM_Sub_Surrogate;
