within StateMachine;
record CrkABCDEcoABCD_16
  import BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod;
  extends Mode30XBVBase(
    final modeID=ModeID30XBV.CrkABCDEcoABCD_16,
    final CrkA=true,
    final CrkB=true,
    final CrkC=true,
    final CrkD=true,
    final EcoA=true,
    final EcoB=true,
    final EcoC=true,
    final EcoD=true,
    final ViA=false,
    final ViB=false,
    final ViC=false,
    final ViD=false,
    initMethod=ModeInitMethod.External);
end CrkABCDEcoABCD_16;
