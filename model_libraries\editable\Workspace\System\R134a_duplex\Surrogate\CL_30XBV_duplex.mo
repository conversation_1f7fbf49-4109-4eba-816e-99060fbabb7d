within Workspace.System.R134a_duplex.Surrogate;
model CL_30XBV_duplex
  extends.Workspace.System.BaseCycle.DUPLEX.Surrogate.System_30XBV(
    ECAT(
      EvapBrineEWT_K(
        setPoint=12+273.15),
      EvapFoulingFactor_m2KW(
        setPoint=0),
      EvapBrineFlowRate_m3s(
        setPoint=0.02)),
    choiceBlock(
      FAN_SPEED_selector=Workspace.Auxiliary.OptionBlock.FanSpeed.Selector.FAN_FS,
      SixtyHz_selector=Workspace.Auxiliary.OptionBlock.SixtyHzOption.SixtyHz_selector.STANDARD,
      HighAmbiant_selector=Workspace.Auxiliary.OptionBlock.HighAmbiantTemperature.HighAmbiant_selector.STANDARD,
      Coating_selector=Workspace.Auxiliary.OptionBlock.CoatingOption.Coating_selector.STANDARD,
      Selector_ModuleB=Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Selector.Unit_30XBV_0900_Duplex,
      Selector_ModuleA=Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Selector.Unit_30XBV_0900_Duplex),
    use_bf=true)
    annotation (Icon(graphics={Text(textString="CL",origin={2,-12},extent={{-100,75},{100,-75}})}));
  annotation (
    Icon(
      graphics={
        Text(
          textString="CL",
          origin={-2,-10},
          extent={{-96.01685393258424,70.66502830561285},{97,-70}})}));
end CL_30XBV_duplex;
