within Workspace.System.BaseCycle.Optimization;
model Equipment_opti
  extends.Workspace.System.BaseCycle.XBV_2C.OpenLoop_EN14511(
    evaporator(
      userDefined_geo1(
        length_tube=length_tube_evap_crkA,
        diameter_in_shell=diameter_in_shell_evap,
        n_tubes_pass1=n_tubes_passe1_evap,
        n_tubes_pass2=n_tubes_passe2_evap),
      selector_tube=evap_selector_tube,
      n_passes=n_passes_evap,
      userDefined_geo2(
        length_tube=length_tube_evap_crkB,
        diameter_in_shell=diameter_in_shell_evap,
        n_tubes_pass1=n_tubes_passe1_evap,
        n_tubes_pass2=n_tubes_passe2_evap)),
    BPHEECOA(
      nPlate=n_plate_crkA,
      selectGeo=Eco_Geo_CKA,
      useOri=true),
    BPHEECOB(
      nPlate=n_plate_crkB,
      selectGeo=Eco_Geo_CKB),
    CompressorA(
      voltage=voltage_crkA,
      selector_comp_NG1_NG2=Selector_comp_crkA),
    CompressorB(
      voltage=voltage_crkB,
      selector_comp_NG1_NG2=Selector_comp_crkB),
    oilseparatorA(
      Di=Di_oil_crkA,
      crossArea=.Modelica.Constants.pi*Di_oil_crkA*Di_oil_crkA/(4),
      perimeter=.Modelica.Constants.pi*Di_oil_crkA,
      dp_reference=dp_ref_oil_crkA,
      m_flow_reference=m_flow_ref_oil_crkA,
      length=length_oil_crkA),
    oilseparatorB(
      Di=Di_oil_crkB,
      crossArea=.Modelica.Constants.pi*Di_oil_crkB*Di_oil_crkB/(4),
      perimeter=.Modelica.Constants.pi*Di_oil_crkB,
      dp_reference=dp_ref_oil_crkB,
      m_flow_reference=m_flow_ref_oil_crkB,
      length=length_oil_crkB),
    suctionlineA(
      dp_reference=dp_ref_SL_crkA,
      m_flow_reference=m_flow_ref_SL_crkA),
    suctionlineB(
      dp_reference=dp_ref_SL_crkB,
      m_flow_reference=m_flow_ref_SL_crkB),
    dischargelineA(
      dp_reference=dp_ref_DL_crkA,
      m_flow_reference=m_flow_ref_DL_crkA),
    dischargelineB(
      dp_reference=dp_ref_DL_crkB,
      m_flow_reference=m_flow_ref_DL_crkB),
    VFDA(
      selector_GenScrew=selector_VFDA_CKA),
    VFDB(
      selector_GenScrew=selector_VFDA_CKB),
    SubcoolingA=-5,
    SubcoolingB=-5,
    FanA(
      use_Z_V_flow_expression=false,
      nStage=1,
      use_nStage_in=true,
      use_scalingFactor_in=false,
      nStage_fixed=true,
      scalingFactor_fixed=true),
    FanB(
      use_Z_V_flow_expression=false,
      nStage=1,
      use_nStage_in=true,
      use_scalingFactor_in=false,
      scalingFactor_fixed=true,
      nStage_fixed=true),
    motorA(
      use_z_power_expression=true,
      Z_power_expression=calibrationBlock.Zfan_A,
      use_shaftSpeed_in=false,
      MotorHz_fixed=not isFanSpeedUnfixedA),
    motorB(
      use_z_power_expression=true,
      Z_power_expression=calibrationBlock.Zfan_B,
      use_shaftSpeed_in=false,
      MotorHz_fixed=not isFanSpeedUnfixedB,
      use_scaling_motor_in=true),
    frq_fan_A(
      y=Fan_crkA),
    frq_fan_B(
      y=Fan_crkB),
    globalParameters(
      capacity_design=capacity_design),
    condAirA(
      T_a_start_air=globalParameters.T_ambient,
      T_b_start_air=globalParameters.T_ambient+10.0,
      Tsat_a_start_ref=globalParameters.Tsat_cond_start,
      Tsat_b_start_ref=globalParameters.Tsat_cond_start-1.0,
      Z_U=0.74,
      use_Z_U_expression=false,
      use_Z_dpr_expression=false,
      n_passes={2},
      n_tubes={{90,30}},
      n_inlets={2},
      n_outlets={1},
      selector_curve=.BOLT.InternalLibrary.HeatExchangers.RefAir.DataBase.MCHXGeo.SelectorCurve.MCHXCurve90,
      tubeOrientation=.BOLT.InternalLibrary.BuildingBlocks.Types.TubeOrientation.Vertical_inletTop,
      userDefined_geo(
        length_tube=1.937,
        diameter_in_header=0.02395,
        length_tube_inlet=0.02286,
        diameter_in_inletTube=0.0157,
        length_outlet=0.02286,
        diameter_in_outlet=0.0157),
      dehumidifying=false,
      fastMode=true,
      selector_curve_num=106,
      Z_dpr_fixed=true,
      Z_dpa_fixed=true),
    condAirB(
      T_a_start_air=globalParameters.T_ambient,
      T_b_start_air=globalParameters.T_ambient+10.0,
      Tsat_a_start_ref=globalParameters.Tsat_cond_start,
      Tsat_b_start_ref=globalParameters.Tsat_cond_start-1.0,
      Z_U=0.74,
      use_Z_U_expression=false,
      use_Z_dpr_expression=false,
      n_passes={2},
      n_tubes={{90,30}},
      n_inlets={2},
      n_outlets={1},
      selector_curve=.BOLT.InternalLibrary.HeatExchangers.RefAir.DataBase.MCHXGeo.SelectorCurve.MCHXCurve90,
      tubeOrientation=.BOLT.InternalLibrary.BuildingBlocks.Types.TubeOrientation.Vertical_inletTop,
      userDefined_geo(
        length_tube=1.937,
        diameter_in_header=0.02395,
        length_tube_inlet=0.02286,
        diameter_in_inletTube=0.0157,
        length_outlet=0.02286,
        diameter_in_outlet=0.0157),
      dehumidifying=false,
      fastMode=true,
      selector_curve_num=106),
    VIA(
      k=Vi_A),
    VIB(
      k=Vi_B),
    airFlowA(
      y=sinkAirA.summary.Vd_flow),
    airFlowB(
      y=sinkAirB.summary.Vd_flow),
    grossPower(
      y=systemVariables.summary.pow_total),
    grossHeatCap(
      y=0),
    brineDPi_cond(
      y=0),
    brineFlow_cond(
      y=0),
    brineDP(
      y=0),
    businessFactors(
      use_business_factor=use_bf),
    FrqComp_A(
      y=CompressorA.summary.Ncomp),
    FrqComp_B(
      y=CompressorB.summary.Ncomp),
    sinkbrine(
      T_fixed=not isLWTUnfixed),
    sourcebrine(
      T_fixed=not isEWTUnfixed),
    sourceAirA(
      Tdb_fixed=not isOATUnfixed),
    sourceAirB(
      Tdb_fixed=not isOATUnfixed));
  .Modelica.Blocks.Sources.RealExpression scA(
    y=nodecondAiroutA.dTsh)
    annotation (Placement(transformation(extent={{175.0,458.25},{195.0,478.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dshA(
    y=nodeCpoutA.dTsh)
    annotation (Placement(transformation(extent={{175.0,476.25},{195.0,496.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression eshA(
    y=nodeBPHEECOoutA.dTsh)
    annotation (Placement(transformation(extent={{175.0,496.25},{195.0,516.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sshA(
    y=nodeCpinA.dTsh)
    annotation (Placement(transformation(extent={{175.0,514.25},{195.0,534.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dgtA(
    y=nodeCpoutA.T)
    annotation (Placement(transformation(extent={{173.0,540.25},{193.0,560.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sdtA(
    y=nodeCpoutA.Tsat)
    annotation (Placement(transformation(extent={{173.0,556.25},{193.0,576.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sstA(
    y=nodeCpinA.Tsat)
    annotation (Placement(transformation(extent={{173.0,574.25},{193.0,594.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_levelB(
    y=evaporator.summary.relLevel_topBundle2)
    annotation (Placement(transformation(extent={{465.0,594.25},{445.0,614.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sstB(
    y=nodeCpinB.Tsat)
    annotation (Placement(transformation(extent={{465.0,576.25},{445.0,596.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sdtB(
    y=nodeCpoutB.Tsat)
    annotation (Placement(transformation(extent={{465.0,558.25},{445.0,578.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dgtB(
    y=nodeCpoutB.T)
    annotation (Placement(transformation(extent={{465.0,542.25},{445.0,562.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression sshB(
    y=nodeCpinB.dTsh)
    annotation (Placement(transformation(extent={{467.0,516.25},{447.0,536.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression eshB(
    y=nodeBPHEECOoutB.dTsh)
    annotation (Placement(transformation(extent={{467.0,498.25},{447.0,518.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dshB(
    y=nodeCpoutB.dTsh)
    annotation (Placement(transformation(extent={{467.0,478.25},{447.0,498.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression scB(
    y=nodecondAiroutB.dTsh)
    annotation (Placement(transformation(extent={{467.0,460.25},{447.0,480.25}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression totalCapacity(
    y=controlledCapacity)
    annotation (Placement(transformation(extent={{308.0,588.0},{328.0,608.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression oat(
    y=sourceAirA.summary.Tdb)
    annotation (Placement(transformation(extent={{308.0,574.0},{328.0,594.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression ewt(
    y=sourcebrine.summary.T)
    annotation (Placement(transformation(extent={{308.5,555.125},{328.5,575.125}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression lwt(
    y=sinkbrine.summary.T)
    annotation (Placement(transformation(extent={{308.0,538.0},{328.0,558.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBusA
    annotation (Placement(transformation(extent={{257.0,427.0},{297.0,467.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-55.913,90.783},{-15.913,130.783}})));
  .Workspace.Interfaces.MeasurementBus measurementBusB
    annotation (Placement(transformation(extent={{346.0,428.0},{386.0,468.0}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{18.087,90.783},{58.087,130.783}})));
  .Modelica.Blocks.Interfaces.RealInput Fan_crkA
    annotation (Placement(transformation(extent={{-341.0608674607705,340.9390771820128},{-330.9391325392295,351.0609228179872}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,50})));
  .Modelica.Blocks.Interfaces.RealInput EXV_Main_crkA
    annotation (Placement(transformation(extent={{-297.0608674607705,-9.060922817987198},{-286.9391325392295,1.0609228179871977}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,30})));
  .Modelica.Blocks.Interfaces.RealInput EXV_Eco_crkA
    annotation (Placement(transformation(extent={{-297.0608674607705,48.939077182012795},{-286.9391325392295,59.060922817987205}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,-10})));
  .Modelica.Blocks.Interfaces.RealInput Speed_crkA
    annotation (Placement(transformation(extent={{-66.93913253922949,114.9390771820128},{-77.06086746077051,125.0609228179872}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,-30})));
  .Modelica.Blocks.Interfaces.RealInput Speed_crkB
    annotation (Placement(transformation(extent={{26.93913253922949,112.9390771820128},{37.06086746077051,123.0609228179872}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,-50})));
  .Modelica.Blocks.Interfaces.RealInput Fan_crkB
    annotation (Placement(transformation(extent={{307.0608674607705,350.9390771820128},{296.9391325392295,361.0609228179872}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,-70})));
  .Modelica.Blocks.Interfaces.RealInput EXV_Main_crkB
    annotation (Placement(transformation(extent={{271.0608674607705,-17.060922817987198},{260.9391325392295,-6.939077182012802}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,-90})));
  .Modelica.Blocks.Interfaces.RealInput EXV_Eco_crkB
    annotation (Placement(transformation(extent={{269.5294773255867,48.47046219140777},{258.4705226744133,59.52953780859223}},origin={0.0,0.0},rotation=0.0),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,-130})));
  parameter Real capacity_design=600000;
  .Workspace.Auxiliary.FanSignal.FanSignalProcessor fanSignalProcessorA(
    is_fixedSpeed=is_fixedSpeed,
    max_fan_rpm=max_fan_rpm,
    max_motor_frequency=max_motor_frequency,
    limit_max_rpm=limit_max_fan_rpm,
    limit_max_frequency=max_motor_frequency)
    annotation (Placement(transformation(extent={{-306.28725628581213,325.71274371418787},{-273.71274371418787,358.28725628581213}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.FanSignal.FanSignalProcessor fanSignalProcessorB(
    is_fixedSpeed=is_fixedSpeed,
    max_fan_rpm=max_fan_rpm,
    max_motor_frequency=max_motor_frequency,
    limit_max_rpm=limit_max_fan_rpm,
    limit_max_frequency=max_motor_frequency)
    annotation (Placement(transformation(extent={{281.32207172919914,334.67792827080086},{250.67792827080086,365.32207172919914}},origin={0.0,0.0},rotation=0.0)));
  parameter Real OAT=35
    annotation (Dialog(tab="Boundary Conditions"));
  parameter Real EWT=12
    annotation (Dialog(tab="Boundary Conditions"));
  parameter Real LWT=7
    annotation (Dialog(tab="Boundary Conditions"));
  parameter Real delta_T=5
    annotation (Dialog(tab="Boundary Conditions"));
  parameter Boolean Vi_A=true
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Boolean Vi_B=true
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real NcompAMax
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real NcompBMax
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real NcompAMin
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real NcompBMin
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.Public_database.Compressor.PD_3Port.ScrewMap.Polynomial10.VI.Selector Selector_comp_crkA
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.Public_database.Compressor.PD_3Port.ScrewMap.Polynomial10.VI.Selector Selector_comp_crkB
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real Frq_Comp_A=95
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real Frq_Comp_B=95
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector selector_VFDA_CKA
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector selector_VFDA_CKB
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.Public_database.Evaporator.Tube.Selector evap_selector_tube
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter.BlackBoxLibrary.GeoSelector.GeoSelector_RefRef Eco_Geo_CKA
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter.BlackBoxLibrary.GeoSelector.GeoSelector_RefRef Eco_Geo_CKB
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter Real n_passes_evap
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter Real length_tube_evap_crkA
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter Real length_tube_evap_crkB
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter Real diameter_in_shell_evap
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter Integer n_tubes_passe1_evap
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter Integer n_tubes_passe2_evap
    annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter Integer n_plate_crkA
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter Integer n_plate_crkB
    annotation (Dialog(group="BPHEECO",tab="Unit Characteristics"));
  parameter Real voltage_crkA
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real voltage_crkB
    annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter Real max_fan_rpm=940
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real max_motor_frequency=50
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Real limit_max_fan_rpm=940
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Boolean is60HZ=false
    annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Integer n_coilsA=7
    annotation (Dialog(group="CondAir",tab="Unit Characteristics"));
  parameter Integer n_coilsB=5
    annotation (Dialog(group="CondAir",tab="Unit Characteristics"));
  parameter Integer n_coil_crkA
    annotation (Dialog(group="CondAir",tab="Unit Characteristics"));
  parameter Integer n_coil_crkB
    annotation (Dialog(group="CondAir",tab="Unit Characteristics"));
  parameter Real Di_oil_crkA
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real Di_oil_crkB
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real length_oil_crkA
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real length_oil_crkB
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real dp_ref_oil_crkA
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real dp_ref_oil_crkB
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real m_flow_ref_oil_crkA
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real m_flow_ref_oil_crkB
    annotation (Dialog(group="OilSeparator",tab="Unit Characteristics"));
  parameter Real dp_ref_SL_crkA
    annotation (Dialog(group="SuctionLine",tab="Unit Characteristics"));
  parameter Real dp_ref_SL_crkB
    annotation (Dialog(group="SuctionLine",tab="Unit Characteristics"));
  parameter Real m_flow_ref_SL_crkA
    annotation (Dialog(group="SuctionLine",tab="Unit Characteristics"));
  parameter Real m_flow_ref_SL_crkB
    annotation (Dialog(group="SuctionLine",tab="Unit Characteristics"));
  parameter Real dp_ref_DL_crkA
    annotation (Dialog(group="DischargeLine",tab="Unit Characteristics"));
  parameter Real dp_ref_DL_crkB
    annotation (Dialog(group="DischargeLine",tab="Unit Characteristics"));
  parameter Real m_flow_ref_DL_crkA
    annotation (Dialog(group="DischargeLine",tab="Unit Characteristics"));
  parameter Real m_flow_ref_DL_crkB
    annotation (Dialog(group="DischargeLine",tab="Unit Characteristics"));
  .Modelica.Blocks.Interfaces.RealInput actuatorSSTmaxA
    annotation (Placement(transformation(extent={{5.874783593258314,5.874783593258314},{-5.874783593258314,-5.874783593258314}},rotation=180.0,origin={-70.0,441.5}),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,10})));
  .Modelica.Blocks.Interfaces.RealInput actuatorSSTmaxB
    annotation (Placement(transformation(extent={{6.498213986187395,6.498213986187409},{-6.498213986187395,-6.498213986187409}},rotation=180.0,origin={-70.0,426.5}),iconTransformation(extent={{-72.8,30.4},{-56.8,46.4}},origin={-49.2,-110})));
  .Modelica.Blocks.Sources.RealExpression rel_cooler_levelA(
    y=evaporator.summary.relLevel_topBundle1)
    annotation (Placement(transformation(extent={{172.0,592.0},{192.0,612.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Boolean use_bf=true
    annotation (Dialog(tab="BusinessFactor&EN14511"));
  //"if false BF's are set to 1 and controlledCapacity = engineering capacity"    
  parameter Boolean use_en=true
    annotation (Dialog(tab="BusinessFactor&EN14511"));
  //"if false controlledCapacity = gross capacity" ;
  .Modelica.SIunits.Power controlledCapacity;
  .Modelica.SIunits.Power controlledHeating;
  .Modelica.SIunits.Power controlledPower;
  Real controlledEfficiency;
  Real controlledHeatingEfficiency;
  .Modelica.SIunits.Power summary_capacity_measurement=controlledCapacity;
  // Optimization
  parameter Boolean isFanSpeedUnfixedA=false
    annotation (Dialog(tab="Optimization",group="Degrees of Freedom"));
  parameter Boolean isFanSpeedUnfixedB=false
    annotation (Dialog(tab="Optimization",group="Degrees of Freedom"));
  parameter Boolean isOATUnfixed=false
    annotation (Dialog(tab="Optimization",group="Degrees of Freedom"));
  parameter Boolean isEWTUnfixed=false
    annotation (Dialog(tab="Optimization",group="Degrees of Freedom"));
  parameter Boolean isLWTUnfixed=false
    annotation (Dialog(tab="Optimization",group="Degrees of Freedom"));
equation
  controlledCapacity=
    if use_en then
      if use_bf then
        businessFactors.pub_net_cool_cap
      else
        en14511.inst_net_cap
    else
      if use_bf then
        businessFactors.pub_gross_cool_cap
      else
        en14511.inst_gross_cap;
  controlledHeating=
    if use_en then
      if use_bf then
        businessFactors.pub_net_heat_cap
      else
        en14511.inst_net_heat
    else
      if use_bf then
        businessFactors.pub_gross_heat_cap
      else
        en14511.inst_gross_heat;
  controlledEfficiency=
    if use_en then
      if use_bf then
        businessFactors.pub_net_cool_cap/businessFactors.pub_net_pow
      else
        en14511.inst_net_cap/en14511.inst_net_pow
    else
      if use_bf then
        businessFactors.pub_gross_cool_cap/businessFactors.pub_gross_pow
      else
        en14511.inst_gross_cap/en14511.inst_gross_pow;
  controlledHeatingEfficiency=
    if use_en then
      if use_bf then
        businessFactors.pub_net_heat_cap/businessFactors.pub_net_pow
      else
        en14511.inst_net_heat/en14511.inst_net_pow
    else
      if use_bf then
        businessFactors.pub_gross_heat_cap/businessFactors.pub_gross_pow
      else
        en14511.inst_gross_heat/en14511.inst_gross_pow;
  controlledPower=
    if use_en then
      if use_bf then
        businessFactors.pub_net_pow
      else
        en14511.inst_net_pow
    else
      if use_bf then
        businessFactors.pub_gross_pow
      else
        en14511.inst_gross_pow;
  connect(sstA.y,measurementBusA.T_sst)
    annotation (Line(points={{194,584.25},{214,584.25},{214,447},{277,447}},color={0,0,127}));
  connect(sdtA.y,measurementBusA.T_sdt)
    annotation (Line(points={{194,566.25},{214,566.25},{214,447},{277,447}},color={0,0,127}));
  connect(dgtA.y,measurementBusA.T_dgt)
    annotation (Line(points={{194,550.25},{214,550.25},{214,447},{277,447}},color={0,0,127}));
  connect(sshA.y,measurementBusA.dT_ssh)
    annotation (Line(points={{196,524.25},{214,524.25},{214,447},{277,447}},color={0,0,127}));
  connect(eshA.y,measurementBusA.dT_esh)
    annotation (Line(points={{196,506.25},{214,506.25},{214,447},{277,447}},color={0,0,127}));
  connect(dshA.y,measurementBusA.dT_dsh)
    annotation (Line(points={{196,486.25},{214,486.25},{214,447},{277,447}},color={0,0,127}));
  connect(oat.y,measurementBusA.T_oat)
    annotation (Line(points={{329,584},{296,584},{296,447},{277,447}},color={0,0,127}));
  connect(ewt.y,measurementBusA.T_ewt)
    annotation (Line(points={{329.5,565.125},{296,565.125},{296,447},{277,447}},color={0,0,127}));
  connect(lwt.y,measurementBusA.T_lwt)
    annotation (Line(points={{329,548},{296,548},{296,447},{277,447}},color={0,0,127}));
  connect(totalCapacity.y,measurementBusA.capacity)
    annotation (Line(points={{329,598},{296,598},{296,447},{277,447}},color={0,0,127}));
  connect(rel_cooler_levelB.y,measurementBusB.rel_cooler_level)
    annotation (Line(points={{444,604.25},{430,604.25},{430,448},{366,448}},color={0,0,127}));
  connect(sstB.y,measurementBusB.T_sst)
    annotation (Line(points={{444,586.25},{430,586.25},{430,448},{366,448}},color={0,0,127}));
  connect(dgtB.y,measurementBusB.T_dgt)
    annotation (Line(points={{444,552.25},{430,552.25},{430,448},{366,448}},color={0,0,127}));
  connect(sshB.y,measurementBusB.dT_ssh)
    annotation (Line(points={{446,526.25},{430,526.25},{430,448},{366,448}},color={0,0,127}));
  connect(eshB.y,measurementBusB.dT_esh)
    annotation (Line(points={{446,508.25},{430,508.25},{430,448},{366,448}},color={0,0,127}));
  connect(dshB.y,measurementBusB.dT_dsh)
    annotation (Line(points={{446,488.25},{430,488.25},{430,448},{366,448}},color={0,0,127}));
  connect(totalCapacity.y,measurementBusB.capacity)
    annotation (Line(points={{329,598},{344,598},{344,448},{366,448}},color={0,0,127}));
  connect(oat.y,measurementBusB.T_oat)
    annotation (Line(points={{329,584},{344,584},{344,448},{366,448}},color={0,0,127}));
  connect(ewt.y,measurementBusB.T_ewt)
    annotation (Line(points={{329.5,565.125},{344,565.125},{344,448},{366,448}},color={0,0,127}));
  connect(lwt.y,measurementBusB.T_lwt)
    annotation (Line(points={{329,548},{344,548},{344,448},{366,448}},color={0,0,127}));
  connect(scA.y,measurementBusA.dT_sbc)
    annotation (Line(points={{196,468.25},{214,468.25},{214,447},{277,447}},color={0,0,127}));
  connect(scB.y,measurementBusB.dT_sbc)
    annotation (Line(points={{446,470.25},{430,470.25},{430,448},{366,448}},color={0,0,127}));
  connect(sdtA.y,measurementBusA.T_sdt)
    annotation (Line(points={{194,566.25},{235.5,566.25},{235.5,447},{277,447}},color={0,0,127}));
  connect(dgtA.y,measurementBusA.T_dgt)
    annotation (Line(points={{194,550.25},{235.5,550.25},{235.5,447},{277,447}},color={0,0,127}));
  connect(sshA.y,measurementBusA.dT_ssh)
    annotation (Line(points={{196,524.25},{236.5,524.25},{236.5,447},{277,447}},color={0,0,127}));
  connect(eshA.y,measurementBusA.dT_esh)
    annotation (Line(points={{196,506.25},{236.5,506.25},{236.5,447},{277,447}},color={0,0,127}));
  connect(dshA.y,measurementBusA.dT_dsh)
    annotation (Line(points={{196,486.25},{236.5,486.25},{236.5,447},{277,447}},color={0,0,127}));
  connect(scA.y,measurementBusA.dT_sbc)
    annotation (Line(points={{196,468.25},{236.5,468.25},{236.5,447},{277,447}},color={0,0,127}));
  connect(lwt.y,measurementBusA.T_lwt)
    annotation (Line(points={{329.5,549.125},{335.5,549.125},{335.5,447},{277,447}},color={0,0,127}));
  connect(ewt.y,measurementBusA.T_ewt)
    annotation (Line(points={{329.5,565.125},{335.5,565.125},{335.5,447},{277,447}},color={0,0,127}));
  connect(oat.y,measurementBusA.T_oat)
    annotation (Line(points={{329,584},{335,584},{335,447},{277,447}},color={0,0,127}));
  connect(totalCapacity.y,measurementBusA.capacity)
    annotation (Line(points={{329,598},{335,598},{335,447},{277,447}},color={0,0,127}));
  connect(lwt.y,measurementBusB.T_lwt)
    annotation (Line(points={{329,548},{348,548},{348,449},{367,449}},color={0,0,127}));
  connect(ewt.y,measurementBusB.T_ewt)
    annotation (Line(points={{329.5,565.125},{348.25,565.125},{348.25,449},{367,449}},color={0,0,127}));
  connect(oat.y,measurementBusB.T_oat)
    annotation (Line(points={{329,584},{348,584},{348,449},{367,449}},color={0,0,127}));
  connect(totalCapacity.y,measurementBusB.capacity)
    annotation (Line(points={{329,598},{348,598},{348,449},{367,449}},color={0,0,127}));
  connect(scB.y,measurementBusB.dT_sbc)
    annotation (Line(points={{446,470.25},{406,470.25},{406,448},{366,448}},color={0,0,127}));
  connect(dshB.y,measurementBusB.dT_dsh)
    annotation (Line(points={{446,488.25},{406,488.25},{406,448},{366,448}},color={0,0,127}));
  connect(eshB.y,measurementBusB.dT_esh)
    annotation (Line(points={{446,508.25},{406,508.25},{406,448},{366,448}},color={0,0,127}));
  connect(sshB.y,measurementBusB.dT_ssh)
    annotation (Line(points={{446,526.25},{406,526.25},{406,448},{366,448}},color={0,0,127}));
  connect(dgtB.y,measurementBusB.T_dgt)
    annotation (Line(points={{444,552.25},{405,552.25},{405,448},{366,448}},color={0,0,127}));
  connect(sdtB.y,measurementBusB.T_sdt)
    annotation (Line(points={{444,568.25},{430,568.25},{430,448},{366,448}},color={0,0,127}));
  connect(sstB.y,measurementBusB.T_sst)
    annotation (Line(points={{444,586.25},{405,586.25},{405,448},{366,448}},color={0,0,127}));
  connect(rel_cooler_levelB.y,measurementBusB.rel_cooler_level)
    annotation (Line(points={{444,604.25},{405,604.25},{405,448},{366,448}},color={0,0,127}));
  connect(EXVECOA.openCommand,EXV_Eco_crkA)
    annotation (Line(points={{-275.9,54},{-292,54}},color={0,0,127}));
  connect(EXVMainA.openCommand,EXV_Main_crkA)
    annotation (Line(points={{-275.9,-4.000000000000001},{-292,-4.000000000000001},{-292,-4}},color={0,0,127}));
  connect(EXVMainB.openCommand,EXV_Main_crkB)
    annotation (Line(points={{243.9,-12},{266,-12}},color={0,0,127}));
  connect(CompressorA.N_speed,Speed_crkA)
    annotation (Line(points={{-85.5296104182744,125.83224494300265},{-85.5296104182744,120},{-72,120}},color={0,0,127}));
  connect(CompressorB.N_speed,Speed_crkB)
    annotation (Line(points={{47.11909060795743,125.18714238393311},{47.11909060795743,118},{32,118}},color={0,0,127}));
  connect(fanSignalProcessorA.SetPoint_ACmotor,motorA.frequency_in)
    annotation (Line(points={{-275.6672144684853,352.749589148636},{-255.13684769568636,352.749589148636},{-255.13684769568636,324.21121186492957}},color={0,0,127}));
  connect(fanSignalProcessorB.SetPoint_ACmotor,motorB.frequency_in)
    annotation (Line(points={{252.51657687830476,360.1125673412714},{232.0492639255063,360.1125673412714},{232.0492639255063,341.2609810755825}},color={0,0,127}));
  connect(fanSignalProcessorB.ControllerSignal,Fan_crkB)
    annotation (Line(points={{281.32207172919914,356.4352701262636},{292.16103586459957,356.4352701262636},{292.16103586459957,356},{302,356}},color={0,0,127}));
  connect(fanSignalProcessorA.SetPoint_fan,motorA.Scaling_motor_in)
    annotation (Line(points={{-275.6672144684853,348.51490251432483},{-250.99287161432008,348.51490251432483},{-250.99287161432008,324.21121186492957}},color={0,0,127}));
  connect(fanSignalProcessorB.SetPoint_fan,motorB.Scaling_motor_in)
    annotation (Line(points={{252.51657687830476,356.12882869167964},{227.37549777521232,356.12882869167964},{227.37549777521232,341.2609810755825}},color={0,0,127}));
  connect(actuatorSSTmaxB,calibrationBlock.SSTmaxB)
    annotation (Line(points={{-70,426.5},{-56.22726524297058,426.5},{-56.22726524297058,389.68252182877086},{-38.45453048594116,389.68252182877086}},color={0,0,127}));
  connect(actuatorSSTmaxA,calibrationBlock.SSTmaxA)
    annotation (Line(points={{-70,441.5},{-48,441.5},{-48,392.9815260257486},{-38.45453048594116,392.9815260257486}},color={0,0,127}));
  connect(rel_cooler_levelA.y,measurementBusA.rel_cooler_level)
    annotation (Line(points={{193,602},{214,602},{214,447},{277,447}},color={0,0,127}));
  connect(fanSignalProcessorA.SetPoint_fan,FanA.NStage)
    annotation (Line(points={{-275.6672144684853,348.51490251432483},{-212.84544134306984,348.51490251432483},{-212.84544134306984,309.28239574853114}},color={0,0,127}));
  connect(fanSignalProcessorB.SetPoint_fan,FanB.NStage)
    annotation (Line(points={{252.51657687830476,356.12882869167964},{188.97706240811655,356.12882869167964},{188.97706240811655,306.33677714215304}},color={0,0,127}));
  connect(Fan_crkA,fanSignalProcessorA.ControllerSignal)
    annotation (Line(points={{-336,346},{-306.28725628581213,346},{-306.28725628581213,348.8406476400411}},color={0,0,127}));
  connect(EXV_Eco_crkB,EXVECOB.openCommand)
    annotation (Line(points={{264,54},{243.9,54}},color={0,0,127}));
  annotation (
    Icon(
      graphics={
        Rectangle(
          extent={{-103,103},{103,-103}},
          fillColor={202,199,245},
          fillPattern=FillPattern.Solid,
          origin={-1,-3}),
        Text(
          extent={{-100,39},{100,-39}},
          fillColor={215,215,215},
          textString="Equipment",
          origin={-2,-22})}));
end Equipment_opti;
