within Workspace.System.BaseCycle.Optimization;
model Test_FanFunction
  import.Workspace.Controller.Components.Functions.fanSpeed;
  import BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMin;
  import BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax;
  parameter.Workspace.Controller.Components.Types.CompressorSelector CompType=.Workspace.Controller.Components.Types.CompressorSelector.NG3;
  parameter Real[15] NG1={0.8,-0.00116,-0.000007,5.82,-1.939,0.1685,0.5217,0.5842,-0.0815,-0.00014,0.000318,0.00003,-0.000008,-0.00851,-0.02069};
  parameter Real[15] NG2={1.038,-0.00457,0.000008,3.465,-0.84,0.05491,0.3949,0.679,-0.0675,-0.00137,0.00029,0.000049,0.000004,-0.00725,-0.02015};
  parameter Real[15] NG3={1.2315,-0.00584,-0.000009,5.396,-0.689,0.03293,0.3352,-0.0458,-0.12187,0.01344,0.000885,-0.000095,0.000032,-0.001057,-0.0068};
  parameter Real[15] fanCoefficients=
    if CompType ==.Workspace.Controller.Components.Types.CompressorSelector.NG3 then
      NG3
    elseif CompType ==.Workspace.Controller.Components.Types.CompressorSelector.NG2 then
      NG2
    else
      NG1;
  parameter Real Frq_Fan_A=50;
  parameter Real Frq_Fan_B=50;
  parameter Real Frq_Comp_A=95;
  parameter Real Frq_Comp_B=75;
  parameter Real LWT=7;
  parameter Real OAT=15;
  parameter Integer n_coilsA=10;
  parameter Integer n_coilsB=10;
  output Real Fan_speed_A;
  output Real Fan_speed_B;
equation
  Fan_speed_A=.BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax(
    0,
    .BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMin(
      60,
      .Workspace.Controller.Components.Functions.fanSpeed(
        Frq_Comp_A,
        n_coilsA,
        LWT+273.15,
        OAT+273.15,
        fanCoefficients),
      1e-4),
    1e-4);
  Fan_speed_B=.BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMax(
    0,
    .BOLT.InternalLibrary.BuildingBlocks.Utilities.Math.Smoothing.softMin(
      60,
      .Workspace.Controller.Components.Functions.fanSpeed(
        Frq_Comp_B,
        n_coilsB,
        LWT+273.15,
        OAT+273.15,
        fanCoefficients),
      1e-4),
    1e-4);
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end Test_FanFunction;
