within KAV_initiation.System30KAV.Controller30KAV.SubSystems.BaseClasses;
partial model ControllerBase
  extends.BOLT.InternalLibrary.BuildingBlocks.Icons.ControllerPackage;
  parameter Boolean crkA_isOff=false
    "Specify whether the circuit A is on or off";
  parameter Boolean crkB_isOff=false
    "Specify whether the circuit B is on or off";
  parameter Boolean ecoA_isOff=false
    "Specify whether the economizer EXV on circuit A is on or off";
  parameter Boolean ecoB_isOff=false
    "Specify whether the economizer EXV on circuit B is on or off";
  parameter Real crkA_min_speed=23
    "Minimum speed of compressor A (Hz)";
  parameter Real crkB_min_speed=27
    "Minimum speed of compressor B (Hz)";
  parameter Real crkA_max_speed=95
    "Maximum speed of compressor A (Hz)";
  parameter Real crkB_max_speed=105
    "Maximum speed of compressor B (Hz)";
  .Workspace.Interfaces.MeasurementBus measurementBus_crkA
    annotation (Placement(transformation(extent={{-120.0,50.0},{-80.0,90.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_crkA
    annotation (Placement(transformation(extent={{-110.53884282723261,19.461157172767386},{-89.46115717276739,40.538842827232614}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus_crkB
    annotation (Placement(transformation(extent={{-120.0,-50.0},{-80.0,-10.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus_crkB
    annotation (Placement(transformation(extent={{-109.79099766511365,-80.79099766511365},{-90.20900233488635,-61.20900233488635}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.CompleteCompressorControlBase completeCompressorControl_base(
    crkA_isOff=crkA_isOff,
    crkB_isOff=crkB_isOff,
    crkA_min_speed=crkA_min_speed,
    crkB_min_speed=crkB_min_speed,
    crkA_max_speed=crkA_max_speed,
    crkB_max_speed=crkB_max_speed)
    annotation (Placement(transformation(extent={{-10.000000149011612,-10.000000149011612},{10.000000149011612,10.000000149011612}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.FanControlBase fanControl_crkA(
    crkIsOff=crkA_isOff)
    annotation (Placement(transformation(extent={{-10.0,80.0},{10.0,100.0}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EXVControlBase eXVControl_crkA(
    crkIsOff=crkA_isOff)
    annotation (Placement(transformation(extent={{-10.0,50.0},{10.0,70.0}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EXVControlBase eXVControl_crkB(
    crkIsOff=crkB_isOff)
    annotation (Placement(transformation(extent={{-10.0,-68.0},{10.0,-48.0}},origin={0,-2},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EcoEXVControlBase ecoEXVControl_crkA(
    crkIsOff=ecoA_isOff)
    annotation (Placement(transformation(extent={{-10.0,20.0},{10.0,40.0}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.EcoEXVControlBase ecoEXVControl_crkB(
    crkIsOff=ecoB_isOff)
    annotation (Placement(transformation(extent={{-8.0,-98.0},{12.0,-78.0}},origin={-2,-2},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput fan_crkA
    annotation (Placement(transformation(extent={{98.0,78.0},{118.0,98.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput compressor_crkA
    annotation (Placement(transformation(extent={{98.0,-0.23999999999999488},{118.0,19.760000000000005}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput exv_crkA
    annotation (Placement(transformation(extent={{98.0,62.0},{118.0,82.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput fan_crkB
    annotation (Placement(transformation(extent={{98.0,-44.0},{118.0,-23.999999999999996}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput compressor_crkB
    annotation (Placement(transformation(extent={{98.0,-22.239999999999995},{118.0,-2.239999999999995}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput exv_crkB
    annotation (Placement(transformation(extent={{98.0,-64.0},{118.0,-44.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput ecoExv_crkA
    annotation (Placement(transformation(extent={{98.0,24.000000000000004},{118.0,44.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput ecoExv_crkB
    annotation (Placement(transformation(extent={{98.0,-100.0},{118.0,-80.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput sstmax_crkA
    annotation (Placement(transformation(extent={{98.0,42.0},{118.0,62.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput sstmax_crkB
    annotation (Placement(transformation(extent={{98.0,-82.0},{118.0,-62.0}},origin={0.0,0.0},rotation=0.0)));
  replaceable.Workspace.Controller.SubSystems.BaseClasses.FanControlBase fanControl_crkB(
    crkIsOff=crkB_isOff)
    annotation (Placement(transformation(extent={{-10.0,-38.0},{10.0,-18.0}},origin={0,-2},rotation=0.0)));
equation
  connect(fanControl_crkA.limitsBus,limitsBus_crkA)
    annotation (Line(points={{-10,84},{-32,84},{-32,30},{-100,30}},color={255,204,51}));
  connect(eXVControl_crkA.measurementBus,measurementBus_crkA)
    annotation (Line(points={{-10,66},{-56,66},{-56,70},{-100,70}},color={255,204,51}));
  connect(eXVControl_crkA.limitsBus,limitsBus_crkA)
    annotation (Line(points={{-10,54},{-32,54},{-32,30},{-100,30}},color={255,204,51}));
  connect(completeCompressorControl_base.limitsBus_crkA,limitsBus_crkA)
    annotation (Line(points={{-10.000000149011612,4.000000059604645},{-32,4.000000059604645},{-32,30},{-100,30}},color={255,204,51}));
  connect(completeCompressorControl_base.measurementBus_crkB,measurementBus_crkB)
    annotation (Line(points={{-10.000000149011612,-4.100000061094761},{-56,-4.100000061094761},{-56,-30},{-100,-30}},color={255,204,51}));
  connect(completeCompressorControl_base.limitsBus_crkB,limitsBus_crkB)
    annotation (Line(points={{-10.000000149011612,-8.100000120699406},{-32,-8.100000120699406},{-32,-71},{-100,-71}},color={255,204,51}));
  connect(eXVControl_crkB.measurementBus,measurementBus_crkB)
    annotation (Line(points={{-10,-54},{-56,-54},{-56,-30},{-100,-30}},color={255,204,51}));
  connect(eXVControl_crkB.limitsBus,limitsBus_crkB)
    annotation (Line(points={{-10,-66},{-32,-66},{-32,-71},{-100,-71}},color={255,204,51}));
  connect(ecoEXVControl_crkB.measurementBus,measurementBus_crkB)
    annotation (Line(points={{-10,-84},{-56,-84},{-56,-30},{-100,-30}},color={255,204,51}));
  connect(ecoEXVControl_crkB.limitsBus,limitsBus_crkB)
    annotation (Line(points={{-10,-96},{-32,-96},{-32,-71},{-100,-71}},color={255,204,51}));
  connect(ecoEXVControl_crkA.measurementBus,measurementBus_crkA)
    annotation (Line(points={{-10,36},{-56,36},{-56,70},{-100,70}},color={255,204,51}));
  connect(ecoEXVControl_crkA.limitsBus,limitsBus_crkA)
    annotation (Line(points={{-10,24},{-32,24},{-32,30},{-100,30}},color={255,204,51}));
  connect(completeCompressorControl_base.measurementBus_crkA,measurementBus_crkA)
    annotation (Line(points={{-10.400000154972076,8.00000011920929},{-56,8.00000011920929},{-56,70},{-100,70}},color={255,204,51}));
  connect(measurementBus_crkA,fanControl_crkA.measurementBus)
    annotation (Line(points={{-100,70},{-54,70},{-54,96},{-10,96}},color={255,204,51}));
  connect(ecoEXVControl_crkB.actuatorSignal,ecoExv_crkB)
    annotation (Line(points={{10,-90},{108,-90}},color={0,0,127}));
  connect(fanControl_crkA.actuatorSignal,fan_crkA)
    annotation (Line(points={{10,90},{58,90},{58,88},{108,88}},color={0,0,127}));
  connect(ecoEXVControl_crkA.actuatorSignal,ecoExv_crkA)
    annotation (Line(points={{10,30},{59,30},{59,34},{108,34}},color={0,0,127}));
  connect(completeCompressorControl_base.actuatorSignal_crkA,compressor_crkA)
    annotation (Line(points={{10.000000149011612,6.000000089406967},{59.000000074505806,6.000000089406967},{59.000000074505806,9.760000000000005},{108,9.760000000000005}},color={0,0,127}));
  connect(completeCompressorControl_base.actuatorSignal_crkB,compressor_crkB)
    annotation (Line(points={{10.000000149011612,-6.000000089406967},{59.000000074505806,-6.000000089406967},{59.000000074505806,-12.239999999999995},{108,-12.239999999999995}},color={0,0,127}));
  connect(eXVControl_crkA.actuatorSignal,exv_crkA)
    annotation (Line(points={{10,60},{59,60},{59,72},{108,72}},color={0,0,127}));
  connect(eXVControl_crkA.actuatorSignal_sst,sstmax_crkA)
    annotation (Line(points={{10,57.4},{59,57.4},{59,52},{108,52}},color={0,0,127}));
  connect(eXVControl_crkB.actuatorSignal,exv_crkB)
    annotation (Line(points={{10,-60},{59,-60},{59,-54},{108,-54}},color={0,0,127}));
  connect(eXVControl_crkB.actuatorSignal_sst,sstmax_crkB)
    annotation (Line(points={{10,-62.6},{59,-62.6},{59,-72},{108,-72}},color={0,0,127}));
  connect(fanControl_crkB.measurementBus,measurementBus_crkB)
    annotation (Line(points={{-10,-24},{-56,-24},{-56,-30},{-100,-30}},color={255,204,51}));
  connect(fanControl_crkB.limitsBus,limitsBus_crkB)
    annotation (Line(points={{-10,-36},{-32,-36},{-32,-71},{-100,-71}},color={255,204,51}));
  connect(fanControl_crkB.actuatorSignal,fan_crkB)
    annotation (Line(points={{10,-30},{59,-30},{59,-34},{108,-34}},color={0,0,127}));
end ControllerBase;
