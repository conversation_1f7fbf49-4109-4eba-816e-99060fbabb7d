within StateMachine;
record CrkABCDEcoCD_15
  import BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod;
  extends Mode30XBVBase(
    final modeID=ModeID30XBV.CrkABCDEcoCD_15,
    final CrkA=true,
    final CrkB=true,
    final CrkC=true,
    final CrkD=true,
    final EcoA=false,
    final EcoB=false,
    final EcoC=true,
    final EcoD=true,
    final ViA=false,
    final ViB=false,
    final ViC=false,
    final ViD=false,
    initMethod=ModeInitMethod.External);
end CrkABCDEcoCD_15;
