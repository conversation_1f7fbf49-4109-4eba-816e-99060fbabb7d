within KAV_initiation.System30KAV.Controller30KAV.Components.Functions;
function fanSpeed
  "Fan speed setpoint calculation"
  import SI=Modelica.SIunits;
  input SI.Frequency compressorFrequency=85
    "Compressor speed [Hz]";
  input Integer nbrCoils=8
    "Number of coils";
  input SI.Temperature T_lwt=273.15-2
    "Leaving water temperature";
  input SI.Temperature T_oat=273+35
    "Outside air temperature";
  input Real[15] coefficients={0.8,-0.00116,-0.000007,5.82,-1.939,0.1685,0.5217,0.5842,-0.0815,-0.00014,0.000318,0.00003,-0.000008,-0.00851,-0.02069}
    "Coefficients array";
  output Real fanSpeed;
protected
  SI.Temp_C T_lwt_degC=T_lwt-273.15
    "Leaving water temperature in centigrade";
  SI.Temp_C T_oat_degC=T_oat-273.15
    "Outside air temperature in centigrade";
  Real[15] variables
    "Variables array";
algorithm
  variables := {compressorFrequency,compressorFrequency^2,compressorFrequency^3,nbrCoils,nbrCoils^2,nbrCoils^3,T_lwt_degC,T_oat_degC,compressorFrequency*nbrCoils,compressorFrequency*T_oat_degC,compressorFrequency^2*nbrCoils,compressorFrequency^2*T_lwt_degC,compressorFrequency^2*T_oat_degC,T_lwt_degC*T_oat_degC,T_oat_degC*nbrCoils};
  fanSpeed := max(
    20,
    coefficients*variables)
    "min output for fan is 20Hz when no override on fans";
end fanSpeed;
