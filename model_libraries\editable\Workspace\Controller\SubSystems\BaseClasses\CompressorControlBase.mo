within KAV_initiation.System30KAV.Controller30KAV.SubSystems.BaseClasses;
partial model CompressorControlBase
  extends BOLT.InternalLibrary.BuildingBlocks.Icons.Compressor;
  parameter Boolean crkIsOff=false
    "Specify whether the controller is on or off";
  parameter Real compressorFrequency_min=23
    "Minimum speed of compressor (Hz)";
  parameter Real compressorFrequency_max=95
    "Maximum speed of compressor (Hz)";
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-120.66302965034427,39.33697034965573},{-79.33697034965573,80.66302965034427}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus
    annotation (Placement(transformation(extent={{-54.0,-56.0},{-34.0,-36.0}},origin={-56,-14},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput actuatorSignal
    annotation (Placement(transformation(extent={{134.0,-4.0},{154.0,16.0}},origin={-44,-6},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealInput compressorFrequency
    annotation (Placement(transformation(extent={{-58.68216823968166,-4.682168239681658},{-37.31783176031834,16.68216823968166}},origin={-52,-6},rotation=0.0)));
equation
  connect(actuatorSignal,measurementBus.compressorFrequency)
    annotation (Line(points={{100,0},{94,0},{94,78},{-100,78},{-100,60}},color={0,0,127}));
end CompressorControlBase;
