within Workspace.System.XF_MTL.Controller.Subsystems;
model <PERSON>umpUserControl
  extends BOLT.InternalLibrary.BuildingBlocks.Icons.Pump;
  parameter Boolean isEWTControl=true
    "When true EWT is controled variable";
  parameter Boolean UserPumpPresent=true
    "When true User Pump is present";
  parameter Real speedPump_max=2900.0
    annotation (Dialog(group="Pump System",tab="General"));
  parameter Real speedPump_min=300.0
    annotation (Dialog(group="Pump System",tab="General"));
  parameter Real ratioVd=1.0
    "Multiplier of Vd_SetPoint"
    annotation (Dialog(group="Pump System",tab="General"));
  parameter Real Ka_min = 0
    annotation (Dialog(group="External System",tab="General"));
  parameter Real Ka_max=1000
    annotation (Dialog(group="External System",tab="General"));
  parameter Real gain_extPressure=-10;
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation error_extPressure(
    isOff=not UserPumpPresent,
    gain=gain_extPressure,
    setPoint=extPressure_SetPoint,
    measurement=extPressure,
    ID=1)
    annotation (Placement(transformation(extent={{-7.147153054827982,0.0},{19.14715305482798,20.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController(
    AV_min=speedPump_min,
    AV_max=speedPump_max,
    AV_start=speedPump_max,
    isOff=not UserPumpPresent,
    manualOff=false,
    AV_value_off=0.0,
    smoothing=1e-8)
    annotation (Placement(transformation(extent={{46.0,0.0},{66.0,20.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Vd
    annotation (Placement(transformation(extent={{-60.166119673866675,91.83388032613333},{-31.833880326133325,120.16611967386667}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput Vd_SetPoint
    annotation (Placement(transformation(extent={{-64.25720905916774,-80.25720905916774},{-39.742790940832265,-55.742790940832265}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput EWT
    annotation (Placement(transformation(extent={{-60.166119673866675,67.83388032613334},{-31.833880326133325,96.16611967386666}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Logical.Switch setpoint0
    annotation (Placement(transformation(extent={{83.31976703037355,-40.580232969626444},{96.68023296962645,-27.219767030373553}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Logical.Switch measurement0
    annotation (Placement(transformation(extent={{83.31976703037355,-70.58023296962645},{96.68023296962645,-57.21976703037355}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EWT_setpoint_block(
    y=EWT_SetPoint)
    annotation (Placement(transformation(extent={{44.32805704550361,-29.671942954496387},{55.67194295449639,-18.328057045503613}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.BooleanExpression EWT_control_block(
    y=isEWTControl)
    annotation (Placement(transformation(extent={{45.073783211335375,-39.81742545828488},{56.50863412790513,-28.382574541715137}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Vflow_measure_block(
    y=Vd)
    annotation (Placement(transformation(extent={{44.32805704550361,-77.77194295449638},{55.67194295449639,-66.4280570455036}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Vflow_setpoint_block(
    y=Vd_SetPoint)
    annotation (Placement(transformation(extent={{44.32805704550361,-47.77194295449639},{55.67194295449639,-36.428057045503614}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression EWT_measure_block(
    y=EWT)
    annotation (Placement(transformation(extent={{44.32805704550361,-61.77194295449639},{55.67194295449639,-50.428057045503614}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation error_Vd(
    ID=1,
    measurement=measurement.y,
    setPoint=setpoint.y,
    gain=gain_EWT_or_Vflow_or_Pdispo.Output_parameter)
    annotation (Placement(transformation(extent={{-7.147153054827982,38.0},{19.14715305482798,58.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController_Ka(
    AV_value_off=0.00001,
    AV_start=0.1,
    AV_max=Ka_max,
    AV_min=Ka_min,
    isOff=not UserPumpPresent,
    smoothing=1e-8)
    annotation (Placement(transformation(extent={{48.0,38.0},{68.0,58.0}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput extPressure
    annotation (Placement(transformation(extent={{-62.166119673866675,113.83388032613334},{-33.833880326133325,142.16611967386666}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput extPressure_SetPoint
    annotation (Placement(transformation(extent={{-63.98713772791985,-59.773631632256986},{-39.47271960958438,-35.259213513921516}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Logical.Switch setpoint
    annotation (Placement(transformation(extent={{145.3144116915871,-51.757751480427004},{158.67487763084,-38.397285541174114}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Logical.Switch measurement
    annotation (Placement(transformation(extent={{145.3144116915871,-81.757751480427},{158.67487763084,-68.39728554117411}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.BooleanExpression UserPump_control_block(
    y=UserPumpPresent)
    annotation (Placement(transformation(extent={{106.65860611039399,-50.99494396908543},{118.09345702696375,-39.56009305251569}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Pdisp_setpoint_block(
    y=extPressure_SetPoint)
    annotation (Placement(transformation(extent={{106.73252346887212,-58.53963970314198},{118.07640937786489,-47.195753794149205}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.MeasurementBus measurementBus
    annotation (Placement(transformation(extent={{-146.0,84.0},{-106.0,124.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Interfaces.LimitsBus limitsBus
    annotation (Placement(transformation(extent={{-151.9971307364438,-99.9971307364438},{-92.00286926355619,-40.00286926355621}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput EWT_SetPoint
    annotation (Placement(transformation(extent={{-64.25720905916774,-102.25720905916774},{-39.742790940832265,-77.74279094083226}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression Pdisp_measure_block(
    y=0)
    annotation (Placement(transformation(extent={{108.32805704550361,-79.67194295449639},{119.67194295449639,-68.32805704550361}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput ActuatorSignal_Ka
    annotation (Placement(transformation(extent={{178,38},{198,58}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Interfaces.RealOutput ActuatorSignal
    annotation (Placement(transformation(extent={{178.0,0.0},{198.0,20.0}},origin={0.0,0.0},rotation=0.0)));
    .Workspace.Auxiliary.Switch_param gain_EWT_or_Vflow(Bool = isEWTControl,Gain_1 = 1,Gain_2 = -10) annotation(Placement(transformation(extent = {{83.99441833217455,-104.00558166782544},{96.00558166782545,-91.99441833217456}},origin = {0.0,0.0},rotation = 0.0)));
    .Workspace.Auxiliary.Switch_param gain_EWT_or_Vflow_or_Pdispo(Gain_2 = -0.01,Gain_1 = gain_EWT_or_Vflow.Output_parameter,Bool = UserPumpPresent) annotation(Placement(transformation(extent = {{143.737700091584,-106.26229990841603},{156.262299908416,-93.73770009158397}},origin = {0.0,0.0},rotation = 0.0)));
equation
  connect(Vd,measurementBus.Vflow_coolant_pumpUser)
    annotation (Line(points={{-46,106},{-70,106},{-70,104},{-126,104}},color={0,0,127}));
  connect(EWT,measurementBus.T_ewt)
    annotation (Line(points={{-46,82},{-70,82},{-70,104},{-126,104}},color={0,0,127}));
  connect(extPressure,measurementBus.External_Pressure)
    annotation (Line(points={{-48,128},{-70.43704469557406,128},{-70.43704469557406,104},{-126,104}},color={0,0,127}));
  connect(extPressure_SetPoint,limitsBus.extPressure_SetPoint)
    annotation (Line(points={{-51.729928668752116,-47.51642257308925},{-86.86496433437605,-47.51642257308925},{-86.86496433437605,-70},{-122,-70}},color={0,0,127}));
  connect(Vd_SetPoint,limitsBus.VflowPumpUserSetpoint)
    annotation (Line(points={{-52,-68},{-87,-68},{-87,-70},{-122,-70}},color={0,0,127}));
  connect(EWT_SetPoint,limitsBus.EWTSetpoint)
    annotation (Line(points={{-52,-90},{-87,-90},{-87,-70},{-122,-70}},color={0,0,127}));
  connect(error_Vd.sensor,setpointController_Ka.errorSignal)
    annotation (Line(points={{18.88420999373142,47.2},{32.958527569955,47.2},{32.958527569955,48},{48,48}},color={28,108,200}));
  connect(error_extPressure.sensor,setpointController.errorSignal)
    annotation (Line(points={{18.88420999373142,9.2},{32.05683764009813,9.2},{32.05683764009813,10},{46,10}},color={28,108,200}));
  connect(EWT_setpoint_block.y,setpoint0.u1)
    annotation (Line(points={{56.239137249946026,-24},{62.239137249946026,-24},{62.239137249946026,-22.555813624298843},{81.98372043644827,-22.555813624298843},{81.98372043644827,-28.555813624298843}},color={0,0,127}));
  connect(EWT_control_block.y,setpoint0.u2)
    annotation (Line(points={{57.080376673733625,-34.10000000000001},{69.53204855509094,-34.10000000000001},{69.53204855509094,-33.9},{81.98372043644827,-33.9}},color={255,0,255}));
  connect(Vflow_setpoint_block.y,setpoint0.u3)
    annotation (Line(points={{56.239137249946026,-42.1},{61.67194295449639,-42.1},{61.67194295449639,-39.244186375701155},{81.98372043644827,-39.244186375701155}},color={0,0,127}));
  connect(setpoint.u1,setpoint0.y)
    annotation (Line(points={{143.97836509766182,-39.7333321350994},{143.97836509766182,-33.7333321350994},{103.34825626658909,-33.7333321350994},{103.34825626658909,-33.9},{97.34825626658909,-33.9}},color={0,0,127}));
  connect(UserPump_control_block.y,setpoint.u2)
    annotation (Line(points={{118.66519957279223,-45.27751851080056},{131.32178233522703,-45.27751851080056},{131.32178233522703,-45.07751851080056},{143.97836509766182,-45.07751851080056}},color={255,0,255}));
  connect(Pdisp_setpoint_block.y,setpoint.u3)
    annotation (Line(points={{118.64360367331453,-52.86769674864559},{124.07640937786489,-52.86769674864559},{124.07640937786489,-56.421704886501715},{143.97836509766182,-56.421704886501715},{143.97836509766182,-50.421704886501715}},color={0,0,127}));
  connect(measurement0.u2,EWT_control_block.y)
    annotation (Line(points={{81.98372043644827,-63.900000000000006},{69.53204855509094,-63.900000000000006},{69.53204855509094,-34.10000000000001},{57.080376673733625,-34.10000000000001}},color={255,0,255}));
  connect(EWT_measure_block.y,measurement0.u1)
    annotation (Line(points={{56.239137249946026,-56.1},{62.239137249946026,-56.1},{62.239137249946026,-52.55581362429885},{81.98372043644827,-52.55581362429885},{81.98372043644827,-58.55581362429885}},color={0,0,127}));
  connect(measurement0.y,measurement.u1)
    annotation (Line(points={{97.34825626658909,-63.900000000000006},{103.34825626658909,-63.900000000000006},{103.34825626658909,-63.7333321350994},{143.97836509766182,-63.7333321350994},{143.97836509766182,-69.7333321350994}},color={0,0,127}));
  connect(measurement.u2,UserPump_control_block.y)
    annotation (Line(points={{143.97836509766182,-75.07751851080056},{130,-75.07751851080056},{130,-45.27751851080056},{118.66519957279223,-45.27751851080056}},color={255,0,255}));
  connect(Pdisp_measure_block.y,measurement.u3)
    annotation (Line(points={{120.23913724994603,-74},{125.67194295449639,-74},{125.67194295449639,-80.42170488650171},{143.97836509766182,-80.42170488650171}},color={0,0,127}));
  connect(setpointController_Ka.actuatorSignal,ActuatorSignal_Ka)
    annotation (Line(points={{68.6,48},{188,48}},color={0,0,127}));
  connect(setpointController.actuatorSignal,ActuatorSignal)
    annotation (Line(points={{66.6,10},{188,10}},color={0,0,127}));
  connect(Vflow_measure_block.y,measurement0.u3)
    annotation (Line(points={{56.239137249946026,-72.1},{61.67194295449639,-72.1},{61.67194295449639,-75.24418637570116},{81.98372043644827,-75.24418637570116},{81.98372043644827,-69.24418637570116}},color={0,0,127}));
end PumpUserControl;
