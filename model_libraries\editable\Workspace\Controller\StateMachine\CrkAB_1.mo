within KAV_initiation.System30KAV.Controller30KAV.StateMachine;
record CrkAB_1
  extends Mode30KAVBase(
    final modeID=ModeID30KAV.CrkAB_1,
    final CrkA=true,
    final CrkB=true,
    final EcoA=false,
    final EcoB=false,
    final ViA=false,
    final ViB=false,
    initMethod=BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod.External);
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false)),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)));
end CrkAB_1;
