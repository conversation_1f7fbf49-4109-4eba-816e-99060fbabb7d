within StateMachine;
record CrkABCDVIAB_11
  import BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod;
  extends Mode30XBVBase(
    final modeID=ModeID30XBV.CrkABCDVIAB_11,
    final CrkA=true,
    final CrkB=true,
    final CrkC=true,
    final CrkD=true,
    final EcoA=false,
    final EcoB=false,
    final EcoC=false,
    final EcoD=false,
    final ViA=true,
    final ViB=true,
    final ViC=false,
    final ViD=false,
    initMethod=ModeInitMethod.External);
end CrkABCDVIAB_11;
