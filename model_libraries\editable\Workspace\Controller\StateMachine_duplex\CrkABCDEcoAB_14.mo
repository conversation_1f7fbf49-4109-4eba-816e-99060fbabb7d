within StateMachine;
record CrkABCDEcoAB_14
  import BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod;
  extends Mode30XBVBase(
    final modeID=ModeID30XBV.CrkABCDEcoAB_14,
    final CrkA=true,
    final CrkB=true,
    final CrkC=true,
    final CrkD=true,
    final EcoA=true,
    final EcoB=true,
    final EcoC=false,
    final EcoD=false,
    final ViA=false,
    final ViB=false,
    final ViC=false,
    final ViD=false,
    initMethod=ModeInitMethod.External);
end CrkABCDEcoAB_14;
