within Workspace.Surrogate.HXComponents.Exemples;
model CondAirM_30XF_SurrGene_R1234ZE
  extends.Workspace.Surrogate.HXComponents.Exemples.CondAirM_30XBV_SurrGene_R134a(
    condAirM_30XBV(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    RefIn(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze),
    RefOut(
      RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R1234ze));
end CondAirM_30XF_SurrGene_R1234ZE;
