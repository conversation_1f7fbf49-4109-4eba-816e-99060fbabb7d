within Workspace.Auxiliary.Records;
record summary_system
  extends.Modelica.Icons.Record;
  .Modelica.SIunits.HeatFlowRate Cooling_capacity;
  output Real EER;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC OAT;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC EWT;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC LWT;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SST_A;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SST_B;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SST_C;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SST_D;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SDT_A;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SDT_B;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SDT_C;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SDT_D;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SET_A;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SET_B;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SET_C;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SET_D;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SCT_A;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SCT_B;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SCT_C;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC SCT_D;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC DGT_A;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC DGT_B;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC DGT_C;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC DGT_D;
  .Modelica.SIunits.Power Compressor_power_A;
  .Modelica.SIunits.Power Compressor_power_B;
  .Modelica.SIunits.Power Compressor_power_C;
  .Modelica.SIunits.Power Compressor_power_D;
  .Modelica.SIunits.Power Fan_power_A;
  .Modelica.SIunits.Power Fan_power_B;
  .Modelica.SIunits.Power Fan_power_C;
  .Modelica.SIunits.Power Fan_power_D;
  .Modelica.SIunits.Frequency Fan_frequency_A;
  .Modelica.SIunits.Frequency Fan_frequency_B;
  .Modelica.SIunits.Frequency Fan_frequency_C;
  .Modelica.SIunits.Frequency Fan_frequency_D;
  .Modelica.SIunits.Frequency Compressor_frequency_A;
  .Modelica.SIunits.Frequency Compressor_frequency_B;
  .Modelica.SIunits.Frequency Compressor_frequency_C;
  .Modelica.SIunits.Frequency Compressor_frequency_D;
  .Modelica.SIunits.MassFlowRate Vd_flow_rate;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC T_in_EXV_A;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC T_in_EXV_B;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC T_in_EXV_C;
  .Modelica.SIunits.Conversions.NonSIunits.Temperature_degC T_in_EXV_D;
  .Modelica.SIunits.Temperature Subcooling_A;
  .Modelica.SIunits.Temperature Subcooling_B;
  .Modelica.SIunits.Temperature Subcooling_C;
  .Modelica.SIunits.Temperature Subcooling_D;
  .Modelica.SIunits.Temperature Pinch_A;
  .Modelica.SIunits.Temperature Pinch_B;
  .Modelica.SIunits.Temperature Pinch_C;
  .Modelica.SIunits.Temperature Pinch_D;
  .Modelica.SIunits.PressureDifference dP_evap;
  .Modelica.SIunits.Frequency Pump_frequency_A;
  .Modelica.SIunits.Frequency Pump_frequency_B;
  .Modelica.SIunits.Power Pump_power_A;
  .Modelica.SIunits.Power Pump_power_B;
  output Real Z_U_cond_A;
  output Real Z_U_cond_B;
  output Real Z_U_cond_C;
  output Real Z_U_cond_D;
  output Real Z_cond_dpr_A;
  output Real Z_cond_dpr_B;
  output Real Z_cond_dpr_C;
  output Real Z_cond_dpr_D;
  output Real Z_U_eco_A;
  output Real Z_U_eco_B;
  output Real Z_U_eco_C;
  output Real Z_U_eco_D;
  output Real Z_U_evap_A;
  output Real Z_U_evap_B;
  output Real Z_U_evap_C;
  output Real Z_U_evap_D;
  output Real Z_dpc_evap;
  output Real Z_flow_comp_A;
  output Real Z_flow_comp_B;
  output Real Z_flow_comp_C;
  output Real Z_flow_comp_D;
  output Real Z_power_comp_A;
  output Real Z_power_comp_B;
  output Real Z_power_comp_C;
  output Real Z_power_comp_D;
  output Real Z_fan_A;
  output Real Z_fan_B;
  output Real Z_fan_C;
  output Real Z_fan_D;
end summary_system;
