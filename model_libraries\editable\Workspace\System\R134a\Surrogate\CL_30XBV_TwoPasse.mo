within Workspace.System.R134a.Surrogate;
model CL_30XBV_TwoPasse
  extends.Workspace.System.BaseCycle.XBV_2C.Surrogate.System_30XBV(
    ModuleA(
      n_passes_evap=2,
      c_load2_bf_pow_opt17A=0.16,
      c_load_bf_pow_opt17A=-0.16,
      c_const_bf_pow_opt17A=1,
      bf_pow_min_opt17A=1,
      bf_cap_max=1.022,
      c_load_bf_cap=0.22000000000000025,
      c_load2_bf_cap=0,
      c_const_bf_cap=0.8019999999999998,
      bf_pow_min=0.9259259259259259,
      c_load2_bf_pow=0.0365431045,
      c_load_bf_pow=0.0406952989,
      c_const_bf_pow=0.9228015063),
    choiceBlock(
      FAN_SPEED_selector=Workspace.Auxiliary.OptionBlock.FanSpeed.Selector.FAN_VS,
      HighAmbiant_selector=Workspace.Auxiliary.OptionBlock.HighAmbiantTemperature.HighAmbiant_selector.HighAmbiant,
      Selector_ModuleA=Workspace.Auxiliary.OptionBlock.RecordBase.R134A_recordBase.Selector.Unit_30XBV_0700,
      SixtyHz_selector=Workspace.Auxiliary.OptionBlock.SixtyHzOption.SixtyHz_selector.STANDARD,
      Coating_selector=Workspace.Auxiliary.OptionBlock.CoatingOption.Coating_selector.STANDARD),
    ECAT(
      TargetCoolingCapacity_W(
        setPoint=2000000),
      AmbientAirDBTemp_K(
        setPoint=35 + 273.15),
      EvapBrineEWT_K(
        setPoint=12 + 273.15),
      EvapBrineLWT_K(
        setPoint=7 + 273.15)),
    ECO_ON_slope=0.944,
    ECO_ON_inter=-0.625)
    annotation (Icon(graphics={Text(textString="CL",origin={2,-12},extent={{-100,75},{100,-75}})}));
  annotation (
    Icon(
      graphics={
        Text(
          textString="CL",
          origin={-2,-10},
          extent={{-96.01685393258424,70.66502830561285},{97,-70}})}));
end CL_30XBV_TwoPasse;
