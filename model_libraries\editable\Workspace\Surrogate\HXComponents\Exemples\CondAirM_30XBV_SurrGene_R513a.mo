within Workspace.Surrogate.HXComponents.Exemples;
model CondAirM_30XBV_SurrGene_R513a
  extends.Workspace.Surrogate.HXComponents.Exemples.CondAirM_30XBV_SurrGene_R134a(
    condAirM_30XBV(
      redeclare replaceable package RefMedium=.CustomRefrigerant.R513A,
      userDefined_geo(
        diameter_in_header=0.02395,
        length_tube_inlet=0.02286,
        length_outlet=0.02286,
        diameter_in_outlet=0.0157,
        diameter_in_inletTube=0.0157)),
    RefIn(
      redeclare replaceable package Medium=.CustomRefrigerant.R513A,
      m_flow_set=0.5),
    RefOut(
      redeclare replaceable package Medium=.CustomRefrigerant.R513A,
      x_set=-0.2),
    AirIn(
      Vds_flow_fixed=false,
      Vd_flow_fixed=true,
      Vd_flow_set=3),
    condAirM_30XBV(
      redeclare replaceable package RefMedium=.CustomRefrigerant.R513A));
end CondAirM_30XBV_SurrGene_R513a;
