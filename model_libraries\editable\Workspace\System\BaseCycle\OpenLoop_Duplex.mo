within Workspace.System.BaseCycle;
model OpenLoop_Duplex
  parameter Integer n_coilsB
    "Number of coils and fans for the circuit B";
  parameter Integer n_coilsA
    "Number of coils and fans for the circuit A";
  parameter Integer n_passes_evap annotation (Dialog(group="Evaporator",tab="Unit Characteristics"));
  parameter.Modelica.SIunits.Frequency NcompAMax annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.Modelica.SIunits.Frequency NcompAMin annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.Modelica.SIunits.Frequency NcompBMax annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.Modelica.SIunits.Frequency NcompBMin annotation (Dialog(group="Compressor",tab="Unit Characteristics"));
  parameter.Modelica.SIunits.PressureDifference pDisp=100000
    "Available static pressure for user if user pump is present"
    annotation (Dialog(group="Boundary conditions"));
  parameter Boolean isOffA
    annotation (Dialog(group="isOff"));
  parameter Boolean isOffB
    annotation (Dialog(group="isOff"));
  parameter Boolean isOffECOA
    annotation (Dialog(group="isOff"));
  parameter Boolean isOffECOB
    annotation (Dialog(group="isOff"));
  parameter Boolean Vi_A;
  parameter Boolean Vi_B;
  parameter Boolean is_fixedSpeed
    "if false then Variable speed, true then fixed speed";
  parameter.Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm max_fan_rpm annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter.Modelica.SIunits.Frequency max_motor_frequency annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter.Modelica.SIunits.Conversions.NonSIunits.AngularVelocity_rpm limit_max_fan_rpm annotation (Dialog(group="Fan",tab="Unit Characteristics"));
  parameter Boolean use_Calib;
  // Declaration of Coolant Medium Package
  package CoolantCommonMedium=.BOLT.InternalLibrary.Media.Coolant.CoolantCommon
    "Coolant Medium Package"
    annotation ();
  // Declaration of Coolant Medium at Evaporator Circuit
  parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMedium
    "Coolant Medium Selection"
    annotation (Dialog(group="Medium"));
  parameter Real BrineConcentration
    annotation (Dialog(group="Medium"));
  //parameter .BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector CoolantMediumHR=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Selector.FW
  //"Coolant Medium Selection" annotation (Dialog(group="Medium"));
  final parameter.BOLT.InternalLibrary.Media.Coolant.BaseClasses.Empty coolantInf=.BOLT.InternalLibrary.Media.Coolant.BaseClasses.getSelector(
    CoolantMedium)
    "Coolant Medium"
    annotation ();
  CoolantCommonMedium.Temperature FreezTemp=CoolantCommonMedium.freezeTemp(
    coolantInf,
    BrineConcentration,
    1);
  parameter Modelica.SIunits.Height altitude=0
    annotation (Dialog(group="Boundary conditions"));
  final parameter.Modelica.SIunits.VolumeFlowRate Vd_flow_min=0.2;
  final parameter.Modelica.SIunits.VolumeFlowRate Vd_flow_max=10;
  Integer coolerMinFlowRateFlag=
    if evaporator.selector_tube ==.Public_database.Evaporator.Tube.Selector.B4HSL_075_025_R134a and evaporator.summary.Re_a_coolant < 4000 then
      -1
    else
      1
    "If cooler side coolant Reynolds number less than 5000 for R-1234ze, flag = -1";
  replaceable model MCHX=.BOLT.Condenser.RefAir.CondAirM
    constrainedby.BOLT.InternalLibrary.BuildingBlocks.Configuration.Base
    annotation (choices(choice(redeclare model MCHX=.BOLT.Condenser.RefAir.CondAirM "Physical model"),choice(redeclare model MCHX=.BOLT.InternalLibrary.HeatExchangersSurrogate.RefAir.MCHX.CondAirM "Surrogate model"),choice(redeclare model MCHX=.BOLT.Condenser.RefAir.CondAirM_MVB "Surrogate model")));
   parameter .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a 
    "Refrigerant medium"
    annotation (choicesAllMatching=true);
  final parameter .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefBase refBase = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.GetRefSelector(RefMedium);
  package refBaseMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.GenericPureRefrigerant;
  inner.BOLT.GlobalParameters globalParameters(
    varLevel=BOLT.InternalLibrary.BuildingBlocks.Types.Var_level.Advanced,
    capacity_start_2=globalParameters.capacity_design/2,
    capacity_start=globalParameters.capacity_design/2,
    Tsat_econ_start=0.5*(globalParameters.Tsat_cond_start+globalParameters.Tsat_evap_start),
    Tsat_cond_start=max(
      300,
      globalParameters.T_ambient+15),
    T_ambient=sourceAirA.Tdb_set,
    p_ambient=altitude_to_Pressure.p,
    m_flow_reference=globalParameters.capacity_design/2/(globalParameters.h_b_start_ref-globalParameters.h_a_start_ref),
    capacity_design=900000,RefMedium = .BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-19.897823555000087,244.10217644499994},{7.8978235550000875,271.89782355500006}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeOilsepoutA(
    isOff=isOffA,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-4,-4},{4,4}},origin={-24.5,214.5},rotation=90)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeBPHEECOinA(
    isOff=isOffA,
    h_fixed=false,
    dTsh_set=-3,
    dTsh_fixed=false,
    x_set=-0.03,
    x_fixed=false,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-4,-4},{4,4}},origin={-256,162},rotation=-90)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeevapinA(
    isOff=isOffA,
    Tsat_set=278.15,
    x_set=0.5,
    x_fixed=false,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-4,-4},{4,4}},origin={-258,-42},rotation=-90)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeCpinA(
    isOff=isOffA,
    T_set=283.15,
    Tsat_set=278.15,
    dTsh_fixed=false,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-4,-4},{4,4}},origin={-22,138},rotation=90)));
  inner.BOLT.SystemVariable systemVariables(
    use_EN14511=false,
    pow_set=272000,
    pow_fixed=false,
    isOff={isOffA,isOffB},
    nStreams=2)
    annotation (Placement(transformation(extent={{-53.231156888333416,244.10217644499994},{-25.43550977833324,271.89782355500006}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.BoundaryNode.Air.Source sourceAirA(
    Vd_flow_set=5,
    pg_fixed=true,
    Vds_flow_fixed=false,
    isOff=isOffA,
    Vd_flow_bounds={Vd_flow_min,Vd_flow_max},
    Vds_flow_set=3)
    annotation (Placement(transformation(extent={{-9.731195983750155,-9.731195983750155},{9.731195983750155,9.731195983750155}},origin={-184.0,176.0},rotation=90.0)));
  .BOLT.BoundaryNode.Air.Sink sinkAirA(
    pg_set=0,
    pg_fixed=true,
    isOff=isOffA)
    annotation (Placement(transformation(extent={{-8.958158691073663,-8.958158691073663},{8.958158691073663,8.958158691073663}},origin={-194,349},rotation=-90)));
  .BOLT.InternalLibrary.HeatExchangers.RefRef.BPHX.RefRefBPHX_MVB BPHEECOA(
    isEcoOff=isOffECOA,
    Z_UA_expression=1.0,
    use_Z_UA_expression=true,
    nPlate=94,
    isOff=isOffA,
    RefMedium_H = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a,
    RefMedium_L = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-156,52.931714861508155},{-136,31.068285138491845}},origin={0,0},rotation=0)));
  .BOLT.RefMisc.Split split(
    isOff=isOffA,
    m_flow_b_min=0.0001,
    isOff_b=isOffECOA,
    x_start=-0.02,
    Tsat_start=globalParameters.Tsat_cond_start-0.5,
    Fraction_b_start=0.2,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{6.252495172317083,-6.252495172317097},{-6.252495172317083,6.252495172317097}},origin={-256,120},rotation=90)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeEXVECOoutA(
    isOff=isOffECOA or isOffA,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-206,40},{-198,48}},origin={0,0},rotation=0)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeBPHEECOoutA(
    isOff=isOffECOA or isOffA,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-118,42},{-110,50}},origin={0,0},rotation=0)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeEXVmaininA(
    isOff=isOffA,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-4,-4},{4,4}},origin={-228,34},rotation=-180)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeOilsepoutB(
    isOff=isOffB,
    streamID=2,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{4,-4.041694439710568},{-4,4.041694439710568}},origin={16,214},rotation=-90)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeBPHEECOinB(
    isOff=isOffB,
    streamID=2,
    x_fixed=false,
    x_set=-0.03,
    dTsh_fixed=false,
    dTsh_set=-3,
    h_fixed=false,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{4.041694439710568,-4},{-4.041694439710568,4}},origin={250,174},rotation=90)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeevapinB(
    isOff=isOffB,
    streamID=2,
    x_fixed=false,
    Tsat_set=278.15,
    x_set=0.5,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{4,-4.041694439710568},{-4,4.041694439710568}},origin={254,-44},rotation=90)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeCpinB(
    isOff=isOffB,
    streamID=2,
    dTsh_fixed=false,
    Tsat_set=278.15,
    T_set=283.15,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{4,-4.041694439710568},{-4,4.041694439710568}},origin={14,138},rotation=-90)));
  .BOLT.BoundaryNode.Air.Source sourceAirB(
    Vd_flow_bounds={Vd_flow_min,Vd_flow_max},
    Vd_flow_set=5,
    Vds_flow_fixed=false,
    Vds_flow_set=3,
    pg_fixed=true,
    pg_set=0,
    isOff=isOffB)
    annotation (Placement(transformation(extent={{8.865526292578352,-8.774068821702627},{-8.865526292578352,8.774068821702627}},origin={184.33333333333331,169},rotation=-90)));
  .BOLT.BoundaryNode.Air.Sink sinkAirB(
    pg_fixed=true,
    pg_set=0,
    isOff=isOffB)
    annotation (Placement(transformation(extent={{9.252518991052398,-9.157069272871581},{-9.252518991052398,9.157069272871581}},origin={198,350},rotation=90)));
  .BOLT.AirMisc.FanCurve FanB(
    use_nStage_in=false,
    k_pow=0,
    Emotor=0.74,
    selector_curve=.BOLT.InternalLibrary.Air.Fans.DataBase.Selector.FB6_proto_Dr,
    scalingFactor=1,
    use_scalingFactor_in=true,
    isOff=isOffB,
    streamID=2)
    annotation (Placement(transformation(extent={{15.889414962633566,-16.05504002618244},{-15.889414962633566,16.05504002618244}},origin={198,317},rotation=-90)));
  .BOLT.InternalLibrary.HeatExchangers.RefRef.BPHX.RefRefBPHX_MVB BPHEECOB(
    isEcoOff=isOffECOB,
    Z_UA_expression=1.0,
    use_Z_UA_expression=true,
    nPlate=94,
    streamID=2,
    isOff=isOffB)
    annotation (Placement(transformation(extent={{150.10423609927642,52.931714861508155},{129.89576390072358,31.068285138491845}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.RefMisc.Split split2(
    isOff_b=isOffECOB,
    streamID=2,
    isOff=isOffB,
    m_flow_b_min=0.0001,
    x_start=-0.02,
    Tsat_start=globalParameters.Tsat_cond_start-0.5,
    Fraction_b_start=0.2,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-6.5245857861579,-6.592595523332278},{6.5245857861579,6.592595523332278}},origin={250,126},rotation=-90)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeEXVECOoutB(
    isOff=isOffECOB or isOffB,
    streamID=2,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{202.04169443971057,42},{193.95830556028943,50}},origin={0,0},rotation=0)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeBPHEECOoutB(
    isOff=isOffECOB or isOffB,
    streamID=2,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{118.04169443971057,42},{109.95830556028943,50}},origin={0,0},rotation=0)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeEXVmaininB(
    isOff=isOffB,
    streamID=2,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{4,-4.041694439710568},{-4,4.041694439710568}},origin={222,36},rotation=180)));
  .BOLT.AirMisc.Duct ductcondAirA(
    length=1,
    Ac=2.68,
    Ka=14,
    T_start=globalParameters.T_ambient+10,
    dp_start(
      displayUnit="Pa")=20,
    isOff=isOffA)
    annotation (Placement(transformation(extent={{-5.112050507385732,-5.112050507385732},{5.112050507385732,5.112050507385732}},origin={-184,268},rotation=90)));
  .BOLT.AirMisc.Duct ductcondAirB(
    Ka=14,
    Ac=2.68,
    length=1,
    T_start=globalParameters.T_ambient+10,
    dp_start(
      displayUnit="Pa")=20,
    streamID=2,
    isOff=isOffB)
    annotation (Placement(transformation(extent={{-5.112050507385732,-5.112050507385732},{5.112050507385732,5.112050507385732}},origin={182.0,270.0},rotation=90.0)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeevapoutA(
    isOff=isOffA,
    Tsat_fixed=false,
    T_fixed=false,
    T_set=283.15,
    Tsat_set=5.28+273.15,
    dTsh_fixed=false,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-4,-4},{4,4}},origin={-25,-11.666666666666664},rotation=90)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeevapoutB(
    isOff=isOffB,
    Tsat_set=5.73+273.15,
    Tsat_fixed=false,
    T_set=283.15,
    dTsh_fixed=false,
    streamID=2,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{3.999999999999999,-4.041694439710568},{-3.999999999999999,4.041694439710568}},origin={14.666666666666664,-12},rotation=-90)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeCpoutA(
    isOff=isOffA,
    Tsat_set=54.98+273.15,
    Tsat_fixed=false,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-56,164},{-48,172}},origin={0,0},rotation=0)));
  .BOLT.BoundaryNode.Refrigerant.Node nodecondAirinA(
    isOff=isOffA,
    p_set=1447000,
    p_fixed=false,
    Tsat_fixed=false,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-4,-4},{4,4}},origin={-144,226},rotation=180)));
  .BOLT.BoundaryNode.Refrigerant.Node nodecondAiroutA(
    isOff=isOffA,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-4,-4},{4,4}},origin={-212,226},rotation=180)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeCpECOA(
    p_set=567000,
    p_fixed=false,
    isOff=isOffECOA or isOffA,
    dTsh_fixed=false,
    dTsh_set=10,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-4,-4},{4,4}},origin={-78,118},rotation=90)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeCpECOB(
    p_set=522000,
    p_fixed=false,
    isOff=isOffECOB or isOffB,
    streamID=2,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{4.041694439710568,-4},{-4.041694439710568,4}},origin={68,118},rotation=-90)));
  .BOLT.BoundaryNode.Refrigerant.Node nodecondAiroutB(
    isOff=isOffB,
    streamID=2,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{4.041694439710568,-4},{-4.041694439710568,4}},origin={220,226},rotation=-180)));
  .BOLT.BoundaryNode.Refrigerant.Node nodecondAirinB(
    isOff=isOffB,
    p_set=1414000,
    p_fixed=false,
    streamID=2,
    x_fixed=false,
    x_set=-0.03,
    dTsh_fixed=false,
    dTsh_set=-3,
    h_fixed=false,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{4.041694439710568,-4},{-4.041694439710568,4}},origin={136,226},rotation=-180)));
  .BOLT.BoundaryNode.Refrigerant.Node nodeCpoutB(
    isOff=isOffB,
    Tsat_set=53.79+273.15,
    Tsat_fixed=false,
    streamID=2,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-4,4},{4,-4}},origin={40,168},rotation=-180)));
  .BOLT.BoundaryNode.Air.Node nodeAircondAirinA(
    isOff=isOffA)
    annotation (Placement(transformation(extent={{-4,4},{4,-4}},origin={-184,198},rotation=90)));
  .BOLT.BoundaryNode.Air.Node nodeAirCondAiroutA(
    isOff=isOffA)
    annotation (Placement(transformation(extent={{-2.136690643431905,-2.136690643431905},{2.136690643431905,2.136690643431905}},origin={-184,254},rotation=90)));
  .BOLT.BoundaryNode.Air.Node nodeAirFaninA(
    isOff=isOffA)
    annotation (Placement(transformation(extent={{2.445105841252939,-2.445105841252939},{-2.445105841252939,2.445105841252939}},origin={-184,280},rotation=-90)));
  .BOLT.BoundaryNode.Air.Node nodeAircondAirinB(
    isOff=isOffB)
    annotation (Placement(transformation(extent={{-4,-4},{4,4}},origin={183.66666666666666,191},rotation=90)));
  .BOLT.BoundaryNode.Air.Node nodeAircondAiroutB(
    isOff=isOffB)
    annotation (Placement(transformation(extent={{-2.9228087610842977,-2.9228087610842977},{2.9228087610842977,2.9228087610842977}},origin={183,257},rotation=90)));
  .BOLT.BoundaryNode.Air.Node nodeAirFaninB(
    isOff=isOffB)
    annotation (Placement(transformation(extent={{-2.9228087610842977,-2.9228087610842977},{2.9228087610842977,2.9228087610842977}},origin={182,284},rotation=90)));
  .BOLT.RefMisc.ReducedPipe suctionlineA(
    U=20,
    m_flow_reference=3.3,
    dp_reference=16000,
    use_k_dp=false,
    Q_in=0.1,
    isFix_Tamb=false,
    isOff=isOffA,
    x_a_start=1.01,
    Tsat_a_start=globalParameters.Tsat_evap_start,
    length=3,
    Di=0.127,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-6,-6.599999999999994},{6,6.599999999999994}},origin={-22,90},rotation=90)));
  .BOLT.RefMisc.ReducedPipe suctionlineB(
    U=20,
    Di=0.127,
    length=3,
    use_k_dp=false,
    dp_reference=6000,
    m_flow_reference=2.2,
    Q_in=0.1,
    isFix_Tamb=false,
    streamID=2,
    isOff=isOffB,
    x_a_start=1.01,
    Tsat_a_start=globalParameters.Tsat_evap_start,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-6,-6.599999999999994},{6,6.599999999999994}},origin={14,90},rotation=90)));
  .BOLT.RefMisc.ReducedPipe oilseparatorA(
    U=20,
    Di=16*0.0254,
    length=1,
    m_flow_reference=3.1,
    dp_reference=15000,
    use_k_dp=false,
    Q_in=0.1,
    isFix_Tamb=false,
    isOff=isOffA,
    x_a_start=1.1,
    Tsat_a_start=globalParameters.Tsat_cond_start,
    Tsat_b_start=globalParameters.Tsat_cond_start*0.99,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-10.44644746281125,-11.491092209092358},{10.44644746281125,11.491092209092358}},origin={-22.0,194.0},rotation=90.0)));
  .BOLT.RefMisc.ReducedPipe oilseparatorB(
    U=20,
    use_k_dp=false,
    dp_reference=10000,
    m_flow_reference=2.2,
    Q_in=0.1,
    isFix_Tamb=false,
    streamID=2,
    isOff=isOffB,
    x_a_start=1.1,
    Tsat_a_start=globalParameters.Tsat_cond_start,
    length=1,
    Di=14*0.0254,
    Tsat_b_start=globalParameters.Tsat_cond_start*0.99,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-10.822145661270518,-11.904360227397547},{10.822145661270518,11.904360227397547}},origin={18.0,194.0},rotation=90.0)));
  .BOLT.RefMisc.ReducedPipe dischargelineA(
    U=20,
    m_flow_reference=4.829,
    dp_reference=34910,
    use_k_dp=false,
    Q_in=0.1,
    isFix_Tamb=false,
    isOff=isOffA,
    x_a_start=1.1,
    Tsat_a_start=globalParameters.Tsat_cond_start,
    length=3,
    Di=0.0413,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-6.0,-6.599999999999994},{6.0,6.599999999999994}},origin={-52.0,226.0},rotation=180.0)));
  .BOLT.RefMisc.ReducedPipe liquidlineA(
    U=20,
    length=7,
    m_flow_reference=0.5,
    dp_reference=0,
    use_k_dp=false,
    Q_in=0.1,
    isFix_Tamb=false,
    isOff=isOffA,
    x_a_start=-0.02,
    Tsat_a_start=globalParameters.Tsat_cond_start-0.5,
    Di=0.0286,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-6,-6.599999999999994},{6,6.599999999999994}},origin={-256,196},rotation=-90)));
  .BOLT.RefMisc.ReducedPipe ECOlineA(
    U=20,
    m_flow_reference=0.2,
    dp_reference=5000,
    use_k_dp=false,
    Q_in=0.1,
    isFix_Tamb=false,
    isOff=isOffECOA or isOffA,
    x_a_start=1.01,
    length=3,
    Di=0.0286,
    Tsat_a_start=globalParameters.Tsat_econ_start,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-98,37.4},{-86,50.6}},origin={0,0},rotation=0)));
  .BOLT.RefMisc.ReducedPipe dischargelineB(
    U=20,
    m_flow_reference=3.368,
    dp_reference=23560,
    use_k_dp=false,
    Q_in=0.1,
    isFix_Tamb=false,
    streamID=2,
    isOff=isOffB,
    x_a_start=1.1,
    Tsat_a_start=globalParameters.Tsat_cond_start,
    length=3,
    Di=0.0413,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{42,217.4},{54,230.6}},origin={0,0},rotation=0)));
  .BOLT.RefMisc.ReducedPipe liquidlineB(
    U=20,
    m_flow_reference=0.5,
    dp_reference=0,
    use_k_dp=false,
    Q_in=0.1,
    isFix_Tamb=false,
    streamID=2,
    isOff=isOffB,
    x_a_start=-0.02,
    Tsat_a_start=globalParameters.Tsat_cond_start-0.5,
    length=7,
    Di=0.0286,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-6,-6.599999999999994},{6,6.599999999999994}},origin={250,202},rotation=-90)));
  .BOLT.RefMisc.ReducedPipe ECOlineB(
    U=20,
    m_flow_reference=0.2,
    dp_reference=5000,
    use_k_dp=false,
    Q_in=0.1,
    isFix_Tamb=false,
    streamID=2,
    isOff=isOffECOB or isOffB,
    x_a_start=1.01,
    Tsat_a_start=globalParameters.Tsat_econ_start,
    length=3,
    Di=0.0286,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-6,-6.599999999999994},{6,6.599999999999994}},origin={96,46},rotation=-180)));
  .BOLT.Valve.Refrigerant.ValveEXV EXVECOA(
    selector_flowData=.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector.VPF_50_Forward,
    isOff=isOffECOA or isOffA,
    Tsat_a_start=globalParameters.Tsat_cond_start-0.5,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a,use_actuator_in = true)
    annotation (Placement(transformation(extent={{-6,6},{6,-6}},rotation=-90,origin={-258,68})));
  .BOLT.Valve.Refrigerant.ValveEXV EXVECOB(
    selector_flowData=.BOLT.InternalLibrary.Refrigerant.Valves.TwoPort.DataBase.Selector.VPF_25_Forward,
    isOff=isOffECOB or isOffB,
    Tsat_a_start=globalParameters.Tsat_cond_start-0.5,
    streamID=2,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a,use_actuator_in = true)
    annotation (Placement(transformation(extent={{-6.0,-6.0},{6.0,6.0}},rotation=-90.0,origin={253.04711431105233,67.04711431105228})));
  .BOLT.Mechanical.VFD VFDA(
    isOff=isOffA,
    NomMtrHz_vfd=95,
    selector_GenScrew=.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_200kW,
    VFDType=.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_StingrayType.VFD_Danfoss)
    annotation (Placement(transformation(extent={{2.4500816393268394,3.9201306229229544},{-2.4500816393268394,-3.9201306229229544}},origin={-108,180},rotation=90)));
  .BOLT.Mechanical.VFD VFDB(
    isOff=isOffB,
    streamID=2,
    NomMtrHz_vfd=100,
    VFDType=.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_StingrayType.VFD_Danfoss,
    selector_GenScrew=.BOLT.InternalLibrary.Mechanicals.VFD.DataBase.VFD_GenScrew.selector.Danfoss_FC102_132kW)
    annotation (Placement(transformation(extent={{-2.892184901008008,4.627495841612813},{2.892184901008008,-4.627495841612813}},origin={102,180},rotation=-90)));
  .BOLT.Mechanical.Motor motorA(
    shaftSpeed_fixed=false,
    is_fixed_speed_AC=is_fixedSpeed,
    use_scaling_motor_in=true,
    selector_AC=.BOLT.InternalLibrary.Mechanicals.Motor.DataBase.MotorCurveMap.AC_motor.Selector.LEROYSOMER_4860698,
    FanMotType=.BOLT.InternalLibrary.Mechanicals.Motor.Interfaces.FanMotType.AC_FanMot,
    k_pow=n_coilsA,
    Mtrfreq_bounds={0.1,100},
    MotorHz_fixed=true,
    use_frequency_in=true,
    use_shaftSpeed_in=false,
    speed_bounds={90,2000},
    isOff=isOffA)
    annotation (Placement(transformation(extent={{-7.67402978030799,-8.441432758338692},{7.67402978030799,8.441432758338692}},rotation=-90.0,origin={-234.0,330.0})));
  .BOLT.Mechanical.Motor motorB(
    shaftSpeed_fixed=false,
    is_fixed_speed_AC=is_fixedSpeed,
    use_scaling_motor_in=true,
    selector_AC=.BOLT.InternalLibrary.Mechanicals.Motor.DataBase.MotorCurveMap.AC_motor.Selector.LEROYSOMER_4860698,
    FanMotType=.BOLT.InternalLibrary.Mechanicals.Motor.Interfaces.FanMotType.AC_FanMot,
    k_pow=n_coilsB,
    Mtrfreq_bounds={0.1,100},
    MotorHz_fixed=true,
    use_frequency_in=true,
    use_shaftSpeed_in=false,
    speed_bounds={90,2000},
    streamID=2,
    isOff=isOffB)
    annotation (Placement(transformation(extent={{8.655122500544394,-9.520634750598845},{-8.655122500544394,9.520634750598845}},rotation=90,origin={238,346})));
  .BOLT.RefMisc.ReducedPipe valve_92A(
    Di=0.127,
    length=3,
    Tsat_a_start=globalParameters.Tsat_evap_start,
    x_a_start=1.01,
    isOff=isOffA,
    isFix_Tamb=false,
    Q_in=0.1,
    use_k_dp=false,
    dp_reference=6200,
    m_flow_reference=3.3,
    U=20,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-6,-6.600000000000001},{6,6.600000000000001}},origin={-23,58.333333333333336},rotation=90)));
  .BOLT.RefMisc.ReducedPipe valve_92B(
    streamID=2,
    U=20,
    m_flow_reference=3.3,
    dp_reference=6200,
    use_k_dp=false,
    Q_in=0.1,
    isFix_Tamb=false,
    isOff=isOffB,
    x_a_start=1.01,
    Tsat_a_start=globalParameters.Tsat_evap_start,
    length=3,
    Di=0.127,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{6,-6.599999999999998},{-6,6.599999999999998}},origin={14,58},rotation=-90)));
  .BOLT.InternalLibrary.HeatExchangers.RefCoolant.FLHX.Cooler_2C_Lumped evaporator(
    CoolantMedium=CoolantMedium,
    level_tb_fixed2=true,
    level_tb_fixed1=true,
    selector_tube=.Public_database.Evaporator.Tube.Selector.B4HSL_075_025_R134a,
    Z_dpc_expression=1.0,
    use_Z_dpc_expression=true,
    use_Z_Uev2_expression=true,
    use_Z_Uev1_expression=true,
    RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a,
    relLevel_topBundle2=0.97,
    relLevel_topBundle1=0.97,isOff_2 = isOffB,isOff_1 = isOffA)
    annotation (Placement(transformation(extent={{-32.53837905565507,-64.53837905565507},{8.53837905565507,-23.46162094434493}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Compressor.PD.Screw_3P_NG CompressorA(
    isEcoOff=isOffECOA,
    Z_power_expression=1.0,
    use_z_power_expression=true,
    Z_flow_suc_expression=1.0,
    use_z_flow_suc_expression=true,
    isLoopBreaker=true,
    isOff=isOffA,
    use_VFD=true,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-12.940779163451197,-11.0920964258153},{12.940779163451197,11.0920964258153}},origin={-78.0,152.0},rotation=180.0)));
  .BOLT.Compressor.PD.Screw_3P_NG CompressorB(
    isEcoOff=isOffECOB,
    Z_power_expression=1.0,
    use_z_power_expression=true,
    Z_flow_suc_expression=1.0,
    use_z_flow_suc_expression=true,
    streamID=2,
    isLoopBreaker=true,
    isOff=isOffB,
    use_VFD=true,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{13.761818784085136,-11.795844672072974},{-13.761818784085136,11.795844672072974}},origin={68,150},rotation=-180)));
  .BOLT.Valve.Refrigerant.ValveEXV EXVMainA(
    Tsat_a_start=globalParameters.Tsat_cond_start-0.5,
    isOff=isOffA,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a,use_actuator_in = true)
    annotation (Placement(transformation(extent={{-6,6},{6,-6}},origin={-258,10},rotation=-90)));
  .BOLT.Valve.Refrigerant.ValveEXV EXVMainB(
    Tsat_a_start=globalParameters.Tsat_cond_start-0.5,
    streamID=2,
    isOff=isOffB,RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a,use_actuator_in = true)
    annotation (Placement(transformation(extent={{6,6},{-6,-6}},origin={254,2},rotation=90)));
  .BOLT.BoundaryNode.Signal.BooleanConstant VIB(
    k=Vi_B)
    annotation (Placement(transformation(extent={{104.29290387655561,120.22695885499911},{95.70709612344439,127.77304114500089}},origin={0,0},rotation=0)));
  .BOLT.BoundaryNode.Signal.BooleanConstant VIA(
    k=Vi_A)
    annotation (Placement(transformation(extent={{-107.84845613517857,118.15154386482143},{-100.15154386482143,125.84845613517857}},origin={0,0},rotation=0)));
  .BOLT.AirMisc.FanCurve FanA(
    use_nStage_in=false,
    k_pow=0,
    Emotor=0.74,
    scalingFactor=1,
    use_scalingFactor_in=true,
    selector_curve=.BOLT.InternalLibrary.Air.Fans.DataBase.Selector.FB6_proto_Dr,
    isOff=isOffA)
    annotation (Placement(transformation(extent={{-15.630455945386416,-15.630455945386586},{15.630455945386416,15.630455945386586}},origin={-194,320},rotation=90)));
  MCHX condAirA(
    isOff=isOffA,
    Z_U_fixed=true,
    RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a)
    annotation (Placement(transformation(extent={{-10.0,10.0},{10.0,-10.0}},origin={-184.0,228.0},rotation=-180.0)));
  MCHX condAirB(
    isOff=isOffB,
    Z_U_fixed=true,
    RefMedium = BOLT.InternalLibrary.Media.Refrigerant.BaseClasses.RefSelector.R134a,
    streamID=2)
    annotation (Placement(transformation(extent={{172.0,216.0},{192.0,236.0}},origin={0.0,0.0},rotation=0.0)));
  .Workspace.Auxiliary.Altitude_to_Pressure altitude_to_Pressure(
    altitude=altitude)
    annotation (Placement(transformation(extent={{15.821055063394425,243.15438839672777},{45.51227826993892,272.84561160327223}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(nodeBPHEECOinA.port_b,split.port_a)
    annotation (Line(points={{-256,158},{-256,125.75229555853171}},color={255,0,0}));
  connect(split.port_c,BPHEECOA.Ref_H_in)
    annotation (Line(points={{-252.7487025103951,113.74750482768292},{-252.7487025103951,102},{-64,102},{-64,34.43188971126359},{-135.8,34.43188971126359}},color={255,0,0}));
  connect(split2.port_c,BPHEECOB.Ref_H_in)
    annotation (Line(points={{246.57185032786722,119.4754142138421},{246.57185032786722,104},{54,104},{54,35.44097108309511},{129.89576390072358,35.44097108309511}},color={255,0,0}));
  connect(nodeBPHEECOinB.port_b,split2.port_a)
    annotation (Line(points={{250,169.95830556028943},{250,132.0026189232653}},color={255,0,0}));
  connect(BPHEECOA.Ref_L_out,nodeBPHEECOoutA.port_a)
    annotation (Line(points={{-136,44.522703429578804},{-118,44.522703429578804},{-118,46}},color={255,0,0}));
  connect(nodeEXVECOoutA.port_b,BPHEECOA.Ref_L_in)
    annotation (Line(points={{-198,44},{-156,44},{-156,44.522703429578804}},color={255,0,0}));
  connect(nodeEXVECOoutB.port_b,BPHEECOB.Ref_L_in)
    annotation (Line(points={{193.95830556028943,46},{130,46},{130,48.340394619674726},{150.30632082126195,48.340394619674726}},color={255,0,0}));
  connect(BPHEECOA.Ref_H_out,nodeEXVmaininA.port_a)
    annotation (Line(points={{-156,34.43188971126359},{-187,34.43188971126359},{-187,34},{-224,34}},color={255,0,0}));
  connect(nodeAirCondAiroutA.port_b,ductcondAirA.port_a)
    annotation (Line(points={{-184,256.1366906434319},{-184,262.88794949261427}},color={0,0,255}));
  connect(nodeAircondAiroutB.port_b,ductcondAirB.port_a)
    annotation (Line(points={{183,259.9228087610843},{183,264.88794949261427},{182,264.88794949261427}},color={0,0,255}));
  connect(nodeAirFaninB.port_a,ductcondAirB.port_b)
    annotation (Line(points={{182,281.0771912389157},{182,275.11205050738573}},color={0,0,255}));
  connect(FanB.port_a,nodeAirFaninB.port_b)
    annotation (Line(points={{198,301.11058503736643},{198,286.9228087610843},{182,286.9228087610843}},color={0,0,255}));
  connect(FanB.port_b,sinkAirB.port)
    annotation (Line(points={{198,332.88941496263357},{198,340.7474810089476}},color={0,0,255}));
  connect(sourceAirA.port,nodeAircondAirinA.port_a)
    annotation (Line(points={{-184,185.73119598375015},{-184,194}},color={0,0,255}));
  connect(sourceAirB.port,nodeAircondAirinB.port_a)
    annotation (Line(points={{184.33333333333331,177.86552629257835},{183.66666666666666,177.86552629257835},{183.66666666666666,187}},color={0,0,255}));
  connect(suctionlineA.port_b,nodeCpinA.port_a)
    annotation (Line(points={{-22.6,96},{-22.6,134},{-22,134}},color={255,0,0}));
  connect(suctionlineB.port_b,nodeCpinB.port_a)
    annotation (Line(points={{13.400000000000002,96},{14,96},{14,134}},color={255,0,0}));
  connect(nodeCpoutA.port_b,oilseparatorA.port_a)
    annotation (Line(points={{-48,168},{-22.000000000000004,168},{-22.000000000000004,183.55355253718875}},color={255,0,0}));
  connect(oilseparatorA.port_b,nodeOilsepoutA.port_a)
    annotation (Line(points={{-21.999999999999996,204.44644746281125},{-24.5,204.44644746281125},{-24.5,210.5}},color={255,0,0}));
  connect(nodeCpoutB.port_b,oilseparatorB.port_a)
    annotation (Line(points={{36,168},{17.999999999999996,168},{17.999999999999996,183.17785433872947}},color={255,0,0}));
  connect(oilseparatorB.port_b,nodeOilsepoutB.port_a)
    annotation (Line(points={{18.000000000000004,204.82214566127053},{16,204.82214566127053},{16,210}},color={255,0,0}));
  connect(nodecondAiroutA.port_b,liquidlineA.port_a)
    annotation (Line(points={{-216,226},{-255.39999999999998,226},{-255.39999999999998,202}},color={255,0,0}));
  connect(ECOlineA.port_a,nodeBPHEECOoutA.port_b)
    annotation (Line(points={{-98,44.6},{-110,44.6},{-110,46}},color={255,0,0}));
  connect(nodecondAiroutB.port_b,liquidlineB.port_a)
    annotation (Line(points={{224.04169443971057,226},{250.6,226},{250.6,208}},color={255,0,0}));
  connect(liquidlineB.port_b,nodeBPHEECOinB.port_a)
    annotation (Line(points={{250.6,196},{250,196},{250,178.04169443971057}},color={255,0,0}));
  connect(nodeBPHEECOoutB.port_b,ECOlineB.port_a)
    annotation (Line(points={{109.95830556028943,46},{102,46},{102,45.400000000000006}},color={255,0,0}));
  connect(ECOlineB.port_b,nodeCpECOB.port_a)
    annotation (Line(points={{90,45.4},{68,45.4},{68,113.95830556028943}},color={255,0,0}));
  connect(BPHEECOB.Ref_H_out,nodeEXVmaininB.port_a)
    annotation (Line(points={{149.9021513772909,35.44097108309511},{218,35.44097108309511},{218,36}},color={255,0,0}));
  connect(split.port_b,EXVECOA.port_a)
    annotation (Line(points={{-259.2512974896049,113.74750482768292},{-259.2512974896049,74},{-258,74}},color={255,0,0}));
  connect(EXVECOA.port_b,nodeEXVECOoutA.port_a)
    annotation (Line(points={{-258,62},{-258,44},{-206,44}},color={255,0,0}));
  connect(split2.port_b,EXVECOB.port_a)
    annotation (Line(points={{253.42814967213278,119.4754142138421},{253.04711431105233,119.4754142138421},{253.04711431105233,73.04711431105228}},color={255,0,0}));
  connect(EXVECOB.port_b,nodeEXVECOoutB.port_a)
    annotation (Line(points={{253.04711431105233,61.04711431105228},{253.04711431105233,46},{202.04169443971057,46}},color={255,0,0}));
  connect(FanB.flange,motorB.ShaftOut)
    annotation (Line(points={{214.05504002618244,317},{222,317},{222,348.25033185014155},{228.47936524940116,348.25033185014155}},color={127,0,0}));
  connect(nodeevapoutB.port_b,valve_92B.port_a)
    annotation (Line(points={{14.666666666666664,-8},{14.666666666666664,-3},{14.600000000000001,-3},{14.600000000000001,52}},color={255,0,0}));
  connect(valve_92B.port_b,suctionlineB.port_a)
    annotation (Line(points={{14.599999999999998,64},{13.399999999999999,64},{13.399999999999999,84}},color={255,0,0}));
  connect(dischargelineA.port_a,nodeOilsepoutA.port_b)
    annotation (Line(points={{-46,226},{-24.5,226},{-24.5,218.5}},color={255,0,0}));
  connect(nodeOilsepoutB.port_b,dischargelineB.port_a)
    annotation (Line(points={{16,218},{16,224.6},{42,224.6}},color={255,0,0}));
  connect(evaporator.port_a_ref_1,nodeevapinA.port_b)
    annotation (Line(points={{-27.814551872854405,-57.658022072010624},{-27.814551872854405,-131.46881432194985},{-258,-131.46881432194985},{-258,-46}},color={255,0,0}));
  connect(evaporator.port_a_ref_2,nodeevapinB.port_b)
    annotation (Line(points={{4.225319453967504,-57.658022072010624},{4.225319453967504,-134},{254,-134},{254,-48}},color={255,0,0}));
  connect(EXVMainA.port_b,nodeevapinA.port_a)
    annotation (Line(points={{-258,4},{-258,-38}},color={255,0,0}));
  connect(EXVMainA.port_a,nodeEXVmaininA.port_b)
    annotation (Line(points={{-258,16},{-258,34},{-232,34}},color={255,0,0}));
  connect(EXVMainB.port_b,nodeevapinB.port_a)
    annotation (Line(points={{254,-4},{254,-40}},color={255,0,0}));
  connect(EXVMainB.port_a,nodeEXVmaininB.port_b)
    annotation (Line(points={{254,8},{254,36},{226,36}},color={255,0,0}));
  connect(CompressorA.electricalPort_b,VFDA.electricalPort_a)
    annotation (Line(points={{-78,163.0920964258153},{-78,174},{-107.95099836721346,174},{-107.95099836721346,177.54991836067316}},color={255,200,50}));
  connect(CompressorA.port_a,nodeCpinA.port_b)
    annotation (Line(points={{-65.0592208365488,152},{-22,152},{-22,142}},color={255,0,0}));
  connect(CompressorA.port_b,nodeCpoutA.port_a)
    annotation (Line(points={{-90.9407791634512,152},{-96.9407791634512,152},{-96.9407791634512,168},{-56,168}},color={255,0,0}));
  connect(CompressorB.port_b,nodeCpoutB.port_a)
    annotation (Line(points={{81.76181878408514,150},{87.76181878408514,150},{87.76181878408514,168},{44,168}},color={255,0,0}));
  connect(CompressorB.port_a,nodeCpinB.port_b)
    annotation (Line(points={{54.238181215914864,150},{14,150},{14,142}},color={255,0,0}));
  connect(CompressorB.port_c,nodeCpECOB.port_b)
    annotation (Line(points={{68,138.20415532792703},{68,122.04169443971057}},color={255,0,0}));
  connect(nodeevapoutA.port_a,evaporator.port_b_ref_1)
    annotation (Line(points={{-25,-15.666666666666664},{-25,-26.850453488528014},{-27.814551872854405,-26.850453488528014}},color={255,0,0}));
  connect(evaporator.port_b_ref_2,nodeevapoutB.port_a)
    annotation (Line(points={{4.636087035080607,-26.850453488528014},{14.666666666666668,-26.850453488528014},{14.666666666666668,-16}},color={255,0,0}));
  connect(nodeevapoutA.port_b,valve_92A.port_a)
    annotation (Line(points={{-25,-7.666666666666664},{-23.6,52.333333333333336}},color={255,0,0}));
  connect(valve_92A.port_b,suctionlineA.port_a)
    annotation (Line(points={{-23.6,64.33333333333334},{-22.6,64.33333333333334},{-22.6,84}},color={255,0,0}));
  connect(ductcondAirA.port_b,nodeAirFaninA.port_a)
    annotation (Line(points={{-184,273.11205050738573},{-184,277.55489415874706}},color={0,0,255}));
  connect(ECOlineA.port_b,nodeCpECOA.port_a)
    annotation (Line(points={{-86,44.6},{-78,44.6},{-78,114}},color={255,0,0}));
  connect(nodeCpECOA.port_b,CompressorA.port_c)
    annotation (Line(points={{-78,122},{-78,140.9079035741847}},color={255,0,0}));
  connect(BPHEECOB.Ref_L_out,nodeBPHEECOoutB.port_a)
    annotation (Line(points={{130.0978486227091,48.121760322444565},{130.0978486227091,46},{118.04169443971057,46}},color={255,0,0}));
  connect(VIA.y,CompressorA.VI)
    annotation (Line(points={{-99.76669825130358,122},{-81.69736547527177,122},{-81.69736547527177,141.83224494300265}},color={255,0,255}));
  connect(CompressorB.VI,VIB.y)
    annotation (Line(points={{71.93194822402432,139.18714238393312},{71.93194822402432,124},{95.27780573578883,124}},color={255,0,255}));
  connect(VFDB.electricalPort_a,CompressorB.electricalPort_b)
    annotation (Line(points={{101.94215630197984,177.10781509899198},{101.94215630197984,174},{68,174},{68,161.79584467207297}},color={255,200,50}));
  connect(nodeBPHEECOinA.port_a,liquidlineA.port_b)
    annotation (Line(points={{-256,166},{-256,190},{-255.39999999999998,190}},color={255,0,0}));
  connect(dischargelineA.port_b,nodecondAirinA.port_a)
    annotation (Line(points={{-58,226},{-140,226}},color={255,0,0}));
  connect(dischargelineB.port_b,nodecondAirinB.port_a)
    annotation (Line(points={{54,224.6},{131.95830556028943,224.6},{131.95830556028943,226}},color={255,0,0}));
  connect(nodeAirFaninA.port_b,FanA.port_a)
    annotation (Line(points={{-184,282.44510584125294},{-194,282.44510584125294},{-194,304.3695440546136}},color={0,0,255}));
  connect(FanA.port_b,sinkAirA.port)
    annotation (Line(points={{-194,335.6304559453864},{-194,340.04184130892634}},color={0,0,255}));
  connect(motorA.ShaftOut,FanA.flange)
    annotation (Line(points={{-225.5585672416613,331.99524774288005},{-215.50528920710377,331.99524774288005},{-215.50528920710377,320},{-209.6304559453866,320}},color={127,0,0}));
  connect(nodeAircondAirinA.port_b,condAirA.port_a_air)
    annotation (Line(points={{-184,202},{-184,219.4}},color={0,0,255}));
  connect(nodecondAirinA.port_b,condAirA.port_a_ref)
    annotation (Line(points={{-148,226},{-170.6,226},{-170.6,228}},color={255,0,0}));
  connect(condAirA.port_b_ref,nodecondAiroutA.port_a)
    annotation (Line(points={{-194,228},{-190,228},{-190,226},{-208,226}},color={255,0,0}));
  connect(condAirA.port_b_air,nodeAirCondAiroutA.port_a)
    annotation (Line(points={{-184,236.8},{-184,251.8633093565681}},color={0,0,255}));
  connect(nodecondAirinB.port_b,condAirB.port_a_ref)
    annotation (Line(points={{140.04169443971057,226},{168.6,226}},color={255,0,0}));
  connect(nodeAircondAirinB.port_b,condAirB.port_a_air)
    annotation (Line(points={{183.66666666666666,195},{183.66666666666666,224},{182,224},{182,217.4}},color={0,0,255}));
  connect(condAirB.port_b_air,nodeAircondAiroutB.port_a)
    annotation (Line(points={{182,234.8},{183,254.0771912389157}},color={0,0,255}));
  connect(condAirB.port_b_ref,nodecondAiroutB.port_a)
    annotation (Line(points={{192,226},{215.95830556028943,226}},color={255,0,0}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          lineColor={0,0,0},
          fillColor={230,230,230},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          extent={{-66,64},{76,-60}},
          lineColor={28,108,200},
          textString="OL")}));
end OpenLoop_Duplex;
