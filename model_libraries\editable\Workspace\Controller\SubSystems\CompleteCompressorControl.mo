within KAV_initiation.System30KAV.Controller30KAV.SubSystems;
model CompleteCompressorControl
  extends.Workspace.Controller.SubSystems.BaseClasses.CompleteCompressorControlBase(
    redeclare replaceable.Workspace.Controller.SubSystems.CompressorControl compressorControl_crkA(
      isOffSSTmin=false,
      isOffSDTmax=false,
      isOffDGTmax=false),
    redeclare replaceable.Workspace.Controller.SubSystems.CompressorControl compressorControl_crkB,
    crkA_isOff=false,
    crkB_isOff=false,
    capacity_controller(
      AV_start=0.6),capacity_error(gain = 1 / (((max(gain_capacity,10000))))));
end CompleteCompressorControl;
