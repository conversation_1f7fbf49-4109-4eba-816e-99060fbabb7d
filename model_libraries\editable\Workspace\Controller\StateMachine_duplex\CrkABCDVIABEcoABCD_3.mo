within StateMachine;
record CrkABCDVIABEcoABCD_3
  import BOLT.Control.SteadyState.StateMachine.BaseClasses.ModeInitMethod;
  extends Mode30XBVBase(
    final modeID=ModeID30XBV.CrkABCDVIABEcoABCD_3,
    final CrkA=true,
    final CrkB=true,
    final CrkC=true,
    final CrkD=true,
    final EcoA=true,
    final EcoB=true,
    final EcoC=true,
    final EcoD=true,
    final ViA=true,
    final ViB=true,
    final ViC=false,
    final ViD=false,
    initMethod=ModeInitMethod.External);
end CrkABCDVIABEcoABCD_3;
