within Workspace.Controller;
model Controller_opti
  extends.Workspace.Controller.SubSystems.BaseClasses.ControllerBase_opti(
    redeclare.Workspace.Controller.SubSystems.FanControl fanControl_crkA(
      isOffSDTmin=isOffSDTmin_fan,
      isOffSDTmax=isOffSDTmax_fan,
      isOffDGTmax=isOffDGTmax_fan,
      MaxFrequency=MaxFrequency),
    redeclare.Workspace.Controller.SubSystems.EXVControl eXVControl_crkA(
      isOffSSTmin=isOffSSTmin_EXV,
      isOffSSTmax=isOffSSTmax_EXV,
      isOffDSHmin=isOffDSHmin_EXV,
      isOffDGTmax=isOffDGTmax_EXV),
    redeclare.Workspace.Controller.SubSystems.CompleteCompressorControl completeCompressorControl_base(
      compressorControl_crkA(
        isOffSSTmin=isOffSSTmin_comp,
        isOffSDTmax=isOffSDTmax_comp,
        isOffDGTmax=isOffDGTmax_comp),
      compressorControl_crkB(
        isOffSSTmin=isOffSSTmin_comp,
        isOffSDTmax=isOffSDTmax_comp,
        isOffDGTmax=isOffDGTmax_comp)),
    redeclare.Workspace.Controller.SubSystems.FanControl fanControl_crkB(
      isOffSDTmin=isOffSDTmin_fan,
      isOffSDTmax=isOffSDTmax_fan,
      isOffDGTmax=isOffDGTmax_fan,
      MaxFrequency=MaxFrequency),
    redeclare.Workspace.Controller.SubSystems.EXVControl eXVControl_crkB(
      isOffSSTmin=isOffSSTmin_EXV,
      isOffSSTmax=isOffSSTmax_EXV,
      isOffDSHmin=isOffDSHmin_EXV,
      isOffDGTmax=isOffDGTmax_EXV),
    redeclare.Workspace.Controller.SubSystems.EcoEXVControl ecoEXVControl_crkA(
      setpointController(
        AV_min=0.05)),
    redeclare.Workspace.Controller.SubSystems.EcoEXVControl ecoEXVControl_crkB(
      setpointController(
        AV_min=0.05)),
    fanCtrlOffA(
      y=useFanActuatorA));
  parameter Real MaxFrequency;
  parameter Real AV_min_setpoint;
  parameter Boolean isOffSDTmin_fan
    annotation (Dialog(group="fanControl crkA",tab="isOff controller"));
  parameter Boolean isOffSDTmax_fan
    annotation (Dialog(group="fanControl ",tab="isOff controller"));
  parameter Boolean isOffDGTmax_fan
    annotation (Dialog(group="fanControl ",tab="isOff controller"));
  parameter Boolean isOffSSTmin_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffSSTmax_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffDSHmin_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffDGTmax_EXV
    annotation (Dialog(group="eXVControl ",tab="isOff controller"));
  parameter Boolean isOffSSTmin_comp
    annotation (Dialog(group="Compressor ",tab="isOff controller"));
  parameter Boolean isOffSDTmax_comp
    annotation (Dialog(group="Compressor ",tab="isOff controller"));
  parameter Boolean isOffDGTmax_comp
    annotation (Dialog(group="Compressor ",tab="isOff controller"));
end Controller_opti;
