within Workspace.Auxiliary.FanSignal.Block_EN14511;
model EN14511
  parameter Boolean integrated_pump=false;
  parameter Boolean glandless_circulator=false;
  parameter Boolean dry_motor_pump=false;
  parameter Real ie=0.88;
  //motor efficiency level defined in EC no 640/2009
  parameter Boolean heating_mode=false;
  parameter Real dp_bound=10
    "pressure drop bound to avoid divided by 0";
  parameter Integer nFanA=1
    "number of fans in circuit A";
  parameter Integer nFanB=1
    "number of fans in circuit B";
  parameter Boolean isOffA=false
    "Set true to turn off circuit A";
  parameter Boolean isOffB=false
    "Set true to turn off circuit B";
  final parameter Real scalingA=
    if isOffA then
      0
    else
      1;
  final parameter Real scalingB=
    if isOffB then
      0
    else
      1;
  final constant Real eta_fan=0.3
    "fan efficiency value from EN14511-3:2017 page11 ";
  .Modelica.Blocks.Interfaces.RealInput fanScalingA
    annotation (Placement(transformation(extent={{-111.06059589391957,62.42477931245692},{-88.93940410608043,84.54597110029606}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealInput fanScalingB
    annotation (Placement(transformation(extent={{-111.06059589391957,46.62392803542896},{-88.93940410608043,68.74511982326808}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealInput DPe(
    unit="Pa")
    annotation (Placement(transformation(extent={{-111.06059589391957,32.40316188610382},{-88.93940410608043,54.52435367394294}},rotation=0.0,origin={0.0,0.0})));
  //[external static pressure difference Pa][water flow]
  .Modelica.Blocks.Interfaces.RealInput DPi(
    unit="Pa")
    annotation (Placement(transformation(extent={{-111.06059589391957,-13.419306817277242},{-88.93940410608043,8.701884970561888}},rotation=0.0,origin={0.0,0.0})));
  //[internal static pressure difference Pa] negative
  .Modelica.Blocks.Interfaces.RealInput DPe_air_A(
    unit="Pa")
    annotation (Placement(transformation(extent={{-111.06059589391957,16.60231060907586},{-88.93940410608043,38.723502396914995}},rotation=0.0,origin={0.0,0.0})));
  //[circuit A external static pressure difference Pa][air flow]
  .Modelica.Blocks.Interfaces.RealInput q_air_A(
    unit="m3/s")
    annotation (Placement(transformation(extent={{-111.06059589391957,-43.44092424363035},{-88.93940410608043,-21.319732455791215}},rotation=0.0,origin={0.0,0.0})));
  //[circuit A air volume flow rate m3/s]
  .Modelica.Blocks.Interfaces.RealInput DPe_air_B(
    unit="Pa")
    annotation (Placement(transformation(extent={{-111.06059589391957,0.8014593320479158},{-88.93940410608043,22.922651119887046}},rotation=0.0,origin={0.0,0.0})));
  //[circuit B external static pressure difference Pa][air flow]
  .Modelica.Blocks.Interfaces.RealInput q_air_B(
    unit="m3/s")
    annotation (Placement(transformation(extent={{-111.06059589391957,-59.241775520658294},{-88.93940410608043,-37.120583732819156}},rotation=0.0,origin={0.0,0.0})));
  //[circuit B air volume flow rate m3/s]
  .Modelica.Blocks.Interfaces.RealInput q(
    unit="m3/s")
    annotation (Placement(transformation(extent={{-111.06059589391957,-27.640072966602386},{-88.93940410608043,-5.518881178763255}},rotation=0.0,origin={0.0,0.0})));
  //[liquid volume flow rate m3/s]
  .Modelica.Blocks.Interfaces.RealInput inst_gross_cap(
    unit="W")
    annotation (Placement(transformation(extent={{-111.06059589391957,-75.04262679768624},{-88.93940410608043,-52.921435009847116}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealInput inst_gross_pow(
    unit="W")
    annotation (Placement(transformation(extent={{-111.06059589391957,-90.84347807471421},{-88.93940410608043,-68.72228628687508}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealInput inst_gross_heat(
    unit="W")
    annotation (Placement(transformation(extent={{-110.40812351283472,-105.9918569706573},{-89.59187648716528,-85.17560994498785}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealInput DPi_cond(
    unit="Pa")
    annotation (Placement(transformation(extent={{-111.06059589391957,-124.0252657564729},{-88.93940410608043,-101.90407396863377}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealInput q_cond(
    unit="m3/s")
    annotation (Placement(transformation(extent={{-111.06059589391957,-141.40620216120362},{-88.93940410608043,-119.2850103733645}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealOutput inst_net_heat(
    unit="W")
    annotation (Placement(transformation(extent={{94.3517832140453,-17.606088872270245},{110.5602709824434,-1.3976011038721445}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealOutput inst_net_cap(
    unit="W")
    annotation (Placement(transformation(extent={{93.6677425666788,-3.353321390163451},{109.8762303350769,12.85516637823465}},rotation=0.0,origin={0.0,0.0})));
  .Modelica.Blocks.Interfaces.RealOutput inst_net_pow(
    unit="W")
    annotation (Placement(transformation(extent={{93.6677425666788,-33.35332139016345},{109.8762303350769,-17.14483362176535}},rotation=0.0,origin={0.0,0.0})));
  Real P_hyd(
    unit="W");
  Real P_hyd_pump(
    unit="W");
  Real eta;
  Real eta_glandless;
  Real eta_dry;
  Real eta_pump;
  Real C(
    unit="W");
  Real M(
    unit="W");
  Real C_pump(
    unit="W");
  Real M_pump(
    unit="W");
  Real M_fan(
    unit="W");
  Real C_glandless(
    unit="W");
  Real C_dry(
    unit="W");
  Real inst_net_cap_wopump(
    unit="W");
  Real inst_net_pow_wopump(
    unit="W");
  Real inst_net_cap_pump(
    unit="W");
  Real inst_net_pow_pump(
    unit="W");
  // Variables for net heating cap in HR mode
  parameter Boolean is_HR=true;
  Real P_hyd_HR(
    unit="W");
  Real eta_HR;
  Real C_HR(
    unit="W");
  Real M_HR(
    unit="W");
  Real inst_net_heat_wopump(
    unit="W");
  Real inst_net_pow_wopump_HR(
    unit="W");
equation
  P_hyd_pump=q*max(
    dp_bound,
    DPe);
  ///[W] pag 49
  P_hyd=q*max(
    dp_bound,
    (-DPi));
  //#[W]
  ////////    ### Determination Efficiency  Annex G pag 49
  eta_glandless=(0.35844*P_hyd_pump*0.49/(1.7*P_hyd_pump+17*(1-exp(
    -0.3*P_hyd_pump))))/0.23;
  //pag49
  if P_hyd_pump > 500 then
    eta_dry=0.092*log(
      P_hyd_pump)-0.0403;
  //            #pag50
  else
    eta_dry=0.0721*(P_hyd_pump^0.3183);
  //            #pag50
  end if;
  if P_hyd <= 300 then
    eta=(0.35844*P_hyd*0.49/(1.7*P_hyd+17*(1-exp(
      -0.3*P_hyd))))/0.23;
  elseif P_hyd > 500 then
    eta=0.092*log(
      P_hyd)-0.0403;
  else
    eta=0.0721*(P_hyd^0.3183);
  end if;
  ////Capacity correction
  //#pag 10
  C_glandless=P_hyd_pump/eta_glandless*(ie-eta_glandless);
  //#[4]
  C_dry=P_hyd_pump/eta_dry*(ie-eta_dry);
  //#[5]
  C_pump=
    if glandless_circulator then
      C_glandless
    else
      C_dry;
  // Capacity correction with pump
  //#pag 11
  if P_hyd > 300 then
    C=P_hyd/eta*(ie-eta);
  //#[7] Capacity correction without pump
  else
    C=P_hyd/eta*(1-eta);
  //  #[6] Capacity correction without pump
  end if;
  /////Power input correction    pag 12 4.1.5.3
  eta_pump=
    if glandless_circulator then
      eta_glandless
    else
      eta_dry;
  M_pump=P_hyd_pump/eta_pump;
  //#[10] power correction with pump
  M=P_hyd/eta;
  //#[11] power correction without pump
  M_fan=fanScalingA*scalingA*nFanA*DPe_air_A*q_air_A/eta_fan+fanScalingB*scalingB*nFanB*DPe_air_B*q_air_B/eta_fan;
  ////Estimation inst_net_cap and inst_net_pow
  // pag 10   4.1.4.2.1
  inst_net_cap_pump=
    if heating_mode then
      inst_gross_cap-C_pump
    else
      inst_gross_cap+C_pump;
  inst_net_pow_pump=inst_gross_pow-M_pump-M_fan;
  // pag 10   4.1.4.2.2
  inst_net_cap_wopump=
    if heating_mode then
      inst_gross_cap+C
    else
      inst_gross_cap-C;
  inst_net_pow_wopump=inst_gross_pow+M-M_fan;
  ////Estimation inst_net_cap and inst_net_pow and inst_net_heat in HR Mode
  P_hyd_HR=q_cond*max(
    dp_bound,
    (-DPi_cond));
  //#[W]
  if P_hyd_HR <= 300 then
    eta_HR=(0.35844*P_hyd_HR*0.49/(1.7*P_hyd+17*(1-exp(
      -0.3*P_hyd_HR))))/0.23;
  elseif P_hyd_HR > 500 then
    eta_HR=0.092*log(
      P_hyd_HR)-0.0403;
  else
    eta_HR=0.0721*(P_hyd_HR^0.3183);
  end if;
  if P_hyd_HR > 300 then
    C_HR=P_hyd_HR/eta_HR*(ie-eta_HR);
  //#[7] Capacity correction without pump
  else
    C_HR=P_hyd_HR/eta_HR*(1-eta_HR);
  //  #[6] Capacity correction without pump
  end if;
  M_HR=P_hyd_HR/eta_HR;
  //#[11] power correction without pump
  inst_net_heat_wopump=
    if is_HR then
      inst_gross_heat+C_HR
    else
      0;
  inst_net_pow_wopump_HR=
    if is_HR then
      M_HR
    else
      0;
  inst_net_cap=
    if integrated_pump then
      inst_net_cap_pump
    else
      inst_net_cap_wopump;
  inst_net_heat=
    if is_HR then
      inst_net_heat_wopump
    else
      0;
  inst_net_pow=
    if integrated_pump then
      inst_net_pow_pump+inst_net_pow_wopump_HR
    else
      inst_net_pow_wopump_HR+inst_net_pow_wopump;
  annotation (
    Icon(
      graphics={
        Rectangle(
          origin={3,-28},
          extent={{-91,116},{91,-116}},
          fillPattern=FillPattern.Solid,
          fillColor={80,227,194}),
        Text(
          textString="EN14511",
          origin={-4,-26},
          extent={{-36,-29},{36,29}})}));
end EN14511;
