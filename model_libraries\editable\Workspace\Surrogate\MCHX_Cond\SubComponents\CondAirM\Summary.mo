within Workspace.Surrogate.MCHX_Cond.SubComponents.CondAirM;
record Summary
  extends BOLT.InternalLibrary.HeatExchangers.RefAir.MCHX.SubComponents.summary_1slab;
  //  SI.TemperatureDifference delta_Tsat;
  //   import Modelica.SIunits;
  //
  //  extends Modelica.Icons.Record;
  //
  // Real   Itube annotation(Dialog);
  // Real FinCurve annotation(Dialog);
  // Real nTube "tube number, parallel tube number" annotation(Dialog); //(tube number, parallel tube number,#)
  // SIunits.Length pTube "Tube Pitch" annotation(Dialog);// (Tube Pitch,mm)
  // SIunits.Length lTube "Tube length" annotation(Dialog);// (Tube length, mm)
  // Real nRow "row number" annotation(Dialog);// (row number, #)
  // SIunits.Length pRow "Row Pitch" annotation(Dialog);// (Row Pitch,mm)
  // Real dFin "Fin Density" annotation(Dialog);//   (Fin Density #, Fins/25.4mm)
  // SIunits.Length tFin "Fin thickness" annotation(Dialog);// (Fin thickness,mm)
  // Real nCir "number of parallel circuits" annotation(Dialog);// (#)
  //
  // SIunits.HeatFlowRate Qdr "refrigerant side heat transfer rate, W" annotation(Dialog); // (refrigerant side heat transfer rate, W)
  // SIunits.HeatFlowRate Qda "sensible heat transfer rate, W" annotation(Dialog);// (sensible heat transfer rate, W)
  //
  // SIunits.Pressure dPr "refrigerant side pressure drop, Pa" annotation(Dialog);
  //                                                      // (refrigerant side pressure drop, Pa)
  // SIunits.Pressure dPa "air side pressure drop" annotation(Dialog);
  //                                          // (air side pressure drop)
  // SIunits.MassFlowRate m_flow_a_ref annotation(Dialog);
  // SIunits.MassFlowRate m_flow_b_ref annotation(Dialog);
  // SIunits.MassFlowRate m_flow_a_air annotation(Dialog);
  // SIunits.MassFlowRate m_flow_b_air annotation(Dialog);
  //
  // SIunits.Mass mass_ref "refrigerant charge mass" annotation(Dialog);
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false)),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)));
end Summary;
