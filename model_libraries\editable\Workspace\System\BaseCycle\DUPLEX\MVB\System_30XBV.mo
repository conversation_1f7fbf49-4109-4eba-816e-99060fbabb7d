within Workspace.System.BaseCycle.DUPLEX.MVB;
model System_30XBV
  extends.Workspace.System.BaseCycle.DUPLEX.Physical.System_30XBV(
    redeclare replaceable.Workspace.System.BaseCycle.Equipement_MVB ModuleA(Selector_comp_crkA_refInd = choiceBlock.Unit_ModuleA.selector_Comp_CKA_refInd,Selector_comp_crkB_refInd = choiceBlock.Unit_ModuleA.selector_Comp_CKB_refInd,
    
    
        NcompAMax =       
    
    if (choiceBlock.isMEPS_ME and choiceBlock.isCoatingOption) then
        if ModuleA.OAT < 38 + 273.15 then
            choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_coating_CKA
        elseif (ModuleA.OAT  >= 38+273.15 and ModuleA.OAT  <= 44 +273.15) then
            ((choiceBlock.Unit_ModuleA.NcompMax_coating_CKA - choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_coating_CKA)/(44-38))*(ModuleA.OAT -273.15) + (choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_coating_CKA - ((choiceBlock.Unit_ModuleA.NcompMax_coating_CKA - choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_coating_CKA)/(44-38)) *38)
        else 
            choiceBlock.Unit_ModuleA.NcompMax_coating_CKA
            
      elseif (choiceBlock.isMEPS_ME) then
    
        if ModuleA.OAT  < 38 + 273.15 then
            choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_CKA
        elseif (ModuleA.OAT  >= 38+273.15 and ModuleA.OAT  <= 44 +273.15) then
            ((choiceBlock.Unit_ModuleA.NcompMax_CKA - choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_CKA)/(44-38))*(ModuleA.OAT -273.15) + (choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_CKA - ((choiceBlock.Unit_ModuleA.NcompMax_CKA - choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_CKA)/(44-38)) *38)
        else 
            choiceBlock.Unit_ModuleA.NcompMax_CKA  
    
    
    
      elseif (choiceBlock.isCoatingOption)  then
        choiceBlock.Unit_ModuleA.NcompMax_coating_CKA
      else
        choiceBlock.Unit_ModuleA.NcompMax_CKA, 
     
    NcompBMax =       
    
    if (choiceBlock.isMEPS_ME and choiceBlock.isCoatingOption) then
        if ModuleA.OAT < 38 + 273.15 then
            choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_coating_CKB
        elseif (ModuleA.OAT >= 38+273.15 and ModuleA.OAT <= 44 +273.15) then
            ((choiceBlock.Unit_ModuleA.NcompMax_coating_CKB - choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_coating_CKB)/(44-38))*(ModuleA.OAT -273.15) + (choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_coating_CKB - ((choiceBlock.Unit_ModuleA.NcompMax_coating_CKB - choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_coating_CKB)/(44-38)) *38)
        else 
            choiceBlock.Unit_ModuleA.NcompMax_coating_CKB
            
   
      elseif (choiceBlock.isMEPS_ME) then
    
        if ModuleA.OAT < 38 + 273.15 then
            choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_CKB
        elseif (ModuleA.OAT >= 38+273.15 and ModuleA.OAT <= 44 +273.15) then
            ((choiceBlock.Unit_ModuleA.NcompMax_CKB - choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_CKB)/(44-38))*(ModuleA.OAT-273.15) + (choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_CKB - ((choiceBlock.Unit_ModuleA.NcompMax_CKB - choiceBlock.Unit_ModuleA.NcompMax_MEPS_ME_CKB)/(44-38)) *38)
        else 
            choiceBlock.Unit_ModuleA.NcompMax_CKB    
    
        
      elseif (choiceBlock.isCoatingOption)  then
        choiceBlock.Unit_ModuleA.NcompMax_coating_CKB
      else
        choiceBlock.Unit_ModuleA.NcompMax_CKB),
    redeclare replaceable.Workspace.System.BaseCycle.Equipement_MVB ModuleB(Selector_comp_crkA_refInd = choiceBlock.Unit_ModuleB.selector_Comp_CKA_refInd,Selector_comp_crkB_refInd = choiceBlock.Unit_ModuleB.selector_Comp_CKB_refInd,
        NcompAMax =       
    
    if (choiceBlock.isMEPS_ME and choiceBlock.isCoatingOption) then
        if ModuleB.OAT < 38 + 273.15 then
            choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_coating_CKA
        elseif (ModuleB.OAT  >= 38+273.15 and ModuleB.OAT  <= 44 +273.15) then
            ((choiceBlock.Unit_ModuleB.NcompMax_coating_CKA - choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_coating_CKA)/(44-38))*(ModuleB.OAT -273.15) + (choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_coating_CKA - ((choiceBlock.Unit_ModuleB.NcompMax_coating_CKA - choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_coating_CKA)/(44-38)) *38)
        else 
            choiceBlock.Unit_ModuleB.NcompMax_coating_CKA
            
      elseif (choiceBlock.isMEPS_ME) then
    
        if ModuleB.OAT  < 38 + 273.15 then
            choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_CKA
        elseif (ModuleB.OAT  >= 38+273.15 and ModuleB.OAT  <= 44 +273.15) then
            ((choiceBlock.Unit_ModuleB.NcompMax_CKA - choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_CKA)/(44-38))*(ModuleB.OAT -273.15) + (choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_CKA - ((choiceBlock.Unit_ModuleB.NcompMax_CKA - choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_CKA)/(44-38)) *38)
        else 
            choiceBlock.Unit_ModuleB.NcompMax_CKA  
    
    
    
      elseif (choiceBlock.isCoatingOption)  then
        choiceBlock.Unit_ModuleB.NcompMax_coating_CKA
      else
        choiceBlock.Unit_ModuleB.NcompMax_CKA, 
     
    NcompBMax =       
    
    if (choiceBlock.isMEPS_ME and choiceBlock.isCoatingOption) then
        if ModuleB.OAT < 38 + 273.15 then
            choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_coating_CKB
        elseif (ModuleB.OAT >= 38+273.15 and ModuleB.OAT <= 44 +273.15) then
            ((choiceBlock.Unit_ModuleB.NcompMax_coating_CKB - choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_coating_CKB)/(44-38))*(ModuleB.OAT -273.15) + (choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_coating_CKB - ((choiceBlock.Unit_ModuleB.NcompMax_coating_CKB - choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_coating_CKB)/(44-38)) *38)
        else 
            choiceBlock.Unit_ModuleB.NcompMax_coating_CKB
            
   
      elseif (choiceBlock.isMEPS_ME) then
    
        if ModuleB.OAT < 38 + 273.15 then
            choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_CKB
        elseif (ModuleB.OAT >= 38+273.15 and ModuleB.OAT <= 44 +273.15) then
            ((choiceBlock.Unit_ModuleB.NcompMax_CKB - choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_CKB)/(44-38))*(ModuleB.OAT-273.15) + (choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_CKB - ((choiceBlock.Unit_ModuleB.NcompMax_CKB - choiceBlock.Unit_ModuleB.NcompMax_MEPS_ME_CKB)/(44-38)) *38)
        else 
            choiceBlock.Unit_ModuleB.NcompMax_CKB    
    
        
      elseif (choiceBlock.isCoatingOption)  then
        choiceBlock.Unit_ModuleB.NcompMax_coating_CKB
      else
        choiceBlock.Unit_ModuleB.NcompMax_CKB),
    redeclare Workspace.Auxiliary.OptionBlock.ChoiceBlock_XBV_Duplex_R513A.ChoiceBlock choiceBlock,controllerSettings_moduleA_crkA(dT_sbc_max = if controllerSettings_moduleA_crkA.LWT_setpoint > 9.5 + 273.15 then 0.5593 * (controllerSettings_moduleA_crkA.LWT_setpoint - 273.15) + 5.17 else 8,LWT_setpoint = if use_ECATBlock then ECAT.EvapBrineLWT_K.setPoint else LWT),controllerSettings_moduleA_crkB(dT_sbc_max = if controllerSettings_moduleA_crkB.LWT_setpoint > 9.5 + 273.15 then 0.5593 * (controllerSettings_moduleA_crkB.LWT_setpoint - 273.15) + 5.17 else 8,LWT_setpoint = if use_ECATBlock then ECAT.EvapBrineLWT_K.setPoint else LWT),controllerSettings_moduleB_crkA(dT_sbc_max = if controllerSettings_moduleB_crkA.LWT_setpoint > 9.5 + 273.15 then 0.5593 * (controllerSettings_moduleB_crkA.LWT_setpoint - 273.15) + 5.17 else 8,LWT_setpoint = if use_ECATBlock then ECAT.EvapBrineLWT_K.setPoint else LWT),controllerSettings_moduleB_crkB(dT_sbc_max = if controllerSettings_moduleB_crkB.LWT_setpoint > 9.5 + 273.15 then 0.5593 * (controllerSettings_moduleB_crkB.LWT_setpoint - 273.15) + 5.17 else 8,LWT_setpoint = if use_ECATBlock then ECAT.EvapBrineLWT_K.setPoint else LWT));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false,
        extent={{-100.0,-100.0},{100.0,100.0}}),
      graphics={
        Rectangle(
          fillColor={245,166,35},
          fillPattern=FillPattern.Solid,
          extent={{-100.0,-100.0},{100.0,100.0}}),
        Text(
          lineColor={0,0,255},
          extent={{-150,150},{150,110}},
          textString="%name")}));
end System_30XBV;
