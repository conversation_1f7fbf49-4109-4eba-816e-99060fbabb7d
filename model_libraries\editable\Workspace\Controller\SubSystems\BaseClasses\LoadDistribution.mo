within KAV_initiation.System30KAV.Controller30KAV.SubSystems.BaseClasses;
model LoadDistribution
  "Computes load distribution for both compressors"
  parameter Real crkA_min_speed
    "Minimum speed of compressor A (Hz)";
  parameter Real crkB_min_speed
    "Minimum speed of compressor B (Hz)";
  parameter Real crkA_max_speed
    "Maximum speed of compressor A (Hz)";
  parameter Real crkB_max_speed
    "Maximum speed of compressor B (Hz)";
  .Modelica.Blocks.Interfaces.RealInput normalized_total_load
    annotation (Placement(transformation(extent={{-76.64857109674557,-16.64857109674557},{-55.35142890325443,4.64857109674557}},origin={-34,6},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput crkB_speed
    annotation (Placement(transformation(extent={{64.0,-52.0},{84.0,-32.0}},origin={26,-18},rotation=0.0)));
  .Modelica.Blocks.Math.Add addA
    annotation (Placement(transformation(extent={{20.0,26.0},{40.0,46.0}},origin={20,24},rotation=0.0)));
  .Modelica.Blocks.Math.Gain gainA(
    k=crkA_max_speed-crkA_min_speed)
    annotation (Placement(transformation(extent={{-9.68508214154221,28.31491785845779},{13.68508214154221,51.68508214154221}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Math.Gain gainB(
    k=crkB_max_speed-crkB_min_speed)
    annotation (Placement(transformation(extent={{-29.68508214154221,-47.68508214154221},{-6.31491785845779,-24.31491785845779}},origin={18,-4},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression crkA_min(
    y=crkA_min_speed)
    annotation (Placement(transformation(extent={{-26.0,44.0},{-6.0,64.0}},origin={16,12},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression crkB_min(
    y=crkB_min_speed)
    annotation (Placement(transformation(extent={{-30.0,-78.0},{-10.0,-58.0}},origin={20,2},rotation=0.0)));
  .Modelica.Blocks.Math.Add addB
    annotation (Placement(transformation(extent={{20.0,-52.0},{40.0,-32.0}},origin={20,-18},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput crkA_speed
    annotation (Placement(transformation(extent={{62.0,26.0},{82.0,46.0}},origin={28,24},rotation=0.0)));
equation
  connect(crkA_min.y,addA.u1)
    annotation (Line(points={{11,66},{38,66}},color={0,0,127}));
  connect(gainA.u,normalized_total_load)
    annotation (Line(points={{-12.022098569850654,40},{-45.011,40},{-45.011,0},{-100,0}},color={0,0,127}));
  connect(gainA.y,addA.u2)
    annotation (Line(points={{14.853590355696433,40},{20,40},{20,54},{38,54}},color={0,0,127}));
  connect(addB.y,crkB_speed)
    annotation (Line(points={{61,-60},{100,-60}},color={0,0,127}));
  connect(crkB_min.y,addB.u2)
    annotation (Line(points={{11,-66},{38,-66}},color={0,0,127}));
  connect(normalized_total_load,gainB.u)
    annotation (Line(points={{-100,0},{-45.011,0},{-45.011,-40},{-14.0221,-40}},color={0,0,127}));
  connect(gainB.y,addB.u1)
    annotation (Line(points={{12.8536,-40},{20,-40},{20,-54},{38,-54}},color={0,0,127}));
  connect(addA.y,crkA_speed)
    annotation (Line(points={{61,60},{100,60}},color={0,0,127}));
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=true,
        extent={{-100,-100},{100,100}}),
      graphics={
        Line(
          points={{100,60},{60,60},{0,0}},
          color={0,0,127}),
        Line(
          points={{100,-60},{60,-60},{0,0}},
          color={0,0,127}),
        Line(
          points={{-100,0},{0,0}},
          color={0,0,127}),
        Rectangle(
          extent={{-40,40},{40,-40}},
          lineColor={0,0,0},
          fillColor={235,235,235},
          fillPattern=FillPattern.Solid),
        Text(
          extent={{-100,140},{100,100}},
          lineColor={28,108,200},
          textString="%name")}));
end LoadDistribution;
