within KAV_initiation.System30KAV.Controller30KAV.SubSystems;
model EXVControl
  extends.Workspace.Controller.SubSystems.BaseClasses.EXVControlBase
    annotation (Icon(coordinateSystem(preserveAspectRatio=false)),Diagram(coordinateSystem(preserveAspectRatio=false)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController(
    AV_min=0.05,
    AV_max=1,
    AV_start=0.6,
    isOff=crkIsOff)
    annotation (Placement(transformation(extent={{66.0,-10.0},{86.0,10.0}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_sst_min_error(
    ID=2,
    measurement=T_sst,
    setPoint=T_sst_min_limit_exv,
    gain=1/273.65,
    isOff=isOffSSTmin)
    annotation (Placement(transformation(extent={{-101.09841898566239,8.028301170601612},{-58.90158101433761,27.971698829398388}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_sst_max_error(
    ID=3,
    measurement=T_sst,
    setPoint=T_sst_max_limit,
    gain=1/((295.15)),
    isOff=isOffSSTmax)
    annotation (Placement(transformation(extent={{-100.67753538407626,-7.993518034050355},{-59.32246461592374,11.993518034050355}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation dT_dsh_min_error(
    ID=4,
    measurement=dT_dsh,
    gain=-1/7,
    setPoint=dT_dsh_min_limit,
    isOff=isOffDSHmin)
    annotation (Placement(transformation(extent={{-100.30882511433632,-23.993518034050357},{-59.691174885663685,-4.006481965949643}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation T_dgt_max_error(
    measurement=T_dgt,
    gain=-1/367.15,
    ID=5,
    setPoint=T_dgt_max_limit_exv,
    isOff=isOffDGTmax)
    annotation (Placement(transformation(extent={{-100.02904159316385,-39.99351803405035},{-59.97095840683615,-20.00648196594965}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.Max max
    annotation (Placement(transformation(extent={{-45.782256298685816,24.217743701314184},{-34.217743701314184,35.782256298685816}},origin={0,0},rotation=0)));
  .BOLT.Control.SteadyState.SetpointControl.Max max1
    annotation (Placement(transformation(extent={{34.918658000099356,10.918658000099349},{49.081341999900644,25.08134199990065}},origin={0,0},rotation=0)));
  .BOLT.Control.SteadyState.SetpointControl.Min min1
    annotation (Placement(transformation(extent={{6.843901550625681,14.843901550625684},{21.15609844937432,29.156098449374316}},origin={0,0},rotation=0)));
  parameter Boolean isOffSSTmin;
  parameter Boolean isOffSSTmax;
  parameter Boolean isOffDSHmin;
  parameter Boolean isOffDGTmax;
  parameter Boolean ov_sbc_IsOn=false
    "Override subcooling setpoint to manually enter it";
  parameter Real ov_sbc_setpoint=-5
    "manual subcooling setpoint";
  parameter Real comp_max_speed;
  parameter Real MaxFanFrequency;
  .Modelica.Blocks.Interfaces.RealOutput rel_cooler_level_setpoint
    "Discharge gas temperature max limit"
    annotation (Placement(transformation(extent={{-86.49941158124145,-164.49941158124145},{-61.500588418758554,-139.50058841875855}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput rel_cooler_level
    "Subcooling"
    annotation (Placement(transformation(extent={{-82.49941158124145,119.50058841875855},{-57.500588418758554,144.49941158124145}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.ErrorCalculation dT_sbc_error(
    setPoint=setpoint.y,
    ID=1,
    gain=0.2,
    measurement=dT_sbc)
    annotation (Placement(transformation(extent={{-101.09841898566239,26.028301170601623},{-58.90158101433761,45.97169882939838}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Logical.Switch setpoint
    annotation (Placement(transformation(extent={{-154.68023296962645,3.3197670303735585},{-141.31976703037355,16.68023296962644}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression dT_sbc_setpoint_block(
    y=
      if ov_sbc_IsOn then
        ov_sbc_setpoint
      else
        dT_sbc_setpoint)
    annotation (Placement(transformation(extent={{-193.6719429544964,-3.671942954496391},{-182.3280570455036,7.671942954496391}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.BooleanExpression law_noSC(
    y=MaxFanFrequency < 55 and(sdt > SDT_max-0.2) and oat >(52+273.15))
    annotation (Placement(transformation(extent={{-193.71742545828488,4.282574541715128},{-182.28257454171512,15.717425458284872}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression noSC_whenEVXtooOpen(
    y=-0.1)
    annotation (Placement(transformation(extent={{-193.6719429544964,12.328057045503613},{-182.3280570455036,23.671942954496387}},origin={0.0,0.0},rotation=0.0)));
  .BOLT.Control.SteadyState.SetpointControl.SetpointController setpointController_sst(
    isOff=crkIsOff,
    AV_start=1,
    AV_max=1,
    AV_min=0)
    annotation (Placement(transformation(extent={{-26.0,-66.0},{-6.0,-46.0}},origin={0.0,0.0},rotation=0.0)));
  parameter Real offset_limit_sst_max=0;
  .Modelica.Blocks.Logical.Switch sstMaxActivation
    annotation (Placement(transformation(extent={{67.31976703037355,-52.680232969626445},{80.68023296962645,-39.319767030373555}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.BooleanExpression SSTmax_isOff(
    y=isOffSSTmax)
    annotation (Placement(transformation(extent={{40.28257454171512,-51.71742545828487},{51.71742545828488,-40.28257454171513}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Sources.RealExpression SSTmax_isOff_value(
    y=1)
    annotation (Placement(transformation(extent={{40.32805704550361,-41.67194295449639},{51.67194295449639,-30.328057045503613}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput sdt
    "Subcooling"
    annotation (Placement(transformation(extent={{-84.49941158124145,141.50058841875855},{-59.500588418758554,166.49941158124145}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput oat
    "Subcooling"
    annotation (Placement(transformation(extent={{-84.49941158124145,161.50058841875855},{-59.500588418758554,186.49941158124145}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput SDT_max
    "Discharge gas temperature max limit"
    annotation (Placement(transformation(extent={{-86.49941158124145,-188.49941158124145},{-61.500588418758554,-163.50058841875855}},origin={0.0,0.0},rotation=0.0)));
protected
  .Modelica.Blocks.Interfaces.RealOutput T_sst
    "Suction saturated temperature"
    annotation (Placement(transformation(extent={{-82.49941158124145,81.50058841875855},{-57.500588418758554,106.49941158124145}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Interfaces.RealOutput dT_dsh
    "Discharge superheat"
    annotation (Placement(transformation(extent={{-82.49941158124145,63.500588418758554},{-57.500588418758554,88.49941158124145}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Interfaces.RealOutput T_dgt
    "Discharge gas temperature"
    annotation (Placement(transformation(extent={{-82.49941158124145,45.500588418758554},{-57.500588418758554,70.49941158124145}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Interfaces.RealOutput dT_sbc
    "Subcooling"
    annotation (Placement(transformation(extent={{-82.49941158124145,100.50058841875855},{-57.500588418758554,125.49941158124145}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Interfaces.RealOutput T_sst_min_limit_exv
    "Suction saturated temperature min limit"
    annotation (Placement(transformation(extent={{-86.49941158124145,-90.49941158124145},{-61.500588418758554,-65.50058841875855}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Interfaces.RealOutput dT_dsh_min_limit
    "Discharge superheat min limit"
    annotation (Placement(transformation(extent={{-86.49941158124145,-126.49941158124145},{-61.500588418758554,-101.50058841875855}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput T_sst_max_limit
    "Suction saturated temperature max limit"
    annotation (Placement(transformation(extent={{-86.49941158124145,-108.49941158124145},{-61.500588418758554,-83.50058841875855}},origin={0.0,0.0},rotation=0.0)));
  .Modelica.Blocks.Interfaces.RealOutput dT_sbc_setpoint
    "Subcooling setpoint"
    annotation (Placement(transformation(extent={{-86.49941158124145,-72.49941158124145},{-61.500588418758554,-47.500588418758554}},origin={0,0},rotation=0)));
  .Modelica.Blocks.Interfaces.RealOutput T_dgt_max_limit_exv
    "Discharge gas temperature max limit"
    annotation (Placement(transformation(extent={{-86.49941158124145,-144.49941158124145},{-61.500588418758554,-119.50058841875855}},origin={0.0,0.0},rotation=0.0)));
equation
  connect(setpointController.actuatorSignal,actuatorSignal)
    annotation (Line(points={{86.6,0},{100,0}},color={28,108,200}));
  connect(T_sst,measurementBus.T_sst)
    annotation (Line(points={{-70,94},{-99.9,94},{-99.9,60.1}},color={0,0,127}));
  connect(dT_dsh,measurementBus.dT_dsh)
    annotation (Line(points={{-70,76},{-99.9,76},{-99.9,60.1}},color={0,0,127}));
  connect(T_dgt,measurementBus.T_dgt)
    annotation (Line(points={{-70,58},{-99.9,58},{-99.9,60.1}},color={0,0,127}));
  connect(dT_sbc,measurementBus.dT_sbc)
    annotation (Line(points={{-70,113},{-99.9,113},{-99.9,60.1}},color={0,0,127}));
  connect(min1.y,max1.u1)
    annotation (Line(points={{21.15609844937432,22},{28.037378224736837,22},{28.037378224736837,21.965551519944366},{34.918658000099356,21.965551519944366}},color={28,108,200}));
  connect(max1.y,setpointController.errorSignal)
    annotation (Line(points={{49.081341999900644,18},{57.54067099995032,18},{57.54067099995032,0},{66,0}},color={28,108,200}));
  connect(T_sst_max_limit,limitsBus.T_sst_max_limit)
    annotation (Line(points={{-74,-96},{-99.95,-96},{-99.95,-59.95}},color={0,0,127}));
  connect(T_sst_min_error.sensor,max.u2)
    annotation (Line(points={{-59.32354939405086,17.20226409364813},{-50,17.20226409364813},{-50,26.530646220788512},{-45.782256298685816,26.530646220788512}},color={28,108,200}));
  connect(min1.u2,dT_dsh_min_error.sensor)
    annotation (Line(points={{6.843901550625681,17.70634093037541},{0,17.70634093037541},{0,-14.799481442724028},{-60.09735138795041,-14.799481442724028}},color={28,108,200}));
  connect(T_dgt_max_error.sensor,max1.u2)
    annotation (Line(points={{-60.37153923869942,-30.799481442724034},{28,-30.799481442724034},{28,13.751194800059608},{34.918658000099356,13.751194800059608}},color={28,108,200}));
  connect(dT_sbc_setpoint,limitsBus.dT_sbc_setpoint)
    annotation (Line(points={{-74,-60},{-86,-60},{-86,-59.95},{-99.95,-59.95}},color={0,0,127}));
  connect(dT_dsh_min_limit,limitsBus.dT_dsh_min_limt)
    annotation (Line(points={{-74,-114},{-99.95,-114},{-99.95,-59.95}},color={0,0,127}));
  connect(T_sst_min_limit_exv,limitsBus.T_sst_min_limit_exv)
    annotation (Line(points={{-74,-78},{-99.95,-78},{-99.95,-59.95}},color={0,0,127}));
  connect(T_dgt_max_limit_exv,limitsBus.T_dgt_max_limit_exv)
    annotation (Line(points={{-74,-132},{-99.95,-132},{-99.95,-59.95}},color={0,0,127}));
  connect(rel_cooler_level_setpoint,limitsBus.rel_cooler_level_setpoint)
    annotation (Line(points={{-74,-152},{-99.95,-152},{-99.95,-59.95}},color={0,0,127}));
  connect(rel_cooler_level,measurementBus.rel_cooler_level)
    annotation (Line(points={{-70,132},{-99.9,132},{-99.9,60.1}},color={0,0,127}));
  connect(dT_sbc_setpoint_block.y,setpoint.u3)
    annotation (Line(points={{-181.76086275005397,2},{-176.3280570455036,2},{-176.3280570455036,4.655813624298846},{-156.01627956355173,4.655813624298846}},color={0,0,127}));
  connect(dT_sbc_error.sensor,max.u1)
    annotation (Line(points={{-59.32354939405086,35.20226409364813},{-52.55290284636834,35.20226409364813},{-52.55290284636834,33.23806352726406},{-45.782256298685816,33.23806352726406}},color={28,108,200}));
  connect(max.y,min1.u1)
    annotation (Line(points={{-34.217743701314184,30},{-13.686921075344252,30},{-13.686921075344252,26.007415131649616},{6.843901550625681,26.007415131649616}},color={28,108,200}));
  connect(T_sst_max_error.sensor,setpointController_sst.errorSignal)
    annotation (Line(points={{-59.73601532360526,1.2005185572759716},{-36,1.2005185572759716},{-36,-56},{-26,-56}},color={28,108,200}));
  connect(setpointController_sst.actuatorSignal,sstMaxActivation.u3)
    annotation (Line(points={{-5.399999999999999,-56},{52,-56},{52,-51.344186375701156},{65.98372043644827,-51.344186375701156}},color={0,0,127}));
  connect(SSTmax_isOff.y,sstMaxActivation.u2)
    annotation (Line(points={{52.289168004113364,-46},{65.98372043644827,-46}},color={255,0,255}));
  connect(SSTmax_isOff_value.y,sstMaxActivation.u1)
    annotation (Line(points={{52.239137249946026,-36},{60,-36},{60,-40.655813624298844},{65.98372043644827,-40.655813624298844}},color={0,0,127}));
  connect(sstMaxActivation.y,actuatorSignal_sst)
    annotation (Line(points={{81.34825626658909,-46},{90.67412813329454,-46},{90.67412813329454,-26},{100,-26}},color={0,0,127}));
  connect(sdt,measurementBus.T_sdt)
    annotation (Line(points={{-72,154},{-100,154},{-100,60}},color={0,0,127}));
  connect(law_noSC.y,setpoint.u2)
    annotation (Line(points={{-181.71083199588662,10},{-156.01627956355173,10}},color={255,0,255}));
  connect(noSC_whenEVXtooOpen.y,setpoint.u1)
    annotation (Line(points={{-181.76086275005397,18},{-156.01627956355173,18},{-156.01627956355173,15.344186375701154}},color={0,0,127}));
  connect(SDT_max,limitsBus.T_sdt_max_limit_comp)
    annotation (Line(points={{-74,-176},{-100,-176},{-100,-60}},color={0,0,127}));
  connect(oat,measurementBus.T_oat)
    annotation (Line(points={{-72,174},{-100,174},{-100,60}},color={0,0,127}));
end EXVControl;
