within Workspace.Auxiliary;
model Altitude_to_Pressure
  "Convert altitude to air pressure"
  parameter.Modelica.SIunits.Height altitude=0;
  final parameter Real beta=-0.0065;
  final parameter Real g0=9.80665;
  final parameter Real M0=28.96442;
  final parameter Real Rstar=8314.32;
  //final parameter Modelica.SIunits.AbsolutePressure p = 101325*(1-2.25577*1e-5*altitude)^5.2559
  final parameter.Modelica.SIunits.AbsolutePressure p=101325*(288.15/(288.15+beta*altitude))^(g0*M0/beta/Rstar)
    "Equation for barometric pressure as a function of altitude, ASHARE Handbook_Fundamentals_2013, page 8, Pa";
  annotation (
    Icon(
      coordinateSystem(
        preserveAspectRatio=false),
      graphics={
        Rectangle(
          extent={{-100,100},{100,-100}},
          lineColor={28,108,200},
          fillColor={85,170,255},
          fillPattern=FillPattern.Solid),
        Text(
          extent={{-72,62},{70,-60}},
          lineColor={0,0,0},
          fillColor={85,170,255},
          fillPattern=FillPattern.None,
          textString="Altitude_to_Pressure")}),
    Diagram(
      coordinateSystem(
        preserveAspectRatio=false)));
end Altitude_to_Pressure;
